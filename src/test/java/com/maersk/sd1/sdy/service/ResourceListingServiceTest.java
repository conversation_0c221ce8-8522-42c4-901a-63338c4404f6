package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.Resource;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.model.WorkPoint;
import com.maersk.sd1.common.model.WorkQueue;
import com.maersk.sd1.common.model.WorkQueueResource;
import com.maersk.sd1.common.repository.ResourceRepository;
import com.maersk.sd1.common.repository.WorkQueueResourceRepository;
import com.maersk.sd1.sdy.dto.ResourceListInput;
import com.maersk.sd1.sdy.dto.ResourceListOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;


import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class ResourceListingServiceTest {

    @Mock
    private ResourceRepository resourceRepository;

    @Mock
    private WorkQueueResourceRepository workQueueResourceRepository;

    @InjectMocks
    private ResourceListingService resourceListingService;

    private Resource resource1;
    private Resource resource2;
    private WorkQueueResource workQueueResource;
    private List<Resource> resources;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // Create test data
        Catalog catResource = new Catalog();
        catResource.setId(1);
        catResource.setDescription("Test Resource Category");

        Catalog catContainerLoad = new Catalog();
        catContainerLoad.setId(2);
        catContainerLoad.setDescription("Test Container Load Category");

        WorkPoint workPoint = new WorkPoint();
        workPoint.setId(1);
        workPoint.setCode("WP001");
        workPoint.setDescription("Test Work Point");

        User user = new User();
        user.setId(1);
        user.setNames("John");
        user.setFirstLastName("Doe");
        user.setSecondLastName("Smith");

        resource1 = new Resource();
        resource1.setId(1);
        resource1.setCatResource(catResource);
        resource1.setCatContainerLoad(catContainerLoad);
        resource1.setCode("RES001");
        resource1.setName("Resource 1");
        resource1.setBrand("Brand 1");
        resource1.setMaximumLevelStacking(3);
        resource1.setActive(true);
        resource1.setRegistrationDate(LocalDateTime.now().minusDays(10));
        resource1.setModificationDate(LocalDateTime.now().minusDays(5));
        resource1.setWorkPoint(workPoint);
        resource1.setRegistrationUser(user);
        resource1.setModificationUser(user);

        resource2 = new Resource();
        resource2.setId(2);
        resource2.setCatResource(catResource);
        resource2.setCatContainerLoad(catContainerLoad);
        resource2.setCode("RES002");
        resource2.setName("Resource 2");
        resource2.setBrand("Brand 2");
        resource2.setMaximumLevelStacking(4);
        resource2.setActive(true);
        resource2.setRegistrationDate(LocalDateTime.now().minusDays(8));
        resource2.setModificationDate(LocalDateTime.now().minusDays(3));
        resource2.setWorkPoint(workPoint);
        resource2.setRegistrationUser(user);
        resource2.setModificationUser(user);

        resources = Arrays.asList(resource1, resource2);

        WorkQueue workQueue = new WorkQueue();
        workQueue.setId(1);
        workQueue.setCode("WQ001");
        workQueue.setDescription("Test Work Queue");

        workQueueResource = new WorkQueueResource();
        workQueueResource.setId(1);
        workQueueResource.setResource(resource1);
        workQueueResource.setWorkQueue(workQueue);
    }

    @Test
    void testListResources_Success() {
        // Arrange
        ResourceListInput.Input input = new ResourceListInput.Input();
        input.setPage(1);
        input.setSize(10);

        ResourceListInput.Prefix prefix = new ResourceListInput.Prefix();
        prefix.setInput(input);

        ResourceListInput.Root root = new ResourceListInput.Root();
        root.setPrefix(prefix);

        Page<Resource> page = new PageImpl<>(resources);
        when(resourceRepository.findResourcesWithFilters(
            any(), any(), any(), any(), any(), any(), any(), any(),
            any(), any(), any(), any(), any(), any(), any(Pageable.class)
        )).thenReturn(page);
        when(workQueueResourceRepository.findByResource_Id(1)).thenReturn(Arrays.asList(workQueueResource));
        when(workQueueResourceRepository.findByResource_Id(2)).thenReturn(new ArrayList<>());

        // Act
        ResourceListOutput output = resourceListingService.listResources(root);

        // Assert
        assertNotNull(output);
        assertNotNull(output.getTotal());
        assertEquals(2, output.getTotal().get(0).get(0));
        assertNotNull(output.getRecursos());
        assertEquals(2, output.getRecursos().size());

        // Check first resource
        ResourceListOutput.ResourceData resourceData1 = output.getRecursos().get(0);
        assertEquals(1, resourceData1.getRecursoId());
        assertEquals("RES001", resourceData1.getCodigo());
        assertEquals("Resource 1", resourceData1.getNombre());
        assertEquals("Brand 1", resourceData1.getMarca());
        assertEquals(3, resourceData1.getMaximoNivelApilamiento());
        assertEquals('1', resourceData1.getActivo());
        assertEquals(1, resourceData1.getPuntoTrabajoId());
        assertEquals("WP001", resourceData1.getPuntoTrabajo());
        assertEquals("John", resourceData1.getUsuarioRegistroNombres());
        assertEquals("Doe Smith", resourceData1.getUsuarioRegistroApellidos());

        // Check work queues
        assertNotNull(resourceData1.getColasTrabajo());

        // Check second resource
        ResourceListOutput.ResourceData resourceData2 = output.getRecursos().get(1);
        assertEquals(2, resourceData2.getRecursoId());
        assertEquals("RES002", resourceData2.getCodigo());
    }

    @Test
    void testListResources_EmptyResult() {
        // Arrange
        ResourceListInput.Input input = new ResourceListInput.Input();
        input.setPage(1);
        input.setSize(10);
        input.setRecursoId(999); // Non-existent ID

        ResourceListInput.Prefix prefix = new ResourceListInput.Prefix();
        prefix.setInput(input);

        ResourceListInput.Root root = new ResourceListInput.Root();
        root.setPrefix(prefix);

        Page<Resource> emptyPage = new PageImpl<>(Collections.emptyList());
        when(resourceRepository.findResourcesWithFilters(
            any(), any(), any(), any(), any(), any(), any(), any(),
            any(), any(), any(), any(), any(), any(), any(Pageable.class)
        )).thenReturn(emptyPage);

        // Act
        ResourceListOutput output = resourceListingService.listResources(root);

        // Assert
        assertNotNull(output);
        assertNotNull(output.getTotal());
        assertEquals(0, output.getTotal().get(0).get(0));
        assertNotNull(output.getRecursos());
        assertEquals(0, output.getRecursos().size());
    }
}
