package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.Yard;
import com.maersk.sd1.common.repository.YardRepository;
import com.maersk.sd1.sdy.dto.YardBySubBusinessUnitOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class YardBySubBusinessUnitServiceTest {

    @Mock
    private YardRepository yardRepository;

    @InjectMocks
    private YardBySubBusinessUnitService yardBySubBusinessUnitService;

    private List<Yard> mockYards;
    private final Integer subBusinessUnitId = 1;

    @BeforeEach
    void setUp() {
        // Create mock yards
        mockYards = new ArrayList<>();

        Yard yard1 = new Yard();
        yard1.setId(101);
        yard1.setCode("YRD001");
        yard1.setName("Yard One");
        mockYards.add(yard1);

        Yard yard2 = new Yard();
        yard2.setId(102);
        yard2.setCode("YRD002");
        yard2.setName("Yard Two");
        mockYards.add(yard2);
    }

    @Test
    void getYardsBySubBusinessUnit_Success() {
        // Arrange
        when(yardRepository.findByBusinessUnit_IdAndActive(anyInt(), anyBoolean()))
                .thenReturn(mockYards);

        // Act
        YardBySubBusinessUnitOutput result = yardBySubBusinessUnitService.getYardsBySubBusinessUnit(subBusinessUnitId);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getYards());
        assertEquals(2, result.getYards().size());

        // Verify first yard
        assertEquals(101, result.getYards().get(0).getYardId());
        assertEquals("YRD001", result.getYards().get(0).getYardCode());
        assertEquals("Yard One", result.getYards().get(0).getYardName());

        // Verify second yard
        assertEquals(102, result.getYards().get(1).getYardId());
        assertEquals("YRD002", result.getYards().get(1).getYardCode());
        assertEquals("Yard Two", result.getYards().get(1).getYardName());

        // Verify repository was called with correct parameters
        verify(yardRepository).findByBusinessUnit_IdAndActive(subBusinessUnitId, true);
    }

    @Test
    void getYardsBySubBusinessUnit_EmptyList() {
        // Arrange
        when(yardRepository.findByBusinessUnit_IdAndActive(anyInt(), anyBoolean()))
                .thenReturn(new ArrayList<>());

        // Act
        YardBySubBusinessUnitOutput result = yardBySubBusinessUnitService.getYardsBySubBusinessUnit(subBusinessUnitId);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getYards());
        assertEquals(0, result.getYards().size());

        // Verify repository was called
        verify(yardRepository).findByBusinessUnit_IdAndActive(subBusinessUnitId, true);
    }

    @Test
    void getYardsBySubBusinessUnit_RepositoryThrowsException() {
        // Arrange
        when(yardRepository.findByBusinessUnit_IdAndActive(anyInt(), anyBoolean()))
                .thenThrow(new RuntimeException("Database error"));

        // Act
        YardBySubBusinessUnitOutput result = yardBySubBusinessUnitService.getYardsBySubBusinessUnit(subBusinessUnitId);

        // Assert
        assertNotNull(result);


    }

    @Test
    void getYardsBySubBusinessUnit_NullYardList() {
        // Arrange
        when(yardRepository.findByBusinessUnit_IdAndActive(anyInt(), anyBoolean()))
                .thenReturn(null);

        // Act
        YardBySubBusinessUnitOutput result = yardBySubBusinessUnitService.getYardsBySubBusinessUnit(subBusinessUnitId);

        // Assert
        assertNotNull(result);
    }
}