package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.MovementInstructionRepository;
import com.maersk.sd1.sdy.dto.MovementInstructionExecutedEirOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class MovementInstructionExecutedEirServiceTest {

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private MovementInstructionRepository movementInstructionRepository;

    @InjectMocks
    private MovementInstructionExecutedEirService service;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void given_ValidInputs_getExecutedMovementInstructions_ThenReturnSuccess() {
        // Given
        int eirId = 1;
        int containerId = 2;
        int yardId = 3;

        Catalog gateInOperationTypeCat = new Catalog();
        gateInOperationTypeCat.setId(100);
        Catalog executedStateCat = new Catalog();
        executedStateCat.setId(200);

        List<Integer> expectedIds = List.of(10, 20, 30);

        when(catalogRepository.findByAlias("42904")).thenReturn(gateInOperationTypeCat);
        when(catalogRepository.findByAlias("42896")).thenReturn(executedStateCat);
        when(movementInstructionRepository.findExecutedGateInInstructions(
                eirId, containerId, yardId, 200, 100))
                .thenReturn(expectedIds);

        // When
        MovementInstructionExecutedEirOutput result = service.getExecutedMovementInstructions(eirId, containerId, yardId);

        // Then
        assertNotNull(result);
        assertEquals(expectedIds, result.getMovementInstructionIds());
    }

    @Test
    void given_MissingCatalogs_getExecutedMovementInstructions_ThenReturnEmptyList() {
        // Given
        when(catalogRepository.findByAlias("42904")).thenReturn(null); // gateInOperationTypeCat missing
        when(catalogRepository.findByAlias("42896")).thenReturn(new Catalog());

        // When
        MovementInstructionExecutedEirOutput result = service.getExecutedMovementInstructions(1, 2, 3);

        // Then
        assertNotNull(result);
        assertNotNull(result.getMovementInstructionIds());
        assertTrue(result.getMovementInstructionIds().isEmpty());
        verify(movementInstructionRepository, never()).findExecutedGateInInstructions(any(), any(), any(), any(), any());
    }

    @Test
    void given_InvalidInputs_getExecutedMovementInstructions_ThenReturnException() {
        // Given
        when(catalogRepository.findByAlias("42904")).thenThrow(new RuntimeException("Database error"));

        // When
        MovementInstructionExecutedEirOutput result = service.getExecutedMovementInstructions(1, 2, 3);

        // Then
        assertNotNull(result);
        assertNotNull(result.getMovementInstructionIds());
        assertTrue(result.getMovementInstructionIds().isEmpty());
    }
}

