package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.repository.StockFullRepository;
import com.maersk.sd1.sdy.dto.TempContainerDTO;
import com.maersk.sd1.sdy.dto.ValidateMassiveContainerRemovementOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class ValidateMassiveContainerRemovementServiceTest {

    @Mock
    private StockFullRepository stockFullRepository;

    @InjectMocks
    private ValidateMassiveContainerRemovementService service;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void Given_ValidSubBusinessUnitIdAndContainerNumbers_When_ContainersExist_Then_ReturnValidOutput() {
        // Arrange
        Integer subBusinessUnitLocalId = 1;
        Integer languageId = 1;
        List<String> containerNumbers = List.of("CONT123", "CONT456");

        when(stockFullRepository.findYardIdByBusinessUnitId(subBusinessUnitLocalId)).thenReturn(100);
        when(stockFullRepository.findContainerByNumber("CONT123"))
                .thenReturn(Collections.singletonList(new Object[]{1, "CONT123"}));
        when(stockFullRepository.findContainerByNumber("CONT456"))
                .thenReturn(Collections.singletonList(new Object[]{1, "CONT456"}));
        when(stockFullRepository.findContainerLocationJson(eq(1), eq(100))).thenReturn("Location1");
        when(stockFullRepository.findContainerLocationJson(eq(2), eq(100))).thenReturn("Location2");
        when(stockFullRepository.findEirIdByContainerId(eq(1))).thenReturn(10);
        when(stockFullRepository.findEirIdByContainerId(eq(2))).thenReturn(null);

        // Act
        List<ValidateMassiveContainerRemovementOutput> result = service.validateMassiveContainerRemovement(
                subBusinessUnitLocalId, languageId, containerNumbers);

        // Assert
        assertEquals(2, result.size());
        assertEquals("CONT123", result.get(0).getContainerNumber());
        assertEquals("Location1", result.get(0).getContainerCurrentLocation());
        assertEquals(10, result.get(0).getContainerGateoutEir());
        assertEquals(false, result.get(0).getInvalidContainer());
        assertEquals(false, result.get(0).getOutsideDepot());
        assertEquals(false, result.get(0).getInStock());

        assertEquals("CONT456", result.get(1).getContainerNumber());
        assertEquals("Location1", result.get(1).getContainerCurrentLocation());
    }

    @Test
    void Given_ValidSubBusinessUnitIdAndContainerNumbers_When_NoContainersExist_Then_ReturnEmptyList() {
        // Arrange
        Integer subBusinessUnitLocalId = 1;
        Integer languageId = 1;
        List<String> containerNumbers = List.of("CONT789");

        when(stockFullRepository.findYardIdByBusinessUnitId(subBusinessUnitLocalId)).thenReturn(100);
        when(stockFullRepository.findContainerByNumber("CONT789")).thenReturn(new ArrayList<>());

        // Act
        List<ValidateMassiveContainerRemovementOutput> result = service.validateMassiveContainerRemovement(
                subBusinessUnitLocalId, languageId, containerNumbers);

        // Assert
        assertEquals(0, result.size());
    }

    @Test
    void Given_ValidSubBusinessUnitIdAndContainerNumbers_When_ContainerLocationIsNull_Then_SetOutsideDepotTrue() {
        // Arrange
        Integer subBusinessUnitLocalId = 1;
        Integer languageId = 1;
        List<String> containerNumbers = List.of("CONT123");

        when(stockFullRepository.findYardIdByBusinessUnitId(subBusinessUnitLocalId)).thenReturn(100);
        when(stockFullRepository.findContainerByNumber("CONT123"))
                .thenReturn(Collections.singletonList(new Object[]{1, "CONT123"}));
        when(stockFullRepository.findContainerLocationJson(eq(1), eq(100))).thenReturn(null);

        // Act
        List<ValidateMassiveContainerRemovementOutput> result = service.validateMassiveContainerRemovement(
                subBusinessUnitLocalId, languageId, containerNumbers);

        // Assert
        assertEquals(1, result.size());
        assertEquals("CONT123", result.get(0).getContainerNumber());
        assertEquals(null, result.get(0).getContainerCurrentLocation());
        assertEquals(true, result.get(0).getOutsideDepot());
    }


}