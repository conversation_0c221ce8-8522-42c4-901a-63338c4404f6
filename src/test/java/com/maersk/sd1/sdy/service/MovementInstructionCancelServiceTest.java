package com.maersk.sd1.sdy.service;


import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.MovementInstructionRepository;
import com.maersk.sd1.sdy.dto.MovementInstructionCancelInput;
import com.maersk.sd1.sdy.dto.MovementInstructionCancelInput.Instruction;
import com.maersk.sd1.sdy.dto.MovementInstructionCancelOutput;
import com.maersk.sd1.sdy.dto.MovementInstructionCancelOutput.InstructionResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MovementInstructionCancelServiceTest {

    @Mock
    private MovementInstructionRepository movementInstructionRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @InjectMocks
    private MovementInstructionCancelService movementInstructionCancelService;

    private Instruction instruction;

    private MovementInstructionCancelInput.Input input;

    @BeforeEach
    void setUp() {
        instruction = new Instruction();
        instruction.setId(1001);

        input =  new MovementInstructionCancelInput.Input();
        input.setUserId(1);
        input.setMovementInstructions(List.of(instruction));
    }

    @Test
    void given_ValidInputs_cancelMovementInstructions_ThenSuccessfulCancellation() {

        List<Object[]> mockRows = new ArrayList<>();
        mockRows.add(new Object[]{1, "CONT123", "CR", "Container Ready", "B1", "C1", "R1", 1,
                "B2", "C2", "R2", 2, "B3", "C3", "R3", 3, "B4", "C4", "R4", 4});

        Catalog mockCatalog = new Catalog();
        mockCatalog.setId(999);

        when(movementInstructionRepository.findCompleteById(anyInt())).thenReturn(mockRows);
        when(catalogRepository.findCancelStatus()).thenReturn(Optional.of(mockCatalog));

        // Invoke service
        MovementInstructionCancelOutput output = movementInstructionCancelService.cancelMovementInstructions(input);

        // Assertions
        assertNotNull(output);
        assertFalse(output.getResults().isEmpty());

        InstructionResult result = output.getResults().get(0);
        assertEquals(1001, result.getInstructionId());
        assertFalse(result.getHasError());

    }

    @Test
    void given_InvalidInputs_cancelMovementInstructions_ThenErrorCancellation() {
        when(movementInstructionRepository.findCompleteById(anyInt())).thenReturn(List.of());

        MovementInstructionCancelOutput output = movementInstructionCancelService.cancelMovementInstructions(input);

        assertNotNull(output);
        assertFalse(output.getResults().isEmpty());

        InstructionResult result = output.getResults().get(0);
        assertEquals(1001, result.getInstructionId());
        assertTrue(result.getHasError());

        verify(movementInstructionRepository, never()).updateStatusById(anyInt(), anyInt());
    }

    @Test
    void given_InvalidStatus_cancelMovementInstructions_ThenErrorCancellation() {
        List<Object[]> mockRows = new ArrayList<>();
        mockRows.add(new Object[]{1, "CONT123", "EXE", "Container Ready", "B1", "C1", "R1", 1,
                "B2", "C2", "R2", 2, "B3", "C3", "R3", 3, "B4", "C4", "R4", 4});
        when(movementInstructionRepository.findCompleteById(anyInt())).thenReturn(mockRows);

        MovementInstructionCancelOutput output = movementInstructionCancelService.cancelMovementInstructions(input);

        assertNotNull(output);
        assertFalse(output.getResults().isEmpty());

        InstructionResult result = output.getResults().get(0);
        assertEquals(1001, result.getInstructionId());
        assertTrue(result.getHasError());

        verify(movementInstructionRepository, never()).updateStatusById(anyInt(), anyInt());
    }

    @Test
    void given_InvalidInputs_cancelMovementInstructions_ThenExceptionHandling() {
        when(movementInstructionRepository.findCompleteById(anyInt())).thenThrow(new RuntimeException("Database error"));

        Exception exception = assertThrows(RuntimeException.class, () ->
                movementInstructionCancelService.cancelMovementInstructions(input));

        assertEquals("Failed to cancel movement instructions", exception.getMessage());
    }
}
