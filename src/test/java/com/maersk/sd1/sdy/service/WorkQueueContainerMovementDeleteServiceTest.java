package com.maersk.sd1.sdy.service;

import com.maersk.sd1.sdy.controller.dto.WorkQueueContainerMovementDeleteOutput;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.common.repository.WorkQueueContainerMovementRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.times;

class WorkQueueContainerMovementDeleteServiceTest {

    @Mock
    private WorkQueueContainerMovementRepository repository;

    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    @InjectMocks
    private WorkQueueContainerMovementDeleteService service;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @Transactional
    void given_ValidData_When_DeleteWorkQueueContainerMovement_Then_Success() {
        // given
        Integer movementId = 100;
        Integer userModificationId = 500;
        Integer languageId = 2;
        when(repository.deactivateWorkQueueContainerMovement(
                ArgumentMatchers.eq(movementId), 
                ArgumentMatchers.eq(userModificationId), 
                ArgumentMatchers.any(LocalDateTime.class)))
                .thenReturn(1);
        when(messageLanguageRepository.fnTranslatedMessage(
                ArgumentMatchers.eq("GENERAL"), 
                ArgumentMatchers.eq(7), 
                ArgumentMatchers.eq(languageId)))
                .thenReturn("Operation completed successfully.");

        // when
        WorkQueueContainerMovementDeleteOutput output = service.deleteWorkQueueContainerMovement(movementId, userModificationId, languageId);

        // then
        verify(repository, times(1)).deactivateWorkQueueContainerMovement(
                ArgumentMatchers.eq(movementId), 
                ArgumentMatchers.eq(userModificationId), 
                ArgumentMatchers.any(LocalDateTime.class));
        assertNotNull(output);
        assertEquals(1, output.getRespEstado());
        assertEquals("Operation completed successfully.", output.getRespMensaje());
    }

    @Test
    @Transactional
    void given_NoRowsUpdated_When_DeleteWorkQueueContainerMovement_Then_NoRecordFound() {
        // given
        Integer movementId = 101;
        Integer userModificationId = 501;
        Integer languageId = 2;
        when(repository.deactivateWorkQueueContainerMovement(
                ArgumentMatchers.eq(movementId), 
                ArgumentMatchers.eq(userModificationId), 
                ArgumentMatchers.any(LocalDateTime.class)))
                .thenReturn(0);

        // when
        WorkQueueContainerMovementDeleteOutput output = service.deleteWorkQueueContainerMovement(movementId, userModificationId, languageId);

        // then
        verify(repository, times(1)).deactivateWorkQueueContainerMovement(
                ArgumentMatchers.eq(movementId), 
                ArgumentMatchers.eq(userModificationId), 
                ArgumentMatchers.any(LocalDateTime.class));
        assertNotNull(output);
        assertEquals(0, output.getRespEstado());
        assertEquals("No record found or nothing was updated.", output.getRespMensaje());
    }

    @Test
    @Transactional
    void given_ExceptionThrown_When_DeleteWorkQueueContainerMovement_Then_ReturnsError() {
        // given
        Integer movementId = 102;
        Integer userModificationId = 502;
        Integer languageId = 2;
        when(repository.deactivateWorkQueueContainerMovement(
                ArgumentMatchers.eq(movementId), 
                ArgumentMatchers.eq(userModificationId), 
                ArgumentMatchers.any(LocalDateTime.class)))
                .thenThrow(new RuntimeException("Test Exception"));

        // when
        WorkQueueContainerMovementDeleteOutput output = null;
        try {
            output = service.deleteWorkQueueContainerMovement(movementId, userModificationId, languageId);
        } catch (Exception e) {
            // Ensure the exception is handled gracefully
            assertNotNull(e);
            assertEquals("Test Exception", e.getMessage());
        }

        // then
        verify(repository, times(1)).deactivateWorkQueueContainerMovement(
                ArgumentMatchers.eq(movementId), 
                ArgumentMatchers.eq(userModificationId), 
                ArgumentMatchers.any(LocalDateTime.class));
        assertNotNull(output);
        assertEquals(0, output.getRespEstado());
        assertEquals("Test Exception", output.getRespMensaje());
    }
}
