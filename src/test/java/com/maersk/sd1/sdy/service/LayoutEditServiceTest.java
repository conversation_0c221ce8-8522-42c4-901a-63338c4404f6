package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.Layout;
import com.maersk.sd1.sdy.dto.LayoutEditOutput;
import com.maersk.sd1.common.repository.LayoutRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.doThrow;

@ExtendWith(MockitoExtension.class)
class LayoutEditServiceTest {

    @Mock
    private LayoutRepository layoutRepository;

    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    @InjectMocks
    private LayoutEditService layoutEditService;

    private Layout sampleLayout;

    @BeforeEach
    void setup() {
        sampleLayout = new Layout();
        sampleLayout.setId(1);
        sampleLayout.setName("Old Name");
        sampleLayout.setConfiguration("Old Config");
        sampleLayout.setMapSettings("Old Map");
        sampleLayout.setActive(false);
    }

    @Test
    void returnsSuccessfulResponseWhenLayoutIsUpdated() {
        Integer planoId = 1;
        String newName = "New Name";
        String newConfig = "New Config";
        String newMapSettings = "New Map";
        Boolean newActive = true;
        Integer modificationUserId = 100;
        Integer idiomaId = 1;
        String translatedMessage = "Success";

        when(layoutRepository.findById(planoId)).thenReturn(Optional.of(sampleLayout));
        when(messageLanguageRepository.fnTranslatedMessage("GENERAL", 10, idiomaId)).thenReturn(translatedMessage);

        LayoutEditOutput output = layoutEditService.editLayout(planoId, newName, newConfig, newMapSettings, newActive, modificationUserId, idiomaId);

        assertEquals(1, output.getRespEstado());
        assertEquals(translatedMessage, output.getRespMensaje());
    }

    @Test
    void returnsFailureResponseWhenLayoutNotFound() {
        Integer planoId = 2;
        String newName = "New Name";
        String newConfig = "New Config";
        String newMapSettings = "New Map";
        Boolean newActive = true;
        Integer modificationUserId = 100;
        Integer idiomaId = 1;

        when(layoutRepository.findById(planoId)).thenReturn(Optional.empty());

        LayoutEditOutput output = layoutEditService.editLayout(planoId, newName, newConfig, newMapSettings, newActive, modificationUserId, idiomaId);

        assertEquals(0, output.getRespEstado());
        assertTrue(output.getRespMensaje().contains("Layout not found"));
    }

    @Test
    void returnsFailureResponseWhenExceptionOccursDuringUpdate() {
        Integer planoId = 1;
        String newName = "New Name";
        String newConfig = "New Config";
        String newMapSettings = "New Map";
        Boolean newActive = true;
        Integer modificationUserId = 100;
        Integer idiomaId = 1;

        when(layoutRepository.findById(planoId)).thenReturn(Optional.of(sampleLayout));
        doThrow(new RuntimeException("Save error")).when(layoutRepository).save(ArgumentMatchers.any(Layout.class));

        LayoutEditOutput output = layoutEditService.editLayout(planoId, newName, newConfig, newMapSettings, newActive, modificationUserId, idiomaId);

        assertEquals(0, output.getRespEstado());
        assertTrue(output.getRespMensaje().contains("Save error"));
    }
}