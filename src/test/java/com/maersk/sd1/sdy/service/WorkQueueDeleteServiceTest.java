package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.model.WorkQueue;
import com.maersk.sd1.sdy.controller.dto.WorkQueueDeleteOutput;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.common.repository.WorkQueueRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.dao.EmptyResultDataAccessException;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class WorkQueueDeleteServiceTest {

    @Mock
    private WorkQueueRepository workQueueRepository;

    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    @InjectMocks
    private WorkQueueDeleteService workQueueDeleteService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void given_ValidParams_when_deleteWorkQueue_then_Success() {
        Integer colaTrabajoId = 100;
        Integer usuarioModificacionId = 200;
        Integer idiomaId = 2;

        WorkQueue mockWorkQueue = new WorkQueue();
        mockWorkQueue.setId(colaTrabajoId);
        mockWorkQueue.setActive(true);

        when(workQueueRepository.findById(colaTrabajoId)).thenReturn(Optional.of(mockWorkQueue));
        when(messageLanguageRepository.fnTranslatedMessage(eq("GENERAL"), eq(7), eq(idiomaId))).thenReturn("Deleted Successfully");
        when(workQueueRepository.save(any(WorkQueue.class))).thenAnswer(invocation -> invocation.getArgument(0));

        WorkQueueDeleteOutput result = workQueueDeleteService.deleteWorkQueue(colaTrabajoId, usuarioModificacionId, idiomaId);

        verify(workQueueRepository, times(1)).findById(colaTrabajoId);
        verify(workQueueRepository, times(1)).save(any(WorkQueue.class));
        verify(messageLanguageRepository, times(1)).fnTranslatedMessage("GENERAL", 7, idiomaId);

        assertEquals(1, result.getRespEstado());
        assertEquals("Deleted Successfully", result.getRespMensaje());
    }

    @Test
    void given_WorkQueueNotFound_when_deleteWorkQueue_then_ExceptionAndState0() {
        Integer colaTrabajoId = 999;
        Integer usuarioModificacionId = 200;
        Integer idiomaId = 2;

        when(workQueueRepository.findById(colaTrabajoId)).thenReturn(Optional.empty());

        WorkQueueDeleteOutput result = workQueueDeleteService.deleteWorkQueue(colaTrabajoId, usuarioModificacionId, idiomaId);

        verify(workQueueRepository, times(1)).findById(colaTrabajoId);
        verify(workQueueRepository, never()).save(any(WorkQueue.class));
        verify(messageLanguageRepository, never()).fnTranslatedMessage(anyString(), anyInt(), anyInt());

        assertEquals(0, result.getRespEstado());
        assertTrue(result.getRespMensaje().contains("WorkQueue not found"));
    }

    @Test
    void given_Exception_when_deleteWorkQueue_then_State0() {
        Integer colaTrabajoId = 100;
        Integer usuarioModificacionId = 200;
        Integer idiomaId = 2;

        when(workQueueRepository.findById(colaTrabajoId)).thenThrow(new EmptyResultDataAccessException("No entity", 1));

        WorkQueueDeleteOutput result = workQueueDeleteService.deleteWorkQueue(colaTrabajoId, usuarioModificacionId, idiomaId);

        verify(workQueueRepository, times(1)).findById(colaTrabajoId);
        verify(workQueueRepository, never()).save(any(WorkQueue.class));
        verify(messageLanguageRepository, never()).fnTranslatedMessage(anyString(), anyInt(), anyInt());

        assertEquals(0, result.getRespEstado());
        assertTrue(result.getRespMensaje().contains("No entity"));
    }
}
