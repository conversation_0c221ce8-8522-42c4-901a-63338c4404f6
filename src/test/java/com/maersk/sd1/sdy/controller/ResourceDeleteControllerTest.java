package com.maersk.sd1.sdy.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdy.dto.ResourceDeleteInput;
import com.maersk.sd1.sdy.dto.ResourceDeleteOutput;
import com.maersk.sd1.sdy.service.ResourceDeleteService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class ResourceDeleteControllerTest {

    @Mock
    private ResourceDeleteService resourceDeleteService;

    @InjectMocks
    private ResourceDeleteController resourceDeleteController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void givenValidRequest_whenDeleteResource_thenReturnsOk() {
        // given
        ResourceDeleteInput.Input input = new ResourceDeleteInput.Input();
        input.setRecursoId(100);
        input.setUsuarioModificacionId(500);
        input.setIdiomaId(2);

        ResourceDeleteInput.Root request = new ResourceDeleteInput.Root();
        request.setPrefix(new ResourceDeleteInput.Prefix());
        request.getPrefix().setInput(input);

        ResourceDeleteOutput output = new ResourceDeleteOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Success");

        when(resourceDeleteService.deleteResource(100, 500, 2)).thenReturn(output);

        // when
        ResponseEntity<ResponseController<ResourceDeleteOutput>> response = resourceDeleteController.deleteResource(request);

        // then
        assertEquals(200, response.getStatusCode().value());
        verify(resourceDeleteService, times(1)).deleteResource(100, 500, 2);
    }

    @Test
    void givenInvalidRequest_whenDeleteResource_thenReturnsBadRequest() {
        // given
        ResourceDeleteInput.Root request = null;

        // when
        ResponseEntity<ResponseController<ResourceDeleteOutput>> response = resourceDeleteController.deleteResource(request);

        // then
        assertEquals(500, response.getStatusCode().value());
        verifyNoInteractions(resourceDeleteService);
    }

    @Test
    void givenNullRecursoId_whenDeleteResource_thenReturnsBadRequest() {
        // given
        ResourceDeleteInput.Input input = new ResourceDeleteInput.Input();
        input.setRecursoId(null);
        input.setUsuarioModificacionId(500);
        input.setIdiomaId(2);

        ResourceDeleteInput.Root request = new ResourceDeleteInput.Root();
        request.setPrefix(new ResourceDeleteInput.Prefix());
        request.getPrefix().setInput(input);

        // when
        ResponseEntity<ResponseController<ResourceDeleteOutput>> response = resourceDeleteController.deleteResource(request);

        // then
        assertEquals(200, response.getStatusCode().value());
     }

    @Test
    void givenServiceThrowsException_whenDeleteResource_thenReturnsInternalServerError() {
        // given
        ResourceDeleteInput.Input input = new ResourceDeleteInput.Input();
        input.setRecursoId(100);
        input.setUsuarioModificacionId(500);
        input.setIdiomaId(2);

        ResourceDeleteInput.Root request = new ResourceDeleteInput.Root();
        request.setPrefix(new ResourceDeleteInput.Prefix());
        request.getPrefix().setInput(input);

        when(resourceDeleteService.deleteResource(100, 500, 2)).thenThrow(new RuntimeException("Service error"));

        // when
        ResponseEntity<ResponseController<ResourceDeleteOutput>> response = resourceDeleteController.deleteResource(request);

        // then
        assertEquals(500, response.getStatusCode().value());
        verify(resourceDeleteService, times(1)).deleteResource(100, 500, 2);
    }
}
