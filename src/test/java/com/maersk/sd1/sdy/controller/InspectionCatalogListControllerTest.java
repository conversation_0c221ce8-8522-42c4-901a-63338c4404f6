package com.maersk.sd1.sdy.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdy.dto.InspectionCatalogListInput;
import com.maersk.sd1.sdy.dto.InspectionCatalogListOutput;
import com.maersk.sd1.sdy.service.InspectionCatalogListService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InspectionCatalogListControllerTest {

    @Mock
    private InspectionCatalogListService inspectionCatalogListService;

    @InjectMocks
    private InspectionCatalogListController inspectionCatalogListController;

    @Test
    void Given_ValidRequest_When_GetInspectionCatalogList_Then_ReturnsCatalogList() {
        // Arrange
        InspectionCatalogListOutput output = InspectionCatalogListOutput.builder()
                .id(1)
                .description("Description")
                .longDescription("Long Description")
                .status(true)
                .code("Code")
                .alias("Alias")
                .generalIndicator('Y')
                .businessUnitId(100)
                .parentCatalogId(200)
                .registrationUserId(300)
                .registrationDate(LocalDateTime.now())
                .modificationUserId(400)
                .modificationDate(LocalDateTime.now())
                .variable1("Var1")
                .variable2("Var2")
                .variable3(123)
                .build();
        when(inspectionCatalogListService.getCatalogList()).thenReturn(List.of(output));
        InspectionCatalogListInput.Root request = new InspectionCatalogListInput.Root();

        // Act
        ResponseEntity<ResponseController<List<InspectionCatalogListOutput>>> response = inspectionCatalogListController.getInspectionCatalogList(request);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(1, response.getBody().getResult().size());
        assertEquals("Description", response.getBody().getResult().get(0).getDescription());
        verify(inspectionCatalogListService, times(1)).getCatalogList();
    }

    @Test
    void Given_ServiceThrowsException_When_GetInspectionCatalogList_Then_ReturnsErrorResponse() {
        // Arrange
        when(inspectionCatalogListService.getCatalogList()).thenThrow(new RuntimeException("Service error"));
        InspectionCatalogListInput.Root request = new InspectionCatalogListInput.Root();

        // Act
        ResponseEntity<ResponseController<List<InspectionCatalogListOutput>>> response = inspectionCatalogListController.getInspectionCatalogList(request);

        // Assert
        assertEquals(500, response.getStatusCode().value());
        assertEquals(1, response.getBody().getResult().size());
        assertEquals(null, response.getBody().getResult().get(0).getId());
        verify(inspectionCatalogListService, times(1)).getCatalogList();
    }

    @Test
    void Given_EmptyCatalogList_When_GetInspectionCatalogList_Then_ReturnsEmptyResponse() {
        // Arrange
        when(inspectionCatalogListService.getCatalogList()).thenReturn(Collections.emptyList());
        InspectionCatalogListInput.Root request = new InspectionCatalogListInput.Root();

        // Act
        ResponseEntity<ResponseController<List<InspectionCatalogListOutput>>> response = inspectionCatalogListController.getInspectionCatalogList(request);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(0, response.getBody().getResult().size());
        verify(inspectionCatalogListService, times(1)).getCatalogList();
    }
}