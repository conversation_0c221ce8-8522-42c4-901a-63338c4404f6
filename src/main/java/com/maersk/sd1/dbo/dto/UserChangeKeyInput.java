package com.maersk.sd1.dbo.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class UserChangeKeyInput {

    @Data
    public static class Input {

        @JsonProperty("usuario")
        @NotNull(message = "usuario cannot be null")
        @Size(max = 100)
        private String usuario;

        @JsonProperty("clave")
        @Size(max = 20)
        private String clave;

        @JsonProperty("clave_md5")
        @Size(max = 50)
        private String claveMd5;

        @JsonProperty("origen")
        @Size(max = 3)
        private String origen;

        @JsonProperty("old_password")
        @NotNull(message = "old_password cannot be null")
        @Size(max = 100)
        private String oldPassword;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("DBO")
        private Prefix prefix;
    }
}

