package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
public class FindMostAvailableFullContainerGateOutOutputDTO {

    private List<ContainerGateOutItem> containers;

    @Data
    public static class ContainerGateOutItem {
        private Integer containerId;
        private String containerNumber;
        private Integer containerLocationId;
        private Integer liftMoves;
    }
}
