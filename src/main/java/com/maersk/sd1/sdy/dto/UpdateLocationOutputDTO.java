package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
public class UpdateLocationOutputDTO {

    @JsonProperty("usuario_alias")
    private String userAlias;

    @JsonProperty("sub_unidad_negocio_alias")
    private String subBusinessUnitAlias;

    @JsonProperty("unidad_negocio_id")
    private Integer businessUnitId;

    @JsonProperty("unidad_negocio_padre_id")
    private Integer parentBusinessUnitId;

    @JsonProperty("patio_id")
    private Integer yardId;

    @JsonProperty("patio_codigo")
    private String yardCode;

    @JsonProperty("tipo_operacion")
    private String operationType;

    @JsonProperty("tipo_movimiento")
    private String movementType;

    @JsonProperty("proceso_realizado")
    private String completedProcess;

    @JsonProperty("IsCorrect")
    private Boolean isCorrect;

    @JsonProperty("Status")
    private String status;

    @JsonProperty("contenedores")
    private List<ContainerVisit> containers = new ArrayList<>();

    @JsonProperty("aditional_data")
    private List<EquipmentDocument> additionalData = new ArrayList<>();

    @JsonProperty("planned_equipments")
    private List<PlannedEquipmentResponse> plannedEquipments = new ArrayList<>();

    @JsonProperty("usuario_id")
    private int userId;

    @JsonProperty("BusinessMessage")
    private String businessMessage;

    @JsonProperty("cola_trabajo_id")
    private Integer workQueueId;

    @JsonProperty("bloque_destino_id")
    private Integer destinationBlockId;

    @JsonProperty("bloque_destino_codigo")
    private String destinationBlockCode;

    @JsonProperty("rows_skip_range")
    private List<Integer> rowsSkipRange;

    @JsonProperty("skip_block_id")
    private Integer skipBlockId;

    @JsonProperty("father_movement_instruction_id")
    private Integer parentMovementInstructionId;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContainerVisit {

        @JsonProperty("contenedor_id")
        private Integer containerId;

        @JsonProperty("numero_contenedor")
        private String containerNumber;

        @JsonProperty("programacion_nave_contenedor_id")
        private Integer vesselScheduleContainerId;

        @JsonProperty("nave")
        private String vessel;

        @JsonProperty("viaje")
        private String voyage;

        @JsonProperty("documento_referencia")
        private String referenceDocument;

        @JsonProperty("eir_numero")
        private String eirNumber;

        @JsonProperty("contador_peticion")
        private Integer requestCounter = 1;

        @JsonProperty("ubicacion_origen")
        private UpdateLocationInputDTO.ContainerLocationValue originLocation;

        @JsonProperty("ubicacion_destino")
        private UpdateLocationInputDTO.ContainerLocationValue destinationLocation;

        @JsonProperty("instruccion_numero")
        private Integer instructionNumber;

        @JsonProperty("instruccion_estado_codigo")
        private String instructionStatusCode;

        @JsonProperty("instruccion_estado_descripcion")
        private String instructionStatusDescription;

        @JsonProperty("placa_vehiculo")
        private String vehiclePlate;

        @JsonProperty("clase")
        private String containerClass;

        @JsonProperty("tamanio")
        private String containerSize;

        @JsonProperty("familia")
        private String containerFamily;

        @JsonProperty("condicion")
        private String containerCondition;

        @JsonProperty("newYardLocation")
        private String newYardLocation;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EquipmentDocument {

        @JsonProperty("document")
        private PlanningDocument document;

        @JsonProperty("equipment")
        private PlanningEquipment equipment;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PlannedEquipmentResponse {

        @JsonProperty("equipment_number")
        private String equipmentNumber;

        @JsonProperty("destination_location")
        private String destinationLocation;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PlanningDocument {

        @JsonProperty("eir_id")
        private Integer eirId;

        @JsonProperty("documento_carga_id")
        private Integer cargoDocumentId;

        @JsonProperty("documento_carga")
        private String cargoDocument;

        @JsonProperty("fecha_registro")
        private String registrationDate;

        @JsonProperty("puerto_descarga_id")
        private Integer dischargePortId;

        @JsonProperty("puerto_nombre")
        private String portName;

        @JsonProperty("puerto")
        private String portCode;

        @JsonProperty("pais_id")
        private Integer countryId;

        @JsonProperty("nave_id")
        private Integer vesselId;

        @JsonProperty("nave_nombre")
        private String vesselName;

        @JsonProperty("nave")
        private String vesselCode;

        @JsonProperty("imo_number")
        private String imoNumber;

        @JsonProperty("viaje")
        private String voyage;

        @JsonProperty("contenedor_id")
        private Integer containerId;

        @JsonProperty("numero_contenedor")
        private String containerNumber;

        @JsonProperty("linea_naviera_id")
        private Integer shippingLineId;

        @JsonProperty("linea_naviera_nombre")
        private String shippingLineName;

        @JsonProperty("linea_naviera")
        private String shippingLine;

        @JsonProperty("cat_operacion_id")
        private Integer operationCategoryId;

        @JsonProperty("cat_operacion_codigo")
        private String operationCategoryCode;

        @JsonProperty("cat_operacion_descripcion")
        private String operationCategoryDescription;

        @JsonProperty("cat_origen_carga_id")
        private Integer cargoOriginCategoryId;

        @JsonProperty("cat_origen_carga_codigo")
        private String cargoOriginCategoryCode;

        @JsonProperty("cat_origen_carga_descripcion")
        private String cargoOriginCategoryDescription;

        @JsonProperty("cat_condicion_carga_id")
        private Integer cargoConditionCategoryId;

        @JsonProperty("cat_condicion_carga_codigo")
        private String cargoConditionCategoryCode;

        @JsonProperty("cat_condicion_carga_descripcion")
        private String cargoConditionCategoryDescription;

        @JsonProperty("cat_movimiento_id")
        private Integer movementCategoryId;

        @JsonProperty("cat_movimiento_codigo")
        private String movementCategoryCode;

        @JsonProperty("cat_movimiento_descripcion")
        private String movementCategoryDescription;

        @JsonProperty("cat_procedencia_id")
        private Integer originCategoryId;

        @JsonProperty("cat_procedencia_codigo")
        private String originCategoryCode;

        @JsonProperty("cat_procedencia_descripcion")
        private String originCategoryDescription;

        @JsonProperty("variable_3")
        private Integer variable3;

        @JsonProperty("cat_empty_full_id")
        private Integer emptyFullCategoryId;

        @JsonProperty("cat_empty_full_codigo")
        private String emptyFullCategoryCode;

        @JsonProperty("cat_empty_full_descripcion")
        private String emptyFullCategoryDescription;

        @JsonProperty("empresa_consignatario_id")
        private Integer consigneeCompanyId;

        @JsonProperty("razon_social")
        private String businessName;

        @JsonProperty("direccion")
        private String address;

        @JsonProperty("vehiculo_id")
        private Integer vehicleId;

        @JsonProperty("placa")
        private String plate;

        @JsonProperty("carga_util")
        private Float payload;

        @JsonProperty("peso_neto")
        private Float netWeight;

        @JsonProperty("peso_bruto")
        private Float grossWeight;

        @JsonProperty("sub_business_unit_local_alias")
        private String subBusinessUnitAlias;

        @JsonProperty("yard_code")
        private String yardCode;

        @JsonProperty("cat_structure_condition_id")
        private Integer structureConditionCategoryId;

        @JsonProperty("cat_structure_condition_codigo")
        private String structureConditionCategoryCode;

        @JsonProperty("cat_structure_condition_descripcion")
        private String structureConditionCategoryDescription;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PlanningEquipment {

        @JsonProperty("contenedor_id")
        private int containerId;

        @JsonProperty("numero_contenedor")
        private String containerNumber;

        @JsonProperty("cat_familia_id")
        private Integer familyCategoryId;

        @JsonProperty("cat_tamano_id")
        private Integer sizeCategoryId;

        @JsonProperty("cat_tipo_contenedor_id")
        private Integer containerTypeCategoryId;

        @JsonProperty("cat_clase_id")
        private Integer classCategoryId;

        @JsonProperty("linea_naviera_id")
        private Integer shippingLineId;

        @JsonProperty("tara")
        private Double tare;

        @JsonProperty("carga_maxima")
        private Double maxLoad;

        @JsonProperty("codigo_iso_id")
        private Integer isoCodeId;

        @JsonProperty("cat_tipo_reefer_id")
        private Integer reeferTypeCategoryId;

        @JsonProperty("cat_marca_motor_id")
        private Integer engineBrandCategoryId;

        @JsonProperty("shipper_own")
        private boolean isShipperOwn;

        @JsonProperty("cat_tipo_actividad_id")
        private Integer activityTypeCategoryId;

        @JsonProperty("actividad_codigo")
        private String activityCode;

        @JsonProperty("actividad_descripcion")
        private String activityDescription;

        @JsonProperty("linea_naviera")
        private String shippingLine;

        @JsonProperty("linea_naviera_nombre")
        private String shippingLineName;
    }

}
