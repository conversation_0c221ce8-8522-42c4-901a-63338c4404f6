package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class VirtualBlockRequestInputDTO {

    @Data
    public static class Input {

        @JsonProperty("bloque_codigo")
        @NotNull
        @Size(max = 10)
        private String blockCode;

        @JsonProperty("patio_id")
        @NotNull
        private Integer yardId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDY")
        private Prefix prefix;
    }
}
