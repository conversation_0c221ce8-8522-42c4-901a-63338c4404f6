package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class PlanificacionRecursoColaTrabajoAsignarInput {

    @Data
    public static class Input {

        @JsonProperty("cola_trabajo_id")
        @NotNull
        private Integer colaTrabajoId;

        @JsonProperty("recurso_id")
        @NotNull
        private Integer recursoId;

        @JsonProperty("usuario_id")
        @NotNull
        private Integer usuarioId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private PlanificacionRecursoColaTrabajoAsignarInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDY")
        private PlanificacionRecursoColaTrabajoAsignarInput.Prefix prefix;
    }
}
