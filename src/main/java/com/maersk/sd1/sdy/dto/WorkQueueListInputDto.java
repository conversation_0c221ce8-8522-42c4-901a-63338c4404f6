package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.Data;
import java.time.LocalDate;

public class WorkQueueListInputDto {

    @Data
    public static class Input {

        @JsonProperty("cola_trabajo_id")
        private Integer queueId;

        @JsonProperty("patio_id")
        private Integer yardId;

        @JsonProperty("codigo")
        @Size(max = 200)
        private String code;

        @JsonProperty("descripcion")
        @Size(max = 200)
        private String description;

        @JsonProperty("activo")
        @JsonDeserialize(using = com.maersk.sd1.common.utils.BooleanDeserializer.class)
        private Boolean active;

        @JsonProperty("fecha_registro_min")
        private LocalDate registrationDateMin;

        @JsonProperty("fecha_registro_max")
        private LocalDate registrationDateMax;

        @JsonProperty("fecha_modificacion_min")
        private LocalDate modificationDateMin;

        @JsonProperty("fecha_modificacion_max")
        private LocalDate modificationDateMax;

        @JsonProperty("usuario_id")
        private Integer userId;

        @JsonProperty("estado_id")
        private Integer catStatusId;

        @JsonProperty("punto_trabajo_id")
        private Integer workPointId;

        @JsonProperty("sub_unidad_negocio_local_id")
        private Integer subBusinessUnitId;

        @JsonProperty("por_defecto")
        @JsonDeserialize(using = com.maersk.sd1.common.utils.BooleanDeserializer.class)
        private Boolean byDefault;

        @JsonProperty("page")
        @Min(1)
        private Integer page;

        @JsonProperty("size")
        @Positive
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDY")
        private Prefix prefix;
    }
}

