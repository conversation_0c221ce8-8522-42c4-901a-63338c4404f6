package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * Input DTO mirroring the stored procedure parameters.
 * It follows the same nested structure (Root -> Prefix -> Input) as in the sample.
 */
@Data
public class RulePlanningListInputDTO {

    @Data
    public static class Input {
        @JsonProperty("regla_planificacion_patio_id")
        private Integer reglaPlanificacionPatioId;

        @JsonProperty("linea_naviera_id")
        private Integer lineaNavieraId;

        @JsonProperty("cliente_id")
        private Long clienteId;

        @JsonProperty("cat_status_id")
        private Long catStatusId;

        @JsonProperty("cat_movimiento_id")
        private Long catMovimientoId;

        @JsonProperty("cat_actividad_contenedor_id")
        private Long catActividadContenedorId;

        @JsonProperty("familia_contenedor_id")
        private Long familiaContenedorId;

        @JsonProperty("tamano_contenedor_id")
        private Long tamanoContenedorId;

        @JsonProperty("tipo_contenedor_id")
        private Long tipoContenedorId;

        @JsonProperty("clase_contenedor_id")
        private Long claseContenedorId;

        @JsonProperty("tipo_categoria_id")
        private Integer tipoCategoriaId;

        @JsonProperty("condicion_reefer_id")
        private Long condicionReeferId;

        @JsonProperty("condicion_caja_id")
        private Long condicionCajaId;

        @JsonProperty("condicion_ca_id")
        private Long condicionCaId;

        @JsonProperty("nave_id")
        private Integer naveId;

        @JsonProperty("viaje_id")
        private Integer viajeId;

        @JsonProperty("puerto_descarga_id")
        private Integer puertoDescargaId;

        @JsonProperty("codigo_grupo_id")
        private Integer codigoGrupoId;

        @JsonProperty("codigo")
        private String codigo;

        @JsonProperty("descripcion")
        private String descripcion;

        @JsonProperty("prioridad")
        private Integer prioridad;

        @JsonProperty("numero_documento_referencia")
        private String numeroDocumentoReferencia;

        @JsonProperty("indice_ordenamiento")
        private Integer indiceOrdenamiento;

        @JsonProperty("activo")
        private Boolean activo;

        @JsonProperty("fecha_registro_min")
        private LocalDate fechaRegistroMin;

        @JsonProperty("fecha_registro_max")
        private LocalDate fechaRegistroMax;

        @JsonProperty("fecha_modificacion_min")
        private LocalDate fechaModificacionMin;

        @JsonProperty("fecha_modificacion_max")
        private LocalDate fechaModificacionMax;

        @JsonProperty("patio_id")
        private Integer patioId;

        @JsonProperty("requiere_conexion_electrica")
        private Boolean requiereConexionElectrica;

        @JsonProperty("cat_tecnologia_id")
        private Long catTecnologiaId;

        @JsonProperty("cat_lavado_id")
        private Long catLavadoId;

        @JsonProperty("carga_imo")
        private Boolean cargaImo;

        @JsonProperty("cat_imo_id")
        private Long catImoId;

        @JsonProperty("sub_unidad_negocio_local_id")
        private Integer subUnidadNegocioLocalId;

        @NotNull
        @Min(1)
        @JsonProperty("page")
        private Integer page;

        @NotNull
        @Min(1)
        @JsonProperty("size")
        private Integer size;

        @JsonProperty("cat_empty_full_id")
        private Long catEmptyFullId;

        @JsonProperty("cat_structure_condition_id")
        private Long catStructureConditionId;

        @JsonProperty("cat_type_activity_id")
        private Long catTypeActivityId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDY")
        private Prefix prefix;
    }
}

