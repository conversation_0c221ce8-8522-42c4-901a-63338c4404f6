package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class RetrieveYardPlanningInput {

    public RetrieveYardPlanningInput() {}

    @Data
    public static class Input {

        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;

        @JsonProperty("patio_codigo")
        private String yardCode;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDY")
        private Prefix prefix;
    }
}
