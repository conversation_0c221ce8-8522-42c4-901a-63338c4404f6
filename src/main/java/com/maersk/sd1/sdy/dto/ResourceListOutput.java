package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Output DTO structure that returns the total number of records and a list of the resource records.
 */
@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class ResourceListOutput {

    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    @JsonProperty("total")
    private List<List<Long>> total;

    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    @JsonProperty("recursos")
    private List<ResourceData> recursos;

    /**
     * Inner DTO representing each Resource row with extended user info and queue info.
     */
    @Data
    public static class ResourceData {
        @JsonProperty("recurso_id")
        private Integer recursoId;

        @JsonProperty("cat_recurso_id")
        private Integer catRecursoId;

        @JsonProperty("cat_carga_contenedor_id")
        private Integer catCargaContenedorId;

        @JsonProperty("codigo")
        private String codigo;

        @JsonProperty("nombre")
        private String nombre;

        @JsonProperty("marca")
        private String marca;

        @JsonProperty("maximo_nivel_apilamiento")
        private Integer maximoNivelApilamiento;

        @JsonProperty("activo")
        private Character activo;

        @JsonProperty("fecha_registro")
        private String fechaRegistro;

        @JsonProperty("fecha_modificacion")
        private String fechaModificacion;

        @JsonProperty("punto_trabajo_id")
        private Integer puntoTrabajoId;

        @JsonProperty("punto_trabajo")
        private String puntoTrabajo;

        @JsonProperty("usuario_registro_id")
        private Integer usuarioRegistroId;

        @JsonProperty("usuario_modificacion_id")
        private Integer usuarioModificacionId;

        @JsonProperty("usuario_registro_nombres")
        private String usuarioRegistroNombres;

        @JsonProperty("usuario_registro_apellidos")
        private String usuarioRegistroApellidos;

        @JsonProperty("usuario_modificacion_nombres")
        private String usuarioModificacionNombres;

        @JsonProperty("usuario_modificacion_apellidos")
        private String usuarioModificacionApellidos;

        @JsonProperty("colas_trabajo")
        private String colasTrabajo;
    }

    /**
     * DTO for holding queue info for each resource.
     */
    @Data
    public static class ColaTrabajoData {
        @JsonProperty("cola_trabajo_id")
        private Integer colaTrabajoId;

        @JsonProperty("codigo")
        private String codigo;

        @JsonProperty("descripcion")
        private String descripcion;
    }
}

