package com.maersk.sd1.sdy.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.Date;


@Getter
@Setter
@SuperBuilder
public abstract class Auditoria {
    private double usuario_registro_id;
    private Date fecha_registro;
    private Double usuario_modificacion_id;
    private Date fecha_modificacion;

    public Auditoria(double u_r, Date f_r) {
        usuario_registro_id = u_r;
        fecha_registro = f_r;
        /*
         * usuario_modificacion_id = u_m; fecha_modificacion = f_m;
         */
    }
}