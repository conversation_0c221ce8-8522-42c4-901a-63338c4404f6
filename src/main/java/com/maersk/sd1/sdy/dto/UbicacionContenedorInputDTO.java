package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class UbicacionContenedorInputDTO {

    @Data
    public static class Input {

        @JsonProperty("patio_id")
        @NotNull(message = "patio_id cannot be null")
        private Integer patioId;

        @JsonProperty("ubicaciones")
        @NotNull(message = "ubicaciones cannot be null")
        @Size(min = 1, message = "ubicaciones must not be empty")
        private String ubicaciones;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDY")
        private Prefix prefix;
    }
}
