package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class PlanificacionUbicacionDestinoCamionInput {

    @Data
    public static class Input {

        @JsonProperty("instruccion_movimiento_id")
        @NotNull
        private Integer instruccionMovimientoId;

        @JsonProperty("bloque_codigo")
        @NotNull
        @Size(max = 10)
        private String bloqueCodigo;

        @JsonProperty("fila")
        @NotNull
        @Size(max = 10)
        private String fila;

        @JsonProperty("columna")
        @NotNull
        @Size(max = 10)
        private String columna;

        @JsonProperty("nivel")
        @NotNull
        private Integer nivel;

        @JsonProperty("bloque40_codigo")
        @NotNull
        @Size(max = 10)
        private String bloque40Codigo;

        @JsonProperty("fila40")
        @NotNull
        @Size(max = 10)
        private String fila40;

        @JsonProperty("columna40")
        @NotNull
        @Size(max = 10)
        private String columna40;

        @JsonProperty("nivel40")
        @NotNull
        private Integer nivel40;

        @JsonProperty("usuario_id")
        @NotNull
        private Integer usuarioId;

        @JsonProperty("placa_numero")
        @NotNull
        @Size(max = 10)
        private String placaNumero;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDG")
        private Prefix prefix;
    }
}
