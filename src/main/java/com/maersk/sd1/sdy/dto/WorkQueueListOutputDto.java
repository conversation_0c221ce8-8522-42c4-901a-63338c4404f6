package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class WorkQueueListOutputDto {

    @JsonProperty("total_registros")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<List<Long>> totalRecords;

    @JsonProperty("registros")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<WorkQueueRecordDto> records;

    @Data
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    public static class WorkQueueRecordDto {
        @JsonProperty("cola_trabajo_id")
        private Integer queueId;

        @JsonProperty("patio_id")
        private Integer yardId;

        @JsonProperty("codigo")
        private String code;

        @JsonProperty("descripcion")
        private String description;

        @JsonProperty("activo")
        private Boolean active;

        @JsonProperty("fecha_registro")
        private String registrationDate;

        @JsonProperty("fecha_modificacion")
        private String modificationDate;

        @JsonProperty("usuario_id")
        private Integer userId;

        @JsonProperty("usuario_nombres")
        private String userNames;

        @JsonProperty("usuario_apellidos")
        private String userLastNames;

        @JsonProperty("estado_id")
        private Integer catStatusId;

        @JsonProperty("punto_trabajo_id")
        private Integer workPointId;

        @JsonProperty("punto_trabajo_codigo")
        private String workPointCode;

        @JsonProperty("usuario_registro_id")
        private Integer registrationUserId;

        @JsonProperty("usuario_modificacion_id")
        private Integer modificationUserId;

        @JsonProperty("usuario_registro_nombres")
        private String registrationUserNames;

        @JsonProperty("usuario_registro_apellidos")
        private String registrationUserLastNames;

        @JsonProperty("usuario_modificacion_nombres")
        private String modificationUserNames;

        @JsonProperty("usuario_modificacion_apellidos")
        private String modificationUserLastNames;

        @JsonProperty("por_defecto")
        private Boolean byDefault;
    }
}

