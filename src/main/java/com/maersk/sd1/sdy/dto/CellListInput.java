package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.UtilityClass;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

@UtilityClass
public class CellListInput {

    @Data
    public static class Input {
        @JsonProperty("celda_id")
        private Integer cellId;

        @JsonProperty("bloque_id")
        private Integer blockId;

        @JsonProperty("fila")
        private String row;

        @JsonProperty("columna")
        private String column;

        @JsonProperty("indice_fila")
        private Integer rowIndex;

        @JsonProperty("indice_columna")
        private Integer columnIndex;

        @JsonProperty("cantidad_contenedores")
        private Integer containersQuantity;

        @JsonProperty("bloqueado")
        private Boolean blocked;

        @JsonProperty("activo")
        private Boolean active;

        @JsonProperty("fecha_registro_min")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private LocalDate registrationDateMin;

        @JsonProperty("fecha_registro_max")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private LocalDate registrationDateMax;

        @JsonProperty("fecha_modificacion_min")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private LocalDate modificationDateMin;

        @JsonProperty("fecha_modificacion_max")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private LocalDate modificationDateMax;

        @NotNull
        @Min(value = 1, message = "Page must be at least 1")
        @JsonProperty("page")
        private Integer page;

        @NotNull
        @Min(value = 1, message = "Size must be at least 1")
        @JsonProperty("size")
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDY")
        private Prefix prefix;
    }
}
