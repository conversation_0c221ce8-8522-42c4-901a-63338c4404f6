package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.UtilityClass;

import java.util.List;

@Data
@UtilityClass
public class UpdateLocationInputDTO {

    @Data
    public static class Input{
        @JsonProperty("unidad_negocio_id")
        private int businessUnitId;

        @JsonProperty("usuario_id")
        private int userId;

        @JsonProperty("patio_codigo")
        private String yardCode;

        @JsonProperty("contenedores")
        private List<ContainerVisitRequest> containers;

        @JsonProperty("patio_id")
        private int yardId;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContainerVisitRequest {

        @JsonProperty("numero_contenedor")
        private String containerNumber;

        @JsonProperty("instruccion_numero")
        private Integer instructionNumber;

        @JsonProperty("contador_peticion")
        private Integer requestCounter = 1;

        @JsonProperty("contenedor_id")
        private Integer containerId;

        @JsonProperty("ubicacion")
        private ContainerLocationValue location;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContainerLocationValue {

        @JsonProperty("bloque_codigo")
        private String blockCode;

        @JsonProperty("fila")
        private String row;

        @JsonProperty("columna")
        private String column;

        @JsonProperty("nivel")
        private Integer level;

        @JsonProperty("bloque_tipo")
        private String blockType;

        @JsonProperty("ubicacion40")
        private String location40;

        @JsonProperty("bloque_codigo40")
        private String blockCode40;

        @JsonProperty("fila40")
        private String row40;

        @JsonProperty("columna40")
        private String column40;

        @JsonProperty("nivel40")
        private Integer level40;

        public String getUbicacion() {
            return formatLegacy();
        }

        private String formatLegacy() {
            if ("STACK".equalsIgnoreCase(blockType)) {
                return blockCode + row.trim() + column.trim() + "." + level;
            } else if ("HEAP".equalsIgnoreCase(blockType) || "VIRTUAL".equalsIgnoreCase(blockType)) {
                return blockCode;
            } else {
                return blockCode + row.trim() + column.trim() + "." + level;
            }
        }
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDY")
        private Prefix prefix;
    }
}
