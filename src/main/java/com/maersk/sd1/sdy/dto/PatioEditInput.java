package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.UtilityClass;

import java.math.BigDecimal;

@UtilityClass
public class PatioEditInput {

    @Data
    public static class Input {

        @JsonProperty("patio_id")
        @NotNull(message = "patio_id cannot be null")
        private Integer patioId;

        @JsonProperty("plano_id")
        @NotNull(message = "plano_id cannot be null")
        private Integer layoutId;

        @JsonProperty("codigo")
        @NotNull(message = "codigo cannot be null")
        @Size(max = 10, message = "codigo can have max 10 characters")
        private String code;

        @JsonProperty("nombre")
        @Size(max = 100, message = "nombre can have max 100 characters")
        private String name;

        @JsonProperty("zoom")
        private Integer zoom;

        @JsonProperty("latitud")
        private BigDecimal latitude;

        @JsonProperty("color")
        @Size(max = 20, message = "color can have max 20 characters")
        private String color;

        @JsonProperty("longitud")
        private BigDecimal longitude;

        @JsonProperty("configuracion")
        @NotNull(message = "configuracion cannot be null")
        private String configuration;

        @JsonProperty("activo")
        @NotNull(message = "activo cannot be null")
        private Boolean active;

        @JsonProperty("usuario_modificacion_id")
        @NotNull(message = "usuario_modificacion_id cannot be null")
        private Integer userModificationId;

        @JsonProperty("uid_seleccionada")
        @NotNull(message = "uid_seleccionada cannot be null")
        private String uidSeleccionada;

        @JsonProperty("idioma_id")
        @NotNull(message = "idioma_id cannot be null")
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull(message = "F cannot be null")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDY")
        @NotNull(message = "SDG cannot be null")
        private Prefix prefix;
    }
}
