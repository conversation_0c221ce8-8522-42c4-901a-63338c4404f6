package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class WorkPointDeleteInput {

    @Data
    public static class Input {

        @JsonProperty("punto_trabajo_id")
        @NotNull(message = "workPointId must not be null")
        private Integer workPointId;

        @JsonProperty("usuario_modificacion_id")
        @NotNull(message = "userModificationId must not be null")
        private Integer userModificationId;

        @JsonProperty("idioma_id")
        @NotNull(message = "languageId must not be null")
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDY")
        private Prefix prefix;
    }
}

