package com.maersk.sd1.sdy.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MovementInstructionDetailsDto {
    private Integer currentStatusId;
    private String currentStatusCode;
    private Integer eirId;
    private Integer movementTypeId;
    private Boolean eirActive;
    private LocalDateTime eirTruckDepartureDate;
    private Integer eirCatEmptyFullId;
    private Integer eirDocumentCargoGofId;
    private Integer containerId;
    private Integer eirCurrentContainerId;
    private Integer fatherMovementInstructionId;
    private Integer destiny20BlockId;
    private Integer destiny20CellId;
    private Integer destiny20LevelId;
    private Integer destiny40BlockId;
    private Integer destiny40CellId;
    private Integer destiny40LevelId;
}