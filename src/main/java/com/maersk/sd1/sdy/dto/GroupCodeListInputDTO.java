package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDate;

/**
 * This DTO encapsulates the input request structure:
 * {
 *   "SDG": {
 *     "F": {
 *       "codigo_grupo_id": ...,  (Integer)
 *       "patio_id": ...,         (Integer)
 *       "codigo": ...,          (String)
 *       "nombre": ...,          (String)
 *       "activo": ...,          (Boolean)
 *       "fecha_registro_min": ... (LocalDate)
 *       "fecha_registro_max": ... (LocalDate)
 *       "fecha_modificacion_min": ... (LocalDate)
 *       "fecha_modificacion_max": ... (LocalDate)
 *       "page": ...,            (Integer)
 *       "size": ...             (Integer)
 *     }
 *   }
 * }
 */
public class GroupCodeListInputDTO {

    @Data
    public static class Input {

        @JsonProperty("codigo_grupo_id")
        private Integer codigoGrupoId;

        @JsonProperty("patio_id")
        private Integer patioId;

        @JsonProperty("codigo")
        @Size(max = 10)
        private String codigo;

        @JsonProperty("nombre")
        @Size(max = 100)
        private String nombre;

        @JsonProperty("activo")
        private Boolean activo;

        @JsonProperty("fecha_registro_min")
        private LocalDate fechaRegistroMin;

        @JsonProperty("fecha_registro_max")
        private LocalDate fechaRegistroMax;

        @JsonProperty("fecha_modificacion_min")
        private LocalDate fechaModificacionMin;

        @JsonProperty("fecha_modificacion_max")
        private LocalDate fechaModificacionMax;

        // Pagination
        @JsonProperty("page")
        private Integer page;

        @JsonProperty("size")
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDY")
        @NotNull
        private Prefix prefix;
    }
}
