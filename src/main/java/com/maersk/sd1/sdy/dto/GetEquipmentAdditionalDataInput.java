package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class GetEquipmentAdditionalDataInput {

    private GetEquipmentAdditionalDataInput() {}
    @Data
    public static class Input {

        @JsonProperty("process")
        private String process;

        @JsonProperty("equipment")
        private String equipment;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private GetEquipmentAdditionalDataInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDY")
        private GetEquipmentAdditionalDataInput.Prefix prefix;
    }
}
