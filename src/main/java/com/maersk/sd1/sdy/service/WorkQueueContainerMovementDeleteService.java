package com.maersk.sd1.sdy.service;

import com.maersk.sd1.sdy.controller.dto.WorkQueueContainerMovementDeleteOutput;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.common.repository.WorkQueueContainerMovementRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@RequiredArgsConstructor
@Service
public class WorkQueueContainerMovementDeleteService {

    private static final Logger logger = LogManager.getLogger(WorkQueueContainerMovementDeleteService.class);

    private final WorkQueueContainerMovementRepository workQueueContainerMovementRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Transactional
    public WorkQueueContainerMovementDeleteOutput deleteWorkQueueContainerMovement(Integer movementId,
                                                                                   Integer userModificationId,
                                                                                   Integer languageId) {
        logger.info("Attempt to deactivate WorkQueueContainerMovement with id: {}", movementId);
        WorkQueueContainerMovementDeleteOutput output = new WorkQueueContainerMovementDeleteOutput();
        try {
            Integer updatedRows = workQueueContainerMovementRepository.deactivateWorkQueueContainerMovement(
                    movementId,
                    userModificationId,
                    LocalDateTime.now()
            );

            if (updatedRows > 0) {
                output.setRespEstado(1);
                String translatedMessage = messageLanguageRepository.fnTranslatedMessage("GENERAL", 7, languageId);
                output.setRespMensaje(translatedMessage);
            } else {
                output.setRespEstado(0);
                output.setRespMensaje("No record found or nothing was updated.");
            }
            logger.info("Deactivation completed successfully for WorkQueueContainerMovement with id: {}", movementId);
        } catch (Exception e) {
            logger.error("Error during deactivation: ", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}