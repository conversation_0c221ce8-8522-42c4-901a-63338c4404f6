package com.maersk.sd1.sdy.service;

import com.maersk.sd1.sdy.dto.WorkQueueListInputDto;
import com.maersk.sd1.sdy.dto.WorkQueueListOutputDto;
import com.maersk.sd1.common.repository.WorkQueueListRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class WorkQueueListService {

    private static final Logger logger = LogManager.getLogger(WorkQueueListService.class);

    private final WorkQueueListRepository workQueueListRepository;

    @Transactional(readOnly = true)
    public WorkQueueListOutputDto listWorkQueues(WorkQueueListInputDto.Input input) {
        // Logging the input
        logger.info("Listing WorkQueues with filters: {}", input);

        // Handling the null page and size scenario from the SP logic:
        Integer page = (input.getPage() == null) ? 1 : input.getPage();
        Integer size = (input.getSize() == null) ? Integer.MAX_VALUE : input.getSize();
        if (size <= 0) {
            size = 10; // fallback if something invalid
        }


        // Convert LocalDate to LocalDateTime for filtering
        LocalDate registrationDateMin = input.getRegistrationDateMin();
        LocalDate registrationDateMax = input.getRegistrationDateMax();
        LocalDateTime regMinDateTime = null;
        LocalDateTime regMaxDateTime = null;
        if (registrationDateMin != null && registrationDateMax != null) {
            regMinDateTime = registrationDateMin.atStartOfDay();
            regMaxDateTime = registrationDateMax.plusDays(1).atStartOfDay();
        }

        LocalDate modificationDateMin = input.getModificationDateMin();
        LocalDate modificationDateMax = input.getModificationDateMax();
        LocalDateTime modMinDateTime = null;
        LocalDateTime modMaxDateTime = null;
        if (modificationDateMin != null && modificationDateMax != null) {
            modMinDateTime = modificationDateMin.atStartOfDay();
            modMaxDateTime = modificationDateMax.plusDays(1).atStartOfDay();
        }

        Boolean filterByDefault = input.getByDefault();
        // The SP logic states if byDefault is not null => filter by TRUE only.
        // We interpret that as is: if not null, we pass the same boolean, but the SP only checks if it's 1.
        // We'll replicate that. If input is not null AND false => The SP logic forced it equals 1. We'll keep the code consistent:
        // If input is false we do not get any row in the SP. So replicate that means if it's false, we won't find any record.
        // We'll pass the parameter as is, so the repository method can handle ( byDefault = TRUE ) only if not null.
        if (filterByDefault != null && !filterByDefault) {
            // The stored procedure basically yields no results because it only checks = 1
            // We'll keep consistent by passing 'true' if filterByDefault is not null.
            filterByDefault = true;
        }

        Long total = workQueueListRepository.countWorkQueues(
                input.getQueueId(),
                input.getYardId(),
                input.getCode(),
                input.getDescription(),
                input.getActive(),
                regMinDateTime,
                regMaxDateTime,
                modMinDateTime,
                modMaxDateTime,
                input.getUserId(),
                input.getCatStatusId(),
                input.getWorkPointId(),
                input.getSubBusinessUnitId()
        );


        List<Object[]> temp = workQueueListRepository.findWorkQueues(
                input.getQueueId(),
                input.getYardId(),
                input.getCode(),
                input.getDescription(),
                input.getActive(),
                regMinDateTime,
                regMaxDateTime,
                modMinDateTime,
                modMaxDateTime,
                input.getUserId(),
                input.getCatStatusId(),
                input.getWorkPointId(),
                input.getSubBusinessUnitId(),
                filterByDefault,
                page,
                size,
                total
        );

        List<WorkQueueListOutputDto.WorkQueueRecordDto> records = mapToWorkQueueRecordDtos(temp);



        WorkQueueListOutputDto outputDto = new WorkQueueListOutputDto();
        outputDto.setTotalRecords(List.of(List.of(total)));
        outputDto.setRecords(records);

        logger.info("Found {} records.",  total);
        return outputDto;
    }
    public List<WorkQueueListOutputDto.WorkQueueRecordDto> mapToWorkQueueRecordDtos(List<Object[]> temp) {
        List<WorkQueueListOutputDto.WorkQueueRecordDto> records = new ArrayList<>();

        for (Object[] row : temp) {
            WorkQueueListOutputDto.WorkQueueRecordDto record = new WorkQueueListOutputDto.WorkQueueRecordDto();
            record.setQueueId((Integer) row[0]);
            record.setYardId((Integer) row[1]);
            record.setCode((String) row[2]);
            record.setDescription((String) row[3]);
            record.setActive((Boolean) row[4]);
            record.setRegistrationDate(row[5] != null ? row[5].toString() : null);
            record.setModificationDate(row[6] != null ? row[6].toString() : null);
            record.setUserId((Integer) row[7]);
            record.setUserNames((String) row[8]);
            record.setUserLastNames((String) row[9]);
            record.setRegistrationUserId(row[10] != null ? ((java.math.BigDecimal) row[10]).intValue() : null);
            record.setWorkPointId((Integer) row[11]);
            record.setWorkPointCode((String) row[12]);
            record.setRegistrationUserId(row[13] != null ? ((java.math.BigDecimal) row[13]).intValue() : null);
            record.setRegistrationUserId(row[14] != null ? ((java.math.BigDecimal) row[14]).intValue() : null);
            record.setRegistrationUserNames((String) row[15]);
            record.setRegistrationUserLastNames((String) row[16]);
            record.setModificationUserNames((String) row[17]);
            record.setModificationUserLastNames((String) row[18]);
            record.setByDefault((Boolean) row[19]);
            records.add(record);
        }

        return records;
    }
}

