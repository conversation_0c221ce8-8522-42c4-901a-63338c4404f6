package com.maersk.sd1.sdy.service;

import com.maersk.sd1.sdy.dto.ResourceDeleteOutput;
import com.maersk.sd1.common.repository.ResourceRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
@Log4j2
public class ResourceDeleteService {

    private final ResourceRepository resourceRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Transactional
    public ResourceDeleteOutput deleteResource(Integer resourceId,
                                               Integer userModificationId,
                                               Integer languageId) {
        ResourceDeleteOutput output = new ResourceDeleteOutput();
        try {
            LocalDateTime now = LocalDateTime.now();
            int updatedRows = resourceRepository.deactivateResource(resourceId, userModificationId, now);

            if (updatedRows > 0) {
                String translatedMessage = messageLanguageRepository.fnTranslatedMessage("GENERAL", 7, languageId);
                output.setRespEstado(1);
                output.setRespMensaje(translatedMessage);
            } else {
                output.setRespEstado(0);
                output.setRespMensaje("No resource found to deactivate.");
            }
        } catch (Exception e) {
            log.error("Error while deactivating resource", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}
