package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.Resource;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.model.WorkQueueResource;
import com.maersk.sd1.common.repository.ResourceRepository;
import com.maersk.sd1.common.repository.WorkQueueResourceRepository;
import com.maersk.sd1.sdy.dto.ResourceListInput;
import com.maersk.sd1.sdy.dto.ResourceListOutput;
import com.maersk.sd1.sdy.dto.ResourceListOutput.ColaTrabajoData;
import com.maersk.sd1.sdy.dto.ResourceListOutput.ResourceData;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ResourceListingService {

    private static final Logger logger = LogManager.getLogger(ResourceListingService.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final WorkQueueResourceRepository workQueueResourceRepository;

    private final ResourceRepository resourceRepository;

    @Transactional(readOnly = true)
    public ResourceListOutput listResources(@Valid ResourceListInput.Root root) {
        logger.info("Starting service to list resources with given filters.");
        ResourceListInput.Input input = root.getPrefix().getInput();

        // Parse date parameters
        LocalDateTime regDateStart = null;
        LocalDateTime regDateEnd = null;
        String regMinStr = input.getFechaRegistroMin();
        String regMaxStr = input.getFechaRegistroMax();
        if (regMinStr != null && !regMinStr.isEmpty() && regMaxStr != null && !regMaxStr.isEmpty()) {
            LocalDate regMin = LocalDate.parse(regMinStr, DATE_FORMATTER);
            LocalDate regMax = LocalDate.parse(regMaxStr, DATE_FORMATTER);
            regDateStart = regMin.atStartOfDay();
            regDateEnd = regMax.plusDays(1).atStartOfDay();
        }

        LocalDateTime modDateStart = null;
        LocalDateTime modDateEnd = null;
        String modMinStr = input.getFechaModificacionMin();
        String modMaxStr = input.getFechaModificacionMax();
        if (modMinStr != null && !modMinStr.isEmpty() && modMaxStr != null && !modMaxStr.isEmpty()) {
            LocalDate modMin = LocalDate.parse(modMinStr, DATE_FORMATTER);
            LocalDate modMax = LocalDate.parse(modMaxStr, DATE_FORMATTER);
            modDateStart = modMin.atStartOfDay();
            modDateEnd = modMax.plusDays(1).atStartOfDay();
        }

        // Parse active parameter
        Boolean active = null;
        if (input.getActivo() != null) {
            active = "true".equalsIgnoreCase(input.getActivo()) || "1".equals(input.getActivo());
        }

        // Create page request - note that page is 1-based, adjusting to 0-based for PageRequest.
        int pageIndex = (input.getPage() == null || input.getPage() < 1) ? 0 : (input.getPage() - 1);
        int size = (input.getSize() == null || input.getSize() < 1) ? Integer.MAX_VALUE : input.getSize();
        org.springframework.data.domain.PageRequest pageRequest = org.springframework.data.domain.PageRequest.of(
            pageIndex, size, org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, "id")
        );

        // Execute query with JPQL
        org.springframework.data.domain.Page<Resource> resultPage = resourceRepository.findResourcesWithFilters(
            input.getRecursoId(),
            input.getCatRecursoId(),
            input.getCatCargaContenedorId(),
            input.getCodigo(),
            input.getNombre(),
            input.getMarca(),
            input.getMaximoNivelApilamiento(),
            active,
            regDateStart,
            regDateEnd,
            modDateStart,
            modDateEnd,
            input.getPuntoTrabajoId(),
            input.getSubUnidadNegocioId(),
            pageRequest
        );

        // Map to output
        ResourceListOutput output = new ResourceListOutput();
        output.setTotal(List.of(List.of(resultPage.getTotalElements())));

        List<Resource> resources = resultPage.getContent();

        List<ResourceData> dataList = new ArrayList<>();
        for (Resource resource : resources) {
            ResourceData rd = new ResourceData();
            rd.setRecursoId(resource.getId());
            rd.setCatRecursoId(resource.getCatResource() != null ? resource.getCatResource().getId() : null);
            rd.setCatCargaContenedorId(resource.getCatContainerLoad() != null ? resource.getCatContainerLoad().getId() : null);
            rd.setCodigo(resource.getCode());
            rd.setNombre(resource.getName());
            rd.setMarca(resource.getBrand());
            rd.setMaximoNivelApilamiento(resource.getMaximumLevelStacking());
            rd.setActivo(resource.getActive() != null && resource.getActive() ? '1' : '0');
            rd.setFechaRegistro(resource.getRegistrationDate() != null ? resource.getRegistrationDate().toString() : null);
            rd.setFechaModificacion(resource.getModificationDate() != null ? resource.getModificationDate().toString() : null);
            rd.setPuntoTrabajoId(resource.getWorkPoint() != null ? resource.getWorkPoint().getId() : null);
            rd.setPuntoTrabajo(resource.getWorkPoint() != null ? resource.getWorkPoint().getCode() : null);

            // Registration user
            User regUser = resource.getRegistrationUser();
            rd.setUsuarioRegistroId(regUser != null ? regUser.getId() : null);
            if (regUser != null) {
                rd.setUsuarioRegistroNombres(regUser.getNames());
                String apellidos = (regUser.getFirstLastName() != null ? regUser.getFirstLastName() : "") + " " +
                        (regUser.getSecondLastName() != null ? regUser.getSecondLastName() : "").trim();
                rd.setUsuarioRegistroApellidos(apellidos.trim());
            }

            // Modification user
            User modUser = resource.getModificationUser();
            rd.setUsuarioModificacionId(modUser != null ? modUser.getId() : null);
            if (modUser != null) {
                rd.setUsuarioModificacionNombres(modUser.getNames());
                String apellidosMod = (modUser.getFirstLastName() != null ? modUser.getFirstLastName() : "") + " " +
                        (modUser.getSecondLastName() != null ? modUser.getSecondLastName() : "").trim();
                rd.setUsuarioModificacionApellidos(apellidosMod.trim());
            }

            // Fetch colas_trabajo from relation in WorkQueueResource
            List<WorkQueueResource> wqrList = workQueueResourceRepository.findByResource_Id(resource.getId());
            List<ColaTrabajoData> colaTrabajoDataList = new ArrayList<>();
            if (wqrList != null) {
                for (WorkQueueResource wqr : wqrList) {
                    if (wqr.getWorkQueue() != null) {
                        ColaTrabajoData ctd = new ColaTrabajoData();
                        ctd.setColaTrabajoId(wqr.getWorkQueue().getId());
                        ctd.setCodigo(wqr.getWorkQueue().getCode());
                        ctd.setDescripcion(wqr.getWorkQueue().getDescription());
                        colaTrabajoDataList.add(ctd);
                    }
                }
            }

            try {
                // Convert the list to a JSON string
                com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
                String colasTrabajoJson = objectMapper.writeValueAsString(colaTrabajoDataList);
                rd.setColasTrabajo(colasTrabajoJson);
            } catch (com.fasterxml.jackson.core.JsonProcessingException e) {
                logger.error("Error converting colas trabajo to JSON", e);
                rd.setColasTrabajo("[]"); // Set empty array as fallback
            }

            dataList.add(rd);
        }
        output.setRecursos(dataList);
        logger.info("Completed service to list resources.");
        return output;
    }


}
