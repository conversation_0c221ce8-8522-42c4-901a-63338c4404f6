package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.Block;
import com.maersk.sd1.common.model.Yard;
import com.maersk.sd1.common.model.YardPlanningRule;
import com.maersk.sd1.common.model.YardPlanningRuleRange;
import com.maersk.sd1.sdy.dto.ReglaPlanificacionPatioDatosObtenerInput;
import com.maersk.sd1.sdy.dto.ReglaPlanificacionPatioDatosObtenerOutput;
import com.maersk.sd1.sdy.dto.ReglaPlanificacionPatioDatosObtenerOutput.BloqueOutputDto;
import com.maersk.sd1.sdy.dto.ReglaPlanificacionPatioDatosObtenerOutput.PatioOutputDto;
import com.maersk.sd1.sdy.dto.ReglaPlanificacionPatioDatosObtenerOutput.RangoOutputDto;
import com.maersk.sd1.common.repository.BlockRepository;
import com.maersk.sd1.common.repository.YardPlanningRuleRangeRepository;
import com.maersk.sd1.common.repository.YardPlanningRuleRepository;
import com.maersk.sd1.sdy.dto.YardPlanningRuleRangeSpecification;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@Service
public class ReglaPlanificacionPatioDatosObtenerService {
    private static final Logger logger = LogManager.getLogger(ReglaPlanificacionPatioDatosObtenerService.class);

    private final YardPlanningRuleRepository yardPlanningRuleRepository;
    private final BlockRepository blockRepository;
    private final YardPlanningRuleRangeRepository yardPlanningRuleRangeRepository;

    @Transactional(readOnly = true)
    public ReglaPlanificacionPatioDatosObtenerOutput obtenerDatos(ReglaPlanificacionPatioDatosObtenerInput.Input input) {
        logger.info("Service - obtenerDatos called with input: {}", input);

        ReglaPlanificacionPatioDatosObtenerOutput output = new ReglaPlanificacionPatioDatosObtenerOutput();

        // Step 1: Fetch the Yard from YardPlanningRule
        Optional<YardPlanningRule> yardPlanningRuleOptional = yardPlanningRuleRepository.findById(input.getYardPlanningRuleId());
        if (yardPlanningRuleOptional.isEmpty()) {
            // If we don't find the rule, we can return empty or handle as needed.
            logger.warn("No YardPlanningRule found for ID: {}", input.getYardPlanningRuleId());
            return output; // returns empty data
        }
        YardPlanningRule yardPlanningRule = yardPlanningRuleOptional.get();
        Yard yard = yardPlanningRule.getYard();

        // If yard is null, handle.
        if (yard == null) {
            logger.warn("Yard not found in the yardPlanningRule with ID: {}", input.getYardPlanningRuleId());
            return output;
        }

        // Fill up the PatioOutputDto
        List<PatioOutputDto> patioDtoList = new ArrayList<>();
        PatioOutputDto patioDto = new PatioOutputDto();
        patioDto.setPatioId(yard.getId());
        patioDto.setCodigo(yard.getCode());
        patioDto.setNombre(yard.getName());
        patioDto.setUnidadNegocioId(yard.getBusinessUnit() == null ? null : yard.getBusinessUnit().getId());
        patioDtoList.add(patioDto);
        output.setPatio(patioDtoList);

        // Step 2: Find blocks by yard and active= true. (As per sp) but sp used 1 for active.
        List<Block> blocks = blockRepository.findByYardAndActive(yard, true);
        List<BloqueOutputDto> bloqueOutputDtoList = new ArrayList<>();
        for (Block b : blocks) {
            BloqueOutputDto dto = new BloqueOutputDto();
            dto.setBloqueId(b.getId());
            dto.setCatBloqueId(b.getCatBlockType() == null ? null : b.getCatBlockType().getId());
            dto.setCodigo(b.getCode());
            dto.setNombre(b.getName());
            dto.setFilas(b.getRows());
            dto.setColumnas(b.getColumns());
            dto.setNiveles(b.getLevels());
            dto.setBlockDirection(b.getBlockDirection());
            dto.setDireccionStacking(b.getStackingDirection());
            bloqueOutputDtoList.add(dto);
        }
        output.setBloques(bloqueOutputDtoList);


        // Build specification
        var spec = YardPlanningRuleRangeSpecification.filter(
                input.getYardPlanningRuleId(),
                input.getBlockId(),
                input.getActive(),
                input.getFechaRegistroMin(),
                input.getFechaRegistroMax(),
                input.getFechaModificacionMin(),
                input.getFechaModificacionMax()
        );

        // First we get total count
        long totalRecords = yardPlanningRuleRangeRepository.count(spec);
        output.setTotalRegistros(List.of(List.of(totalRecords)));

        if (totalRecords == 0) {
            // No records found, return output with empty list
            output.setRangos(new ArrayList<>());
            return output;
        }

        // Now gather page & size ( offset )
        int page = (input.getPage() == null || input.getPage() < 1) ? 1 : input.getPage();
        int size = (input.getSize() == null || input.getSize() < 1) ? 100 : input.getSize();

        // offset arrangement with page: page is 1-based, so offset is (page-1)
        var pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "id"));
        Page<YardPlanningRuleRange> pagedResult = yardPlanningRuleRangeRepository.findAll(spec, pageable);

        // Convert the entities to RangoOutputDto
        List<RangoOutputDto> rangoDtoList = new ArrayList<>();
        for (YardPlanningRuleRange range : pagedResult.getContent()) {
            RangoOutputDto dto = new RangoOutputDto();
            dto.setRangoReglaPlanificacionPatioId(range.getId());
            dto.setBloqueId(range.getBlock() != null ? range.getBlock().getId() : null);
            dto.setIndiceColumnaDesde(range.getIndexColumnSince());
            dto.setIndiceColumnaHasta(range.getIndexColumnUntil());
            dto.setIndiceFilaDesde(range.getIndexRowSince());
            dto.setIndiceFilaHasta(range.getIndexRowUntil());
            dto.setActivo(range.getActive());
            dto.setUsuarioRegistroId(range.getRegistrationUser() != null ? range.getRegistrationUser().getId() : null);
            dto.setFechaRegistro(range.getRegistrationDate());
            dto.setUsuarioModificacionId(range.getModificationUser() != null ? range.getModificationUser().getId() : null);
            dto.setFechaModificacion(range.getModificationDate());
            dto.setDireccionStacking(range.getDirectionStacking());
            dto.setIndiceLimiteStacking(range.getIndexLimitStacking());

            // user names
            if (range.getRegistrationUser() != null) {
                dto.setUsuarioRegistroNombres(range.getRegistrationUser().getNames());
                dto.setUsuarioRegistroApellidos(range.getRegistrationUser().getFirstLastName());
            }
            if (range.getModificationUser() != null) {
                dto.setUsuarioModificacionNombres(range.getModificationUser().getNames());
                dto.setUsuarioModificacionApellidos(range.getModificationUser().getFirstLastName());
            }

            rangoDtoList.add(dto);
        }

        output.setRangos(rangoDtoList);
        return output;
    }
}