package com.maersk.sd1.sdy.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.dto.SystemRuleMergedShippingLinesDTO;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.service.SystemRuleService;
import com.maersk.sd1.sdy.dto.OutgoingContainerPlanningSearchInput;
import com.maersk.sd1.sdy.dto.OutgoingContainerPlanningSearchOutput;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class OutgoingContainerPlanningSearchService {

    private final ContainerRepository containerRepository;
    private final ContainerLocationRepository containerLocationRepository;
    private final BookingRepository bookingRepository;
    private final SystemRuleRepository systemRuleRepository;
    private final SystemRuleService systemRuleService;
    private final BusinessUnitRepository businessUnitRepository;
    private final EirRepository eirRepository;
    private final EirActivityZoneRepository eirActivityZoneRepository;
    private final ObjectMapper objectMapper;

    @Transactional(readOnly = true)
    public OutgoingContainerPlanningSearchOutput searchOutgoingContainerPlanning(OutgoingContainerPlanningSearchInput.Input input) {
        log.info("Starting outgoing container planning search with input: {}", input);

        try {
            // 1. Validate yard availability for the business unit
            boolean yardEnabled = validateYardAvailability(input.getUnidadNegocioId());

            // 2. Get reference container location if provided
            ReferenceContainerLocation referenceLocation = null;
            if (input.getContenedorReferencia() != null && !input.getContenedorReferencia().trim().isEmpty()) {
                referenceLocation = getReferenceContainerLocation(input.getContenedorReferencia());
            }

            // 3. Get shipping line from booking
            Integer shippingLineId = getShippingLineFromBooking(input.getBooking());

            // 4. Get merged shipping lines for Maersk group
            List<Integer> mergedShippingLineIds = getMergedShippingLineIds(shippingLineId);

            // 5. Search containers with all criteria
            List<OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData> containers =
                searchContainersWithCriteria(input, mergedShippingLineIds, referenceLocation, yardEnabled);

            // 6. Create output
            OutgoingContainerPlanningSearchOutput output = new OutgoingContainerPlanningSearchOutput();
            output.setContenedores(containers);

            log.info("Found {} containers for outgoing planning", containers.size());
            return output;

        } catch (Exception e) {
            log.error("Error in outgoing container planning search", e);
            throw new RuntimeException("Error searching outgoing container planning", e);
        }
    }

    private boolean validateYardAvailability(Integer unidadNegocioId) {
        try {
            // Get parent business unit
            Optional<BusinessUnit> businessUnit = businessUnitRepository.findById(unidadNegocioId);
            if (businessUnit.isEmpty()) {
                return true; // Default to enabled if business unit not found
            }

            Integer parentBusinessUnitId = businessUnit.get().getParentBusinessUnit() != null ?
                businessUnit.get().getParentBusinessUnit().getId() : unidadNegocioId;

            // Get system rule for yard availability
            String rule = systemRuleRepository.findRuleByIdAndActiveTrue("sdy_disponibilidad_sde");
            if (rule == null) {
                return true; // Default to enabled if rule not found
            }

            // Parse JSON rule to check if yard is enabled for this business unit
            JsonNode ruleNode = objectMapper.readTree(rule);
            if (ruleNode.isArray()) {
                for (JsonNode node : ruleNode) {
                    if (node.has("unidad_negocio_id") && node.get("unidad_negocio_id").asInt() == unidadNegocioId) {
                        return node.has("activo") && node.get("activo").asBoolean();
                    }
                }
            }

            return true; // Default to enabled
        } catch (Exception e) {
            log.warn("Error validating yard availability, defaulting to enabled", e);
            return true;
        }
    }

    private ReferenceContainerLocation getReferenceContainerLocation(String containerNumber) {
        // Find container location for reference container
        List<ContainerLocation> locations = containerLocationRepository.findByContainer_ContainerNumberAndActiveTrue(containerNumber);

        if (!locations.isEmpty()) {
            ContainerLocation location = locations.get(0);
            // Get the minimum row for 40ft containers
            List<ContainerLocation> sameContainerLocations = containerLocationRepository
                .findByContainer_IdAndActiveTrue(location.getContainer().getId());

            ContainerLocation minRowLocation = sameContainerLocations.stream()
                .min(Comparator.comparing(cl -> cl.getCell().getRow()))
                .orElse(location);

            return ReferenceContainerLocation.builder()
                .blockCode(minRowLocation.getBlock().getCode())
                .columnIndex(minRowLocation.getCell().getColumnIndex())
                .rowIndex(minRowLocation.getCell().getRowIndex())
                .levelIndex(minRowLocation.getLevel().getIndex())
                .build();
        }

        return null;
    }

    private Integer getShippingLineFromBooking(String bookingNumber) {
        Optional<Booking> booking = bookingRepository.findByBookingNumber(bookingNumber);
        return booking.map(b -> b.getVesselProgrammingDetail().getShippingLine().getId()).orElse(null);
    }

    private List<Integer> getMergedShippingLineIds(Integer shippingLineId) {
        List<Integer> mergedIds = new ArrayList<>();

        if (shippingLineId != null) {
            mergedIds.add(shippingLineId);

            // Get merged shipping lines from system rule
            List<SystemRuleMergedShippingLinesDTO> mergedShippingLines = systemRuleService.mapRuleMergedShippingLines();
            if (mergedShippingLines != null) {
                for (SystemRuleMergedShippingLinesDTO mergedGroup : mergedShippingLines) {
                    for (SystemRuleMergedShippingLinesDTO.MergedDetail detail : mergedGroup.getMergedDetails()) {
                        if (detail.getLineId() == shippingLineId) {
                            // Add all other lines in the same group
                            mergedIds.addAll(mergedGroup.getMergedDetails().stream()
                                .map(SystemRuleMergedShippingLinesDTO.MergedDetail::getLineId)
                                .collect(Collectors.toList()));
                            break;
                        }
                    }
                }
            }
        }

        return mergedIds.stream().distinct().collect(Collectors.toList());
    }

    private List<OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData>
        searchContainersWithCriteria(OutgoingContainerPlanningSearchInput.Input input,
                                   List<Integer> mergedShippingLineIds,
                                   ReferenceContainerLocation referenceLocation,
                                   boolean yardEnabled) {

        // Build the search criteria
        List<Container> containers = findEligibleContainers(input, mergedShippingLineIds);

        // Convert to output DTOs with additional calculations
        List<OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData> result =
            containers.stream()
                .map(container -> convertToOutputData(container, referenceLocation))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // Sort results based on criteria
        sortResults(result, referenceLocation, yardEnabled);

        // Apply limit if specified
        if (input.getCantidadRegistros() != null && input.getCantidadRegistros() > 0) {
            result = result.stream()
                .limit(input.getCantidadRegistros())
                .collect(Collectors.toList());
        }

        return result;
    }

    private List<Container> findEligibleContainers(OutgoingContainerPlanningSearchInput.Input input,
                                                  List<Integer> mergedShippingLineIds) {
        // This would be implemented as a custom repository method
        // For now, using a simplified approach
        return containerRepository.findEligibleContainersForOutgoingPlanning(
            input.getContenedorTamanoId(),
            input.getContenedorTipoId(),
            mergedShippingLineIds,
            input.getUnidadNegocioId(),
            input.getCargaMaxima(),
            input.getClaseId(),
            input.getTecnologiaId(),
            input.getContenedorOk(),
            input.getContenedorReferencia()
        );
    }

    private OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData
        convertToOutputData(Container container, ReferenceContainerLocation referenceLocation) {

        // Get container location
        List<ContainerLocation> locations = containerLocationRepository
            .findByContainer_IdAndActiveTrue(container.getId());

        if (locations.isEmpty()) {
            return null;
        }

        // Get minimum row location for 40ft containers
        ContainerLocation location = locations.stream()
            .min(Comparator.comparing(cl -> cl.getCell().getRow()))
            .orElse(locations.get(0));

        // Calculate days of permanence
        Integer daysPermanence = calculateDaysPermanence(container);

        // Calculate proximity to reference container
        Integer proximityToContainer = calculateProximity(location, referenceLocation);

        OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData data =
            new OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData();

        data.setContenedorId(container.getId());
        data.setNumeroContenedor(container.getContainerNumber());
        data.setTamanoCodigo(container.getCatSize().getDescription());
        data.setTipoCodigo(container.getCatContainerType().getDescription());
        data.setCargaMaxima(container.getMaximunPayload());
        data.setClaseCodigo(container.getCatGrade() != null ? container.getCatGrade().getDescription() : null);
        data.setTipoReeferCodigo(container.getCatReeferType() != null ? container.getCatReeferType().getDescription() : null);
        data.setLineaNaviera(container.getShippingLine().getShippingLine());
        data.setOrigenBloque(location.getBlock().getCode());
        data.setOrigenColumna(location.getCell().getColumn());
        data.setOrigenFila(location.getCell().getRow());
        data.setOrigenNivel(location.getLevel().getIndex());
        data.setCantidadRemovidos(location.getQuantityRemoved());
        data.setDiasPermanencia(daysPermanence);
        data.setCercaniaAContenedor(proximityToContainer);

        // Get container status from latest EIR zone activity
        String containerStatus = getContainerStatus(container);
        data.setEstadoContenedor(containerStatus);

        return data;
    }

    private Integer calculateDaysPermanence(Container container) {
        try {
            // Get the latest EIR for this container
            Optional<Eir> latestEir = eirRepository.findTopByContainer_IdOrderByIdDesc(container.getId());
            if (latestEir.isEmpty()) {
                return 0;
            }

            // Get the latest EIR zone activity for this EIR
            Optional<EirActivityZone> latestActivity = eirActivityZoneRepository
                .findTopByEir_IdOrderByIdDesc(latestEir.get().getId());

            if (latestActivity.isEmpty()) {
                return 0;
            }

            // Calculate days between start date and now
            LocalDateTime startDate = latestActivity.get().getStartDate();
            return (int) ChronoUnit.DAYS.between(startDate, LocalDateTime.now());

        } catch (Exception e) {
            log.warn("Error calculating days of permanence for container {}", container.getId(), e);
            return 0;
        }
    }

    private Integer calculateProximity(ContainerLocation location, ReferenceContainerLocation referenceLocation) {
        if (referenceLocation == null) {
            return 0;
        }

        // Calculate Euclidean distance squared
        int columnDiff = Math.abs(referenceLocation.getColumnIndex() - location.getCell().getColumnIndex());
        int rowDiff = Math.abs(referenceLocation.getRowIndex() - location.getCell().getRowIndex());

        return (columnDiff * columnDiff) + (rowDiff * rowDiff);
    }

    private String getContainerStatus(Container container) {
        try {
            // Get the latest EIR for this container
            Optional<Eir> latestEir = eirRepository.findTopByContainer_IdOrderByIdDesc(container.getId());
            if (latestEir.isEmpty()) {
                return "UNKNOWN";
            }

            // Get the latest EIR zone activity for this EIR
            Optional<EirActivityZone> latestActivity = eirActivityZoneRepository
                .findTopByEir_IdOrderByIdDesc(latestEir.get().getId());

            if (latestActivity.isEmpty()) {
                return "UNKNOWN";
            }

            // Get the EIR zone and its container zone catalog
            EirZone eirZone = latestActivity.get().getEirZone();
            if (eirZone != null && eirZone.getCatContainerZone() != null) {
                return eirZone.getCatContainerZone().getDescription();
            }

            return "UNKNOWN";

        } catch (Exception e) {
            log.warn("Error getting container status for container {}", container.getId(), e);
            return "UNKNOWN";
        }
    }

    private void sortResults(List<OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData> results,
                           ReferenceContainerLocation referenceLocation, boolean yardEnabled) {

        if (referenceLocation != null && yardEnabled) {
            // Sort by proximity to reference container, then by quantity removed, then by days of permanence
            results.sort(Comparator
                .comparing(OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData::getCercaniaAContenedor)
                .thenComparing(OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData::getCantidadRemovidos)
                .thenComparing(OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData::getDiasPermanencia));
        } else {
            // Group by block and calculate average days of permanence per block
            Map<String, Double> blockAverageDays = results.stream()
                .collect(Collectors.groupingBy(
                    OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData::getOrigenBloque,
                    Collectors.averagingInt(OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData::getDiasPermanencia)
                ));

            // Sort by block average days, then by quantity removed
            results.sort(Comparator
                .comparing((OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData data) ->
                    blockAverageDays.getOrDefault(data.getOrigenBloque(), 0.0))
                .thenComparing(OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData::getCantidadRemovidos));
        }
    }

    // Helper class for reference container location
    @lombok.Builder
    @lombok.Data
    private static class ReferenceContainerLocation {
        private String blockCode;
        private Integer columnIndex;
        private Integer rowIndex;
        private Integer levelIndex;
    }
}
