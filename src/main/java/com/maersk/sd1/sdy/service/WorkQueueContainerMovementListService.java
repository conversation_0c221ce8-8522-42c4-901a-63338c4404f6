package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.WorkQueueContainerMovement;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.sdy.dto.WorkQueueContainerMovementListInput;
import com.maersk.sd1.sdy.dto.WorkQueueContainerMovementListOutput;
import com.maersk.sd1.sdy.dto.WorkQueueContainerMovementListOutput.MovementItem;
import com.maersk.sd1.common.repository.WorkQueueContainerMovementRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class WorkQueueContainerMovementListService {

    private static final Logger logger = LogManager.getLogger(WorkQueueContainerMovementListService.class);
    private final WorkQueueContainerMovementRepository repository;

    public WorkQueueContainerMovementListOutput listMovements(WorkQueueContainerMovementListInput.Input input) {
        WorkQueueContainerMovementListOutput output = new WorkQueueContainerMovementListOutput();
       

        try {
            int page = (input.getPage() == null) ? 1 : input.getPage();
            int size = (input.getSize() == null) ? 10 : input.getSize();
            page = (page <= 0) ? 1 : page;
            size = (size <= 0) ? 10 : size;
            Pageable pageable = PageRequest.of(page - 1, size);

            Specification<WorkQueueContainerMovement> spec = buildSpecification(input);
            Page<WorkQueueContainerMovement> resultPage = repository.findAll(spec, pageable);

            List<MovementItem> items = new ArrayList<>();
            for (WorkQueueContainerMovement entity : resultPage.getContent()) {
                MovementItem item = new MovementItem();
                item.setQueueMovementId(entity.getId());
                if (entity.getWorkQueue() != null) {
                    item.setQueueId(entity.getWorkQueue().getId());
                    item.setQueueCode(entity.getWorkQueue().getCode());
                }
                if (entity.getCatMovementType() != null) {
                    item.setCatMovementTypeId(entity.getCatMovementType().getId());
                }
                if (entity.getCatOrigin() != null) {
                    item.setCatOriginId(entity.getCatOrigin().getId());
                }
                if (entity.getCatEmptyFull() != null) {
                    item.setCatEmptyFullId(entity.getCatEmptyFull().getId());
                }
                if (entity.getBlock() != null) {
                    item.setBlockId(entity.getBlock().getId());
                }
                item.setActive(entity.getActive());
                item.setRegistrationDate(entity.getRegistrationDate());
                item.setModificationDate(entity.getModificationDate());

                if (entity.getRegistrationUser() != null) {
                    User u = entity.getRegistrationUser();
                    item.setRegistrationUserId(u.getId());
                    item.setUserRegistrationNames(u.getNames());
                    // Combine paternal + maternal last names
                    StringBuilder sb = new StringBuilder();
                    sb.append((u.getFirstLastName() != null) ? u.getFirstLastName() : "");
                    if (u.getSecondLastName() != null && !u.getSecondLastName().isEmpty()) {
                        sb.append(" ").append(u.getSecondLastName());
                    }
                    item.setUserRegistrationLastNames(sb.toString().trim());
                }

                if (entity.getModificationUser() != null) {
                    User um = entity.getModificationUser();
                    item.setModificationUserId(um.getId());
                    item.setUserModificationNames(um.getNames());
                    StringBuilder sbMod = new StringBuilder();
                    sbMod.append((um.getFirstLastName() != null) ? um.getFirstLastName() : "");
                    if (um.getSecondLastName() != null && !um.getSecondLastName().isEmpty()) {
                        sbMod.append(" ").append(um.getSecondLastName());
                    }
                    item.setUserModificationLastNames(sbMod.toString().trim());
                }
                items.add(item);
            }

            output.setMovements(items);
            output.setTotalRegistros(List.of(List.of(resultPage.getTotalElements())));
            
        } catch (Exception e) {
            logger.error("Error listing WorkQueueContainerMovements", e);
            
        }

        return output;
    }

    private Specification<WorkQueueContainerMovement> buildSpecification(WorkQueueContainerMovementListInput.Input input) {
        return (root, query, cb) -> {
            List<jakarta.persistence.criteria.Predicate> predicates = new ArrayList<>();

            // Filter by queueMovementId
            if (input.getQueueMovementId() != null) {
                predicates.add(cb.equal(root.get("id"), input.getQueueMovementId()));
            }

            // Filter by queueId
            if (input.getQueueId() != null) {
                predicates.add(cb.equal(root.get("workQueue").get("id"), input.getQueueId()));
            }

            // Filter by catMovementTypeId
            if (input.getCatMovementTypeId() != null) {
                predicates.add(cb.equal(root.get("CatMovementType").get("id"), input.getCatMovementTypeId()));
            }

            // Filter by catOriginId
            if (input.getCatOriginId() != null) {
                predicates.add(cb.equal(root.get("catOrigin").get("id"), input.getCatOriginId()));
            }

            // Filter by catEmptyFullId
            if (input.getCatEmptyFullId() != null) {
                predicates.add(cb.equal(root.get("catEmptyFull").get("id"), input.getCatEmptyFullId()));
            }

            // Filter by blockId
            if (input.getBlockId() != null) {
                predicates.add(cb.equal(root.get("block").get("id"), input.getBlockId()));
            }

            // Filter by active
            if (input.getActive() != null) {
                predicates.add(cb.equal(root.get("active"), input.getActive()));
            }

            // Date range filter registrationDate
            LocalDate regMin = input.getRegistrationDateMin();
            LocalDate regMax = input.getRegistrationDateMax();
            if (regMin != null && regMax != null) {
                // registrationDate >= regMin and < regMax+1
                predicates.add(cb.greaterThanOrEqualTo(root.get("registrationDate"), regMin.atStartOfDay()));
                predicates.add(cb.lessThan(root.get("registrationDate"), regMax.plusDays(1).atStartOfDay()));
            }

            // Date range filter modificationDate
            LocalDate modMin = input.getModificationDateMin();
            LocalDate modMax = input.getModificationDateMax();
            if (modMin != null && modMax != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("modificationDate"), modMin.atStartOfDay()));
                predicates.add(cb.lessThan(root.get("modificationDate"), modMax.plusDays(1).atStartOfDay()));
            }

            // subBusinessUnitId -> yard.businessUnit.id
            if (input.getSubBusinessUnitId() != null) {
                predicates.add(cb.equal(root.get("yard").get("businessUnit").get("id"), input.getSubBusinessUnitId()));
            }

            // yard.active = true always
            predicates.add(cb.isTrue(root.get("yard").get("active")));

            query.orderBy(cb.desc(root.get("id")));
            return cb.and(predicates.toArray(new jakarta.persistence.criteria.Predicate[0]));
        };
    }
}
