package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.ContainerFilterRule;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.ContainerFilterRuleImoRepository;
import com.maersk.sd1.common.repository.ContainerFilterRuleRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.sdy.controller.dto.RuleContainerFilterListOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RuleContainerFilterListService {

    private static final Logger logger = LogManager.getLogger(RuleContainerFilterListService.class.getName());
    
    private final ContainerFilterRuleRepository containerFilterRuleRepository;
    private final ContainerFilterRuleImoRepository containerFilterRuleImoRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Transactional(readOnly = true)
    public RuleContainerFilterListOutput listRuleContainerFilters(
            Integer ruleContainerFilterId,
            String code,
            String name,
            String description,
            String containerNumber,
            Integer containerTare,
            Integer containerMaximumLoad,
            LocalDate containerManufactureDateMin,
            LocalDate containerManufactureDateMax,
            Integer catContainerFamilyId,
            Integer catContainerSizeId,
            Integer catContainerTypeId,
            Integer catContainerClassId,
            Integer catContainerTechnologyId,
            Integer catEngineBrandId,
            Character flagElectricalConnection,
            Character flagDangerousCargo,
            Character active,
            Integer isoCodeId,
            Integer businessUnitId,
            LocalDate registrationDateMin,
            LocalDate registrationDateMax,
            LocalDate modificationDateMin,
            LocalDate modificationDateMax,
            Integer registrationUserId,
            Integer modificationUserId,
            Integer userId,
            Integer page,
            Integer size) {

        RuleContainerFilterListOutput output = new RuleContainerFilterListOutput();
        
        try {
            // Convert Character to Boolean for JPA query
            Boolean flagElectricalConnectionBool = flagElectricalConnection != null ? 
                    flagElectricalConnection == '1' || flagElectricalConnection == 'Y' || flagElectricalConnection == 'y' : null;
            Boolean flagDangerousCargoBool = flagDangerousCargo != null ? 
                    flagDangerousCargo == '1' || flagDangerousCargo == 'Y' || flagDangerousCargo == 'y' : null;
            Boolean activeBool = active != null ? 
                    active == '1' || active == 'Y' || active == 'y' : null;
            
            // Convert LocalDate to LocalDateTime for JPA query
            LocalDateTime registrationDateMinTime = registrationDateMin != null ? 
                    LocalDateTime.of(registrationDateMin, LocalTime.MIN) : null;
            LocalDateTime registrationDateMaxTime = registrationDateMax != null ? 
                    LocalDateTime.of(registrationDateMax, LocalTime.MAX) : null;
            LocalDateTime modificationDateMinTime = modificationDateMin != null ? 
                    LocalDateTime.of(modificationDateMin, LocalTime.MIN) : null;
            LocalDateTime modificationDateMaxTime = modificationDateMax != null ? 
                    LocalDateTime.of(modificationDateMax, LocalTime.MAX) : null;
            
            // Get total count
            Integer total = containerFilterRuleRepository.countRuleContainerFilters(
                    ruleContainerFilterId,
                    code,
                    name,
                    description,
                    containerNumber,
                    containerTare,
                    containerMaximumLoad,
                    catContainerFamilyId,
                    catContainerSizeId,
                    catContainerTypeId,
                    catContainerClassId,
                    catContainerTechnologyId,
                    catEngineBrandId,
                    flagElectricalConnectionBool,
                    flagDangerousCargoBool,
                    activeBool,
                    isoCodeId,
                    businessUnitId,
                    registrationDateMinTime,
                    registrationDateMaxTime,
                    modificationDateMinTime,
                    modificationDateMaxTime,
                    registrationUserId,
                    modificationUserId);
            
            output.setTotal(List.of(List.of(0)));
            
            // If no results, return empty list
            if (total == 0) {
                output.setRules(new ArrayList<>());
                return output;
            }
            
            // Set default values for pagination
            int pageNum = page != null ? page : 1;
            int pageSize = size != null ? size : total;
            
            // Create pageable object
            Pageable pageable = PageRequest.of(pageNum - 1, pageSize);
            
            // Get paginated results
            Page<ContainerFilterRule> rules = containerFilterRuleRepository.findRuleContainerFilters(
                    ruleContainerFilterId,
                    code,
                    name,
                    description,
                    containerNumber,
                    containerTare,
                    containerMaximumLoad,
                    catContainerFamilyId,
                    catContainerSizeId,
                    catContainerTypeId,
                    catContainerClassId,
                    catContainerTechnologyId,
                    catEngineBrandId,
                    flagElectricalConnectionBool,
                    flagDangerousCargoBool,
                    activeBool,
                    isoCodeId,
                    businessUnitId,
                    registrationDateMinTime,
                    registrationDateMaxTime,
                    modificationDateMinTime,
                    modificationDateMaxTime,
                    registrationUserId,
                    modificationUserId,
                    pageable);
            
            // Map to DTOs
            List<RuleContainerFilterListOutput.RuleContainerFilterDTO> ruleDTOs = rules.getContent().stream()
                    .map(rule -> {
                        RuleContainerFilterListOutput.RuleContainerFilterDTO dto = RuleContainerFilterListOutput.RuleContainerFilterDTO.builder()
                                .ruleContainerFilterId(rule.getId())
                                .code(rule.getCode())
                                .name(rule.getName())
                                .description(rule.getDescription())
                                .containerNumber(rule.getContainerNumber())
                                .containerTare(rule.getContainerTare())
                                .containerMaximumLoad(rule.getContainerMaximumLoad())
                                .containerManufactureDate(rule.getContainerManufactureDate() != null ? 
                                        rule.getContainerManufactureDate().toString() : null)
                                .catContainerFamilyId(rule.getCatContainerFamily() != null ? 
                                        rule.getCatContainerFamily().getId() : null)
                                .catContainerSizeId(rule.getCatContainerSize() != null ? 
                                        rule.getCatContainerSize().getId() : null)
                                .catContainerTypeId(rule.getCatContainerType() != null ? 
                                        rule.getCatContainerType().getId() : null)
                                .catContainerClassId(rule.getCatContainerClass() != null ? 
                                        rule.getCatContainerClass().getId() : null)
                                .catContainerTechnologyId(rule.getCatContainerTechnology() != null ? 
                                        rule.getCatContainerTechnology().getId() : null)
                                .catEngineBrandId(rule.getCatEngineBrand() != null ? 
                                        rule.getCatEngineBrand().getId() : null)
                                .flagElectricalConnection(rule.getFlagElectricalConnection())
                                .flagDangerousCargo(rule.getFlagDangerousCharge())
                                .active(rule.getActive())
                                .isoCodeId(rule.getIsoCode() != null ? rule.getIsoCode().getId() : null)
                                .businessUnitId(rule.getBusinessUnit() != null ? rule.getBusinessUnit().getId() : null)
                                .registrationDate(rule.getRegistrationDate() != null ? 
                                        rule.getRegistrationDate().toString() : null)
                                .registrationUserId(rule.getRegistrationUser() != null ? 
                                        rule.getRegistrationUser().getId() : null)
                                .registrationUserName(rule.getRegistrationUser() != null ? 
                                        rule.getRegistrationUser().getNames() : null)
                                .registrationUserLastName(rule.getRegistrationUser() != null ? 
                                        rule.getRegistrationUser().getFirstLastName() : null)
                                .modificationDate(rule.getModificationDate() != null ? 
                                        rule.getModificationDate().toString() : null)
                                .modificationUserId(rule.getModificationUser() != null ? 
                                        rule.getModificationUser().getId() : null)
                                .modificationUserName(rule.getModificationUser() != null ? 
                                        rule.getModificationUser().getNames() : null)
                                .modificationUserLastName(rule.getModificationUser() != null ? 
                                        rule.getModificationUser().getFirstLastName() : null)
                                .build();
                        
                        // Get IMOs as JSON
                        try {
                            String imos = containerFilterRuleImoRepository.findImosJsonByRuleContainerFilterId(rule.getId());
                            dto.setImos(imos);
                        } catch (Exception e) {
                            logger.error("Error getting IMOs for rule container filter ID: " + rule.getId(), e);
                            dto.setImos("[]");
                        }
                        
                        return dto;
                    })
                    .collect(Collectors.toList());
            
            output.setRules(ruleDTOs);
            
        } catch (Exception e) {
            logger.error("Error listing rule container filters", e);
            output.setTotal(List.of(List.of(0)));
            output.setRules(new ArrayList<>());
        }
        
        return output;
    }
}
