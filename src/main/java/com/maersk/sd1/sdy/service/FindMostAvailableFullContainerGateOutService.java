package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdy.dto.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Log4j2
@RequiredArgsConstructor
@Service
public class FindMostAvailableFullContainerGateOutService {

    private final CatalogRepository catalogRepository;
    private final CargoDocumentRepository cargoDocumentRepository;
    private final ContainerRestrictionRepository containerRestrictionRepository;
    private final MovementInstructionRepository movementInstructionRepository;
    private final ContainerLocationRepository containerLocationRepository;
    private final BlockRepository blockRepository;
    private final VesselProgrammingDetailRepository vesselProgrammingDetailRepository;
    private final EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;

    @Transactional(readOnly = true)
    public FindMostAvailableFullContainerGateOutOutputDTO findContainers(Integer documentoCargoId, Integer pickUpQuantity) {
        log.info("Starting findContainers with documentoCargoId={}, pickUpQuantity={}", documentoCargoId, pickUpQuantity);

        // Step 1: Get catalog IDs
        Map<String, Integer> catalogMap = getCatalogMap();
        CatalogIds catalogIds = extractCatalogIds(catalogMap);

        // Step 2: Get business unit IDs
        BusinessUnitIds businessUnitIds = getBusinessUnitIds(documentoCargoId);
        if (businessUnitIds == null) {
            log.warn("No business unit IDs found for documentoCargoId={}", documentoCargoId);
            return new FindMostAvailableFullContainerGateOutOutputDTO();
        }

        // Step 3: Get initial container data
        List<ContainerFullInitialData> initialDataList = getInitialContainerData(
                documentoCargoId,
                catalogIds.gateoutPlanningProgrammedState);

        if (CollectionUtils.isEmpty(initialDataList)) {
            log.warn("No initial container data found for documentoCargoId={}", documentoCargoId);
            return new FindMostAvailableFullContainerGateOutOutputDTO();
        }

        // Step 4: Filter out containers with restrictions
        Set<Integer> containerIds = initialDataList.stream()
                .map(ContainerFullInitialData::getContainerId)
                .collect(Collectors.toSet());

        // Step 4.1: Remove containers with active restrictions
        filterContainersWithRestrictions(containerIds, businessUnitIds.subBusinessUnitId, catalogIds.isFull);

        // Step 4.2: Remove containers without active locations
        filterContainersWithoutLocations(containerIds);

        // Step 4.3: Remove containers with pending movement instructions
        filterContainersWithPendingInstructions(containerIds, catalogIds);

        if (containerIds.isEmpty()) {
            log.warn("No valid containers found after filtering");
            return new FindMostAvailableFullContainerGateOutOutputDTO();
        }

        // Step 5: Update initial data list to only include valid containers
        initialDataList = initialDataList.stream()
                .filter(data -> containerIds.contains(data.getContainerId()))
                .collect(Collectors.toList());

        // Step 6: Get container locations and calculate lift moves
        List<ContainerLocationDTO> containerLocations = getContainerLocations(
                new ArrayList<>(containerIds),
                businessUnitIds.localSubBusinessUnitId);

        if (CollectionUtils.isEmpty(containerLocations)) {
            log.warn("No container locations found");
            return new FindMostAvailableFullContainerGateOutOutputDTO();
        }

        // Step 7: Calculate lift moves
        Map<Integer, Integer> containerLiftMoves = calculateLiftMoves(containerLocations, new ArrayList<>(containerIds));

        // Step 8: Build final result
        List<ContainerFullInitialData> sortedContainers = sortContainers(
                initialDataList, containerLiftMoves, containerLocations);

        // Step 9: Take top N containers based on pickup quantity
        List<ContainerFullInitialData> selectedContainers = sortedContainers.stream()
                .limit(pickUpQuantity)
                .toList();

        // Step 10: Build output DTO
        return buildOutputDTO(selectedContainers, containerLiftMoves);
    }

    // Helper methods will be implemented below

    private Map<String, Integer> getCatalogMap() {
        List<String> aliases = List.of(
                "sd1_full_box_machinery_condition_ok",
                "43084", // is_full
                "sd1_equipment_category_container",
                "42895", // movement_instruction_created
                "42899", // movement_instruction_on_hold
                "42898", // movement_instruction_in_progress
                "42897", // movement_instruction_to_be_attended
                "cat_48746_programm" // gateout_planning_programmed_state
        );

        return catalogRepository.findIdsByAliases(aliases).stream()
                .collect(Collectors.toMap(
                        obj -> (String) obj[0],
                        obj -> (Integer) obj[1]
                ));
    }

    private CatalogIds extractCatalogIds(Map<String, Integer> catalogMap) {
        return new CatalogIds(
                catalogMap.get("sd1_full_box_machinery_condition_ok"),
                catalogMap.get("43084"), // is_full
                catalogMap.get("sd1_equipment_category_container"),
                catalogMap.get("42895"), // movement_instruction_created
                catalogMap.get("42899"), // movement_instruction_on_hold
                catalogMap.get("42898"), // movement_instruction_in_progress
                catalogMap.get("42897"), // movement_instruction_to_be_attended
                catalogMap.get("cat_48746_programm") // gateout_planning_programmed_state
        );
    }

    private BusinessUnitIds getBusinessUnitIds(Integer documentoCargoId) {
        // Get business unit IDs from cargo document - equivalent to SQL lines 87-95
        Long subBusinessUnitId = cargoDocumentRepository.findBusinessUnitId(documentoCargoId);
        if (subBusinessUnitId == null) {
            log.warn("No business unit found for documentoCargoId={}", documentoCargoId);
            return null;
        }

        // Get local sub business unit ID from vessel programming
        Integer vesselProgrammingDetailId = cargoDocumentRepository.findVesselProgrammingDetailId(documentoCargoId);
        if (vesselProgrammingDetailId == null) {
            log.warn("No vessel programming detail found for documentoCargoId={}", documentoCargoId);
            return null;
        }

        Integer localSubBusinessUnitId = vesselProgrammingDetailRepository.findLocalSubBusinessUnitId(vesselProgrammingDetailId);
        if (localSubBusinessUnitId == null) {
            log.warn("No local sub business unit found for vesselProgrammingDetailId={}", vesselProgrammingDetailId);
            // Fallback to using the same business unit ID
            localSubBusinessUnitId = subBusinessUnitId.intValue();
        }

        return new BusinessUnitIds(subBusinessUnitId.intValue(), localSubBusinessUnitId);
    }

    private List<ContainerFullInitialData> getInitialContainerData(Integer documentoCargoId, Integer gateoutPlanningProgrammedState) {
        // Equivalent to SQL lines 97-138 - complex join to get initial container data
        try {
            return eirDocumentCargoDetailRepository.findInitialContainerDataForFull(
                    documentoCargoId
            );
        } catch (Exception e) {
            log.error("Error retrieving initial container data for documentoCargoId={}, gateoutPlanningProgrammedState={}",
                    documentoCargoId, gateoutPlanningProgrammedState, e);
            return new ArrayList<>();
        }
    }

    private void filterContainersWithRestrictions(Set<Integer> containerIds, Integer subBusinessUnitId, Integer isFull) {
        List<Integer> containersWithRestrictions = containerRestrictionRepository
                .findContainersWithActiveRestrictionsFull(subBusinessUnitId, isFull);

        if (!CollectionUtils.isEmpty(containersWithRestrictions)) {
            containersWithRestrictions.forEach(containerIds::remove);
        }
    }

    private void filterContainersWithoutLocations(Set<Integer> containerIds) {
        List<Integer> containersWithLocations = containerLocationRepository
                .findContainersWithActiveLocations(new ArrayList<>(containerIds))
                .orElse(List.of());

        containerIds.retainAll(new HashSet<>(containersWithLocations));
    }

    private void filterContainersWithPendingInstructions(Set<Integer> containerIds, CatalogIds catalogIds) {
        List<Integer> pendingStatuses = List.of(
                catalogIds.movementCreatedInstruction,
                catalogIds.movementOnHoldInstruction,
                catalogIds.movementInProgressInstruction,
                catalogIds.movementInstructionToBeAttended
        );

        List<Integer> containersWithPendingInstructions = movementInstructionRepository
                .findContainersWithPendingInstructions(pendingStatuses);

        if (!CollectionUtils.isEmpty(containersWithPendingInstructions)) {
            containersWithPendingInstructions.forEach(containerIds::remove);
        }
    }

    private List<ContainerLocationDTO> getContainerLocations(List<Integer> containerIds, Integer localSubBusinessUnitId) {
        return containerLocationRepository.findContainerLocationsForFull(containerIds, localSubBusinessUnitId);
    }

    private Map<Integer, Integer> calculateLiftMoves(List<ContainerLocationDTO> containerLocations, List<Integer> containerIds) {
        // 3.2 & 3.3 SET DATA OF ROWS OCCUPIED BY EACH CONTAINER AND AGGREGATE THEM
        Map<Integer, String> containerRowsMap = containerLocations.stream()
                .collect(Collectors.groupingBy(
                        ContainerLocationDTO::getContainerId,
                        Collectors.mapping(
                                ContainerLocationDTO::getRowIndex,
                                Collectors.collectingAndThen(
                                        Collectors.toSet(),
                                        rows -> rows.stream()
                                                .map(String::valueOf)
                                                .collect(Collectors.joining(","))
                                )
                        )
                ));

        // 3.4 SET DATA OF ABSOLUTE FIELDS FOR EACH LOCATIONS
        Map<Integer, List<ContainerAbsoluteLocation>> containerAbsoluteLocations = containerLocations.stream()
                .collect(Collectors.groupingBy(
                        ContainerLocationDTO::getContainerId,
                        Collectors.mapping(
                                loc -> new ContainerAbsoluteLocation(
                                        loc.getBlockId(),
                                        loc.getColumnIndex(),
                                        loc.getLevelIndex()
                                ),
                                Collectors.toList()
                        )
                ));

        // 3.5 GET DATA OF THE BLOCKS IN WHICH THE SELECTED CONTAINERS ARE STORED
        Set<Integer> occupiedBlockIds = containerLocations.stream()
                .map(ContainerLocationDTO::getBlockId)
                .collect(Collectors.toSet());

        // Get block stacking info
        Map<Integer, BlockStackingInfo> blockStackingInfoMap = findBlockStackingInfo(new ArrayList<>(occupiedBlockIds));

        // Create a map of container ID to block ID and stacking info
        Map<Integer, BlockStackingInfo> occupiedBlocksMap = new HashMap<>();
        for (ContainerLocationDTO loc : containerLocations) {
            BlockStackingInfo blockInfo = blockStackingInfoMap.get(loc.getBlockId());
            if (blockInfo != null) {
                occupiedBlocksMap.put(loc.getContainerId(), new BlockStackingInfo(
                        loc.getBlockId(),
                        blockInfo.getBlockStackingSides(),
                        blockInfo.getBlockStackingDirection()
                ));
            }
        }

        // 3.6 SET DATA OF ROWS OCCUPIED PER EACH OCCUPIED BLOCK
        Map<Integer, Set<Integer>> rowsOccupiedPerBlock = containerLocations.stream()
                .collect(Collectors.groupingBy(
                        ContainerLocationDTO::getBlockId,
                        Collectors.mapping(
                                ContainerLocationDTO::getRowIndex,
                                Collectors.toSet()
                        )
                ));

        // 3.7 GET DATA OF THE FIRST LOCATION OCCUPIED BY THE BLOCKING CONTAINERS
        Map<Integer, List<ContainerLocationDTO>> blockingContainers = new HashMap<>();
        for (Map.Entry<Integer, Set<Integer>> entry : rowsOccupiedPerBlock.entrySet()) {
            Integer blockId = entry.getKey();
            Set<Integer> rowIndices = entry.getValue();

            List<ContainerLocationDTO> locations = containerLocationRepository
                    .findBlockingContainersLocationsByBlockAndRows(blockId, rowIndices);

            blockingContainers.put(blockId, locations);
        }

        // 3.8 CALCULATE THE NUMBER OF BLOCKING CONTAINERS FOR EACH SELECTED CONTAINER
        Map<Integer, LiftMovesComponents> containerLiftMovesComponents = new HashMap<>();

        for (Integer containerId : containerIds) {
            // Skip if container doesn't have absolute locations
            List<ContainerAbsoluteLocation> locations = containerAbsoluteLocations.get(containerId);
            String rowsStr = containerRowsMap.getOrDefault(containerId, "");
            BlockStackingInfo blockInfo = occupiedBlocksMap.get(containerId);
            boolean shouldProcess = locations != null && !locations.isEmpty()
                    && !rowsStr.isEmpty()
                    && blockInfo != null;
            if (shouldProcess) {
                ContainerAbsoluteLocation location = locations.getFirst();
                int blockId = location.getBlockId();
                int columnIndex = location.getColumnIndex();
                int levelIndex = location.getLevelIndex();

                // Get rows for this container
                Set<Integer> containerRows = Arrays.stream(rowsStr.split(","))
                        .filter(s -> !s.isEmpty())
                        .map(Integer::parseInt)
                        .collect(Collectors.toSet());

                // Get blocking containers for this block
                List<ContainerLocationDTO> locationsInBlock = blockingContainers.getOrDefault(blockId, Collections.emptyList());

                // Calculate up lift moves (containers above)
                int upLiftMoves = (int) locationsInBlock.stream()
                        .filter(loc -> containerRows.contains(loc.getRowIndex()) &&
                                loc.getColumnIndex() == columnIndex &&
                                loc.getLevelIndex() > levelIndex)
                        .map(ContainerLocationDTO::getContainerId)
                        .distinct()
                        .count();

                // Calculate left lift moves (containers to the left)
                int leftLiftMoves = (int) locationsInBlock.stream()
                        .filter(loc -> containerRows.contains(loc.getRowIndex()) &&
                                loc.getColumnIndex() < columnIndex)
                        .map(ContainerLocationDTO::getContainerId)
                        .distinct()
                        .count();

                // Calculate right lift moves (containers to the right)
                int rightLiftMoves = (int) locationsInBlock.stream()
                        .filter(loc -> containerRows.contains(loc.getRowIndex()) &&
                                loc.getColumnIndex() > columnIndex)
                        .map(ContainerLocationDTO::getContainerId)
                        .distinct()
                        .count();

                containerLiftMovesComponents.put(containerId, new LiftMovesComponents(upLiftMoves, leftLiftMoves, rightLiftMoves));
            }
        }

        // 4.1 CALCULATE THE LIFT MOVES FOR EACH CONTAINER BASED ON THE BLOCK STACKING DATA
        Map<Integer, Integer> containerLiftMoves = new HashMap<>();

        for (Integer containerId : containerIds) {
            // Skip if container doesn't have lift moves components
            LiftMovesComponents components = containerLiftMovesComponents.get(containerId);
            // Skip if container doesn't have block info
            BlockStackingInfo blockInfo = occupiedBlocksMap.get(containerId);
            boolean shouldSkip = components == null || blockInfo == null;

            // Calculate final lift moves using the same logic as in SQL
            if (!shouldSkip) {
                // Calculate final lift moves using the same logic as in SQL
                int finalLiftMoves = getFinalLiftMoves(blockInfo, components);

                containerLiftMoves.put(containerId, finalLiftMoves);
            }
        }

        return containerLiftMoves;
    }

    private int getFinalLiftMoves(BlockStackingInfo blockInfo, LiftMovesComponents components) {
        int finalLiftMoves;
        if (Boolean.TRUE.equals(blockInfo.getBlockStackingSides())) {
            finalLiftMoves = components.getUpLiftMoves() +
                    Math.min(components.getLeftLiftMoves(), components.getRightLiftMoves());
        } else if (Boolean.TRUE.equals(blockInfo.getBlockStackingDirection())) {
            finalLiftMoves = components.getUpLiftMoves() + components.getLeftLiftMoves();
        } else if (Boolean.FALSE.equals(blockInfo.getBlockStackingDirection())) {
            finalLiftMoves = components.getUpLiftMoves() + components.getRightLiftMoves();
        } else {
            finalLiftMoves = components.getUpLiftMoves() + components.getLeftLiftMoves();
        }
        return finalLiftMoves;
    }

    private Map<Integer, BlockStackingInfo> findBlockStackingInfo(List<Integer> blockIds) {
        return blockRepository.findBlockStackingInfoList(blockIds).stream()
                .collect(Collectors.toMap(
                        BlockStackingInfo::getBlockId,
                        Function.identity()
                ));
    }

    private List<ContainerFullInitialData> sortContainers(
            List<ContainerFullInitialData> containers,
            Map<Integer, Integer> containerLiftMoves,
            List<ContainerLocationDTO> containerLocations) {

        // Create a map of container ID to highest level (equivalent to CROSS APPLY in SQL)
        Map<Integer, Integer> containerHighestLevel = containerLocations.stream()
                .collect(Collectors.groupingBy(
                        ContainerLocationDTO::getContainerId,
                        Collectors.mapping(
                                ContainerLocationDTO::getLevelIndex,
                                Collectors.maxBy(Integer::compare)
                        )
                ))
                .entrySet().stream()
                .filter(e -> e.getValue().isPresent())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> e.getValue().get()
                ));

        // Sort containers based on criteria from stored procedure
        // This replicates the ORDER BY clause in the SQL
        return containers.stream()
                .sorted((c1, c2) -> {
                    // 1. TRES.lift_moves ASC - Lift moves (ascending)
                    int liftMoves1 = containerLiftMoves.getOrDefault(c1.getContainerId(), 0);
                    int liftMoves2 = containerLiftMoves.getOrDefault(c2.getContainerId(), 0);
                    int liftComp = Integer.compare(liftMoves1, liftMoves2);
                    if (liftComp != 0) return liftComp;

                    // 2. LVL.level_index DESC - Level (descending)
                    int level1 = containerHighestLevel.getOrDefault(c1.getContainerId(), 0);
                    int level2 = containerHighestLevel.getOrDefault(c2.getContainerId(), 0);
                    int levelComp = Integer.compare(level2, level1);  // Note: reversed for descending order
                    if (levelComp != 0) return levelComp;

                    // 3. TCID.truck_arrival_date ASC - Truck arrival date (ascending)
                    return c1.getTruckArrivalDate().compareTo(c2.getTruckArrivalDate());
                })
                .collect(Collectors.toList());
    }

    private FindMostAvailableFullContainerGateOutOutputDTO buildOutputDTO(
            List<ContainerFullInitialData> containers,
            Map<Integer, Integer> containerLiftMoves) {

        FindMostAvailableFullContainerGateOutOutputDTO outputDTO = new FindMostAvailableFullContainerGateOutOutputDTO();
        List<FindMostAvailableFullContainerGateOutOutputDTO.ContainerGateOutItem> items = new ArrayList<>();

        for (ContainerFullInitialData container : containers) {
            FindMostAvailableFullContainerGateOutOutputDTO.ContainerGateOutItem item =
                    new FindMostAvailableFullContainerGateOutOutputDTO.ContainerGateOutItem();
            item.setContainerId(container.getContainerId());
            item.setContainerNumber(container.getContainerNumber());
            item.setContainerLocationId(0); // Default to 0 as per stored procedure
            item.setLiftMoves(containerLiftMoves.getOrDefault(container.getContainerId(), 0));
            items.add(item);
        }

        outputDTO.setContainers(items);
        return outputDTO;
    }

    private record CatalogIds(
            Integer isFullConditionOk,
            Integer isFull,
            Integer isContainer,
            Integer movementCreatedInstruction,
            Integer movementOnHoldInstruction,
            Integer movementInProgressInstruction,
            Integer movementInstructionToBeAttended,
            Integer gateoutPlanningProgrammedState
    ) {
    }

    private record BusinessUnitIds(
            Integer subBusinessUnitId,
            Integer localSubBusinessUnitId
    ) {
    }
}
