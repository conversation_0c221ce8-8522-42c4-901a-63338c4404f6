package com.maersk.sd1.sdy.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.repository.SystemRuleRepository;
import com.maersk.sd1.common.model.SystemRule;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@AllArgsConstructor
public class CheckYardIntegrationService {

    private final SystemRuleRepository systemRuleRepository;

    public boolean checkYardIntegration(String subBusinessUnitLocalAlias, String typeProcess) throws JsonProcessingException {
        SystemRule ruleIntegrationSDY = systemRuleRepository.findByAliasAndActiveTrue("sd1_rule_integration_sdy");
        ObjectMapper objectMapper = new ObjectMapper();

        if(ruleIntegrationSDY == null) {
            return false;
        }
        // Read JSON file and convert to Map list
        List<Map<String, Object>> list = objectMapper.readValue(ruleIntegrationSDY.getRule(), List.class);

        for (Map<String, Object> item : list) {
            // Get JSON values
            String jsonAlias = (String) item.get("sub_business_unit_local_alias");
            String jsonTypeProcess = (String) item.get("type_process");

            // Return true if business unit local and typeProcess
            if (jsonAlias.equals(subBusinessUnitLocalAlias) && jsonTypeProcess.equals(typeProcess)) {
                // If a match is found
                return true; // Corresponds to @finded = 1
            }
        }
        return false; // Corresponds to @finded = 0
    }

}
