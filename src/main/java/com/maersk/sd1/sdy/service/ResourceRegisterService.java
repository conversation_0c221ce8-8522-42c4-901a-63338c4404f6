package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.common.repository.ResourceRepository;
import com.maersk.sd1.sdy.controller.dto.ResourceRegisterInput;
import com.maersk.sd1.sdy.controller.dto.ResourceRegisterOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@RequiredArgsConstructor
@Service
public class ResourceRegisterService {

    private static final Logger logger = LogManager.getLogger(ResourceRegisterService.class);

    private final ResourceRepository resourceRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Transactional
    public ResourceRegisterOutput registerResource(ResourceRegisterInput.Root request) {
        ResourceRegisterInput.Input input = request.getPrefix().getInput();
        ResourceRegisterOutput output = new ResourceRegisterOutput();

        logger.info("Starting resource registration for code: {}", input.getCodigo());

        try {
            // Build the Resource entity from request
            Resource newResource = new Resource();

            // catResource (FK to Catalog)
            if (input.getCatRecursoId() != null) {
                Catalog catResource = new Catalog();
                catResource.setId(input.getCatRecursoId());
                newResource.setCatResource(catResource);
            }

            // catContainerLoad (FK to Catalog)
            if (input.getCatCargaContenedorId() != null) {
                Catalog catContainerLoad = new Catalog();
                catContainerLoad.setId(input.getCatCargaContenedorId());
                newResource.setCatContainerLoad(catContainerLoad);
            }

            // yard (patio_id) (FK to Yard)
            if (input.getPatioId() != null) {
                Yard yard = new Yard();
                yard.setId(input.getPatioId());
                newResource.setYard(yard);
            }

            // Basic properties
            newResource.setCode(input.getCodigo());
            newResource.setName(input.getNombre());
            newResource.setBrand(input.getMarca());
            newResource.setMaximumLevelStacking(input.getMaximoNivelApilamiento());

            // Convert active string to boolean
            if (input.getActivo() != null) {
                newResource.setActive("true".equalsIgnoreCase(input.getActivo()) || "1".equals(input.getActivo()));
            }

            // userRegistrationId (FK to User)
            if (input.getUsuarioRegistroId() != null) {
                User registrationUser = new User();
                registrationUser.setId(input.getUsuarioRegistroId());
                newResource.setRegistrationUser(registrationUser);
            }

            // registration date
            newResource.setRegistrationDate(LocalDateTime.now());

            // workPoint (FK to WorkPoint)
            if (input.getPuntoTrabajoId() != null) {
                WorkPoint workPoint = new WorkPoint();
                workPoint.setId(input.getPuntoTrabajoId());
                newResource.setWorkPoint(workPoint);
            }

            // businessUnit (FK to BusinessUnit)
            if (input.getUnidadNegocioId() != null) {
                BusinessUnit businessUnit = new BusinessUnit();
                businessUnit.setId(input.getUnidadNegocioId());
                newResource.setBusinessUnit(businessUnit);
            }

            // Let the database handle ID generation
            // Save resource
            Resource savedResource = resourceRepository.save(newResource);
            logger.info("Resource saved successfully with ID: {}", savedResource.getId());

            // Set successful outcome
            output.setRespEstado(1);

            // Get translated message
            Integer languageId = (input.getIdiomaId() == null) ? 1 : input.getIdiomaId();
            String translatedMessage = messageLanguageRepository.fnTranslatedMessage("GENERAL", 9, languageId);
            output.setRespMensaje(translatedMessage);
            output.setRespNewId(savedResource.getId());

        } catch (Exception e) {
            logger.error("Error occurred while registering resource: {}", e.getMessage(), e);
            output.setRespEstado(0);
            output.setRespNewId(0);
            output.setRespMensaje(e.getMessage());
        }

        return output;
    }
}
