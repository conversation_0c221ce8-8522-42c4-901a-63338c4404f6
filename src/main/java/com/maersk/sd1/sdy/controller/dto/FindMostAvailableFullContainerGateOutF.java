package com.maersk.sd1.sdy.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class FindMostAvailableFullContainerGateOutF {

    @Data
    public static class Input {
        @JsonProperty("documento_carga_id")
        private Integer documentoCargoId;
        
        @JsonProperty("pick_up_quantity")
        private Integer pickUpQuantity;
    }
    
    @Data
    public static class Output {
        @JsonProperty("containers")
        private java.util.List<ContainerGateOutItem> containers;
        
        @Data
        public static class ContainerGateOutItem {
            @JsonProperty("container_id")
            private Integer containerId;
            
            @JsonProperty("container_number")
            private String containerNumber;
            
            @JsonProperty("container_location_id")
            private Integer containerLocationId;
            
            @JsonProperty("lift_moves")
            private Integer liftMoves;
        }
    }
}
