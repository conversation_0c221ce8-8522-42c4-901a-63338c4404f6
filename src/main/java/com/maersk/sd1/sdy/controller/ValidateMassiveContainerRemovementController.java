package com.maersk.sd1.sdy.controller;

import com.maersk.sd1.sdy.dto.ValidateMassiveContainerRemovementInput;
import com.maersk.sd1.sdy.dto.ValidateMassiveContainerRemovementOutput;
import com.maersk.sd1.sdy.service.ValidateMassiveContainerRemovementService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import jakarta.validation.Valid;

import java.util.List;

/**
 * Controller that exposes an endpoint for validating massive container removement,
 * translated from the stored procedure logic.
 */
@RestController
@RequestMapping("/ModuleSDY/module/sdy/SDYContainerLoadServiceImp")
public class ValidateMassiveContainerRemovementController {

    private static final Logger logger = LogManager.getLogger(ValidateMassiveContainerRemovementController.class);

    @Autowired
    private ValidateMassiveContainerRemovementService service;

    @PostMapping("/sdyvalidateMassiveContainerRemovement")
    public ResponseEntity<ResponseController<List<ValidateMassiveContainerRemovementOutput>>> validateMassiveContainerRemoval(
            @RequestBody @Valid ValidateMassiveContainerRemovementInput.Root request) {
        try {
            ValidateMassiveContainerRemovementInput.Input input = request.getPrefix().getInput();

            List<ValidateMassiveContainerRemovementOutput> output = service.validateMassiveContainerRemovement(
                    input.getSubBusinessUnitLocalId(),
                    input.getLanguageId(),
                    input.getContainers()
            );

            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing validateMassiveContainerRemoval.", e);

            // Construct a response with error info
            ValidateMassiveContainerRemovementOutput output = new ValidateMassiveContainerRemovementOutput();
            // We can leave the container list empty or null to indicate an error.
            return ResponseEntity.internalServerError().body(new ResponseController<>(List.of(output)));
        }
    }
}

