package com.maersk.sd1.sdy.controller;

import com.maersk.sd1.sdy.controller.dto.FindMostAvailableFullContainerGateOutF;
import com.maersk.sd1.sdy.service.FindMostAvailableFullContainerGateOutService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/ModuleSDY/module/sdy")
public class FindMostAvailableFullContainerGateOutController {

    private final FindMostAvailableFullContainerGateOutService service;

    @PostMapping("/find-most-available-full-container-gateout")
    public ResponseEntity<FindMostAvailableFullContainerGateOutF.Output> findMostAvailableFullContainerGateOut(
            @RequestBody FindMostAvailableFullContainerGateOutF.Input input) {
        
        FindMostAvailableFullContainerGateOutF.Output output = new FindMostAvailableFullContainerGateOutF.Output();
        output.setContainers(service.findContainers(input.getDocumentoCargoId(), input.getPickUpQuantity())
                .getContainers().stream()
                .map(item -> {
                    FindMostAvailableFullContainerGateOutF.Output.ContainerGateOutItem outputItem = 
                            new FindMostAvailableFullContainerGateOutF.Output.ContainerGateOutItem();
                    outputItem.setContainerId(item.getContainerId());
                    outputItem.setContainerNumber(item.getContainerNumber());
                    outputItem.setContainerLocationId(item.getContainerLocationId());
                    outputItem.setLiftMoves(item.getLiftMoves());
                    return outputItem;
                })
                .toList());
        
        return ResponseEntity.ok(output);
    }
}
