package com.maersk.sd1.sdy.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdy.dto.PlanificacionContenedorBuscarInput;
import com.maersk.sd1.sdy.dto.PlanificacionContenedorBuscarPaginatedResponse;
import com.maersk.sd1.sdy.service.PlanificacionContenedorBuscarService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDY/moduleYard/sdy/SDYBuscarContenedorServiceImp")
public class PlanificacionContenedorBuscarController {

    private static final Logger logger = LogManager.getLogger(PlanificacionContenedorBuscarController.class);

    private final PlanificacionContenedorBuscarService planificacionContenedorBuscarService;

    @PostMapping("/sdybuscarContenedorListar")
    public ResponseEntity<ResponseController<PlanificacionContenedorBuscarPaginatedResponse>> planificacionContenedorBuscar(
            @RequestBody @Valid PlanificacionContenedorBuscarInput.Root request) {
        try {
            logger.info("Request received for planificacionContenedorBuscar: {}", request);
            
            if (request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                logger.error("Invalid request structure");
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid request structure"));
            }
            
            PlanificacionContenedorBuscarInput.Input input = request.getPrefix().getInput();
            
            PlanificacionContenedorBuscarPaginatedResponse response = planificacionContenedorBuscarService.buscarContenedores(
                    input.getNumeroContendores(),
                    input.getCatFamiliaId(),
                    input.getCatTamanoId(),
                    input.getCatTipoContenedorId(),
                    input.getLineaNavieraId(),
                    input.getNaveId(),
                    input.getCatClaseId(),
                    input.getCatTipoReeferId(),
                    input.getGroupCode(),
                    input.getCatCategoriaId(),
                    input.getNumeroDocumentoIngreso(),
                    input.getNumeroDocumentoSalida(),
                    input.getCargaMaxima(),
                    input.getUnidadNegocioId(),
                    input.getPage(),
                    input.getSize(),
                    input.getLanguageId(),
                    input.getOwner(),
                    input.getEmrCondition()
            );
            
            return ResponseEntity.ok(new ResponseController<>(response));
        } catch (Exception e) {
            logger.error("Error processing planificacionContenedorBuscar request", e);
            return ResponseEntity.status(500).body(new ResponseController<>(e.getMessage()));
        }
    }
}
