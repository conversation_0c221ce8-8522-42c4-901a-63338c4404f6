package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.Eir;
import com.maersk.sd1.common.model.Person;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sde.controller.dto.ListPhotosAndContainerOutput;
import com.maersk.sd1.sde.controller.dto.ListPhotosAndContainerOutput.EirContainerResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;
@Service
public class ListPhotosAndContainerService {

    private static final Logger logger = LogManager.getLogger(ListPhotosAndContainerService.class);

    private final EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;
    private final BusinessUnitConfigRepository businessUnitConfigRepository;
    private final EirRepository eirRepository;
    private final ContainerRepository containerRepository;
    private final BusinessUnitRepository businessUnitRepository;

    public ListPhotosAndContainerService(
            EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository,
            BusinessUnitConfigRepository businessUnitConfigRepository,
            @Qualifier("eirRepository")EirRepository eirRepository,
            ContainerRepository containerRepository,
            BusinessUnitRepository businessUnitRepository) {
        this.eirDocumentCargoDetailRepository = eirDocumentCargoDetailRepository;
        this.businessUnitConfigRepository = businessUnitConfigRepository;
        this.eirRepository = eirRepository;
        this.containerRepository = containerRepository;
        this.businessUnitRepository = businessUnitRepository;
    }

    @Transactional(readOnly = true)
    public ListPhotosAndContainerOutput getFotosEirContenedor(Integer subBusinessUnitId2, String containerEir, Integer languageId) {
        ListPhotosAndContainerOutput output = new ListPhotosAndContainerOutput();
        output.setResponseStatus(1); // assume success
        output.setResponseMessage("OK");

        try {
            if (isInvalidContainerEir(containerEir)) {
                return createErrorResponse("contenedor_eir is empty");
            }

            Long subBusinessUnitId = getSubBusinessUnitId(subBusinessUnitId2);
            Integer containerDummyId = getContainerDummyId();
            String dbDateTimeFormat = getDbDateTimeFormat(subBusinessUnitId);

            if (isNumeric(containerEir)) {
                return handleNumericContainerEir(subBusinessUnitId2, containerEir, languageId, output, dbDateTimeFormat);
            } else {
                return handleNonNumericContainerEir(subBusinessUnitId2, containerEir, languageId, output, dbDateTimeFormat, containerDummyId);
            }
        } catch (Exception e) {
            logger.error("Error on getFotosEirContenedor", e);
            return createErrorResponse("An error occurred: " + e.getMessage());
        }
    }

    private boolean isInvalidContainerEir(String containerEir) {
        return containerEir == null || containerEir.trim().isEmpty();
    }

    private ListPhotosAndContainerOutput createErrorResponse(String message) {
        ListPhotosAndContainerOutput output = new ListPhotosAndContainerOutput();
        output.setResponseStatus(0);
        output.setResponseMessage(message);
        return output;
    }

    private Long getSubBusinessUnitId(Integer subBusinessUnitId2) {
        Long subBusinessUnitId = businessUnitRepository.findParentBusinessUnitIdLong(subBusinessUnitId2);
        if (subBusinessUnitId == null) {
            logger.info("No parent business unit found. Using fallback.");
            subBusinessUnitId = 0L;
        }
        return subBusinessUnitId;
    }

    private Integer getContainerDummyId() {
        Integer containerDummyId = containerRepository.findContainerDummyId();
        if (containerDummyId == null) {
            logger.info("No DUMMY container found.");
            containerDummyId = -99999;
        }
        return containerDummyId;
    }

    private String getDbDateTimeFormat(Long subBusinessUnitId) {
        String dbDateTimeFormat = businessUnitConfigRepository.fnFormatoDateTime(subBusinessUnitId.intValue());
        if (dbDateTimeFormat == null || dbDateTimeFormat.trim().isEmpty()) {
            dbDateTimeFormat = "yyyy-MM-dd HH:mm";
        }
        return dbDateTimeFormat;
    }

    private boolean isNumeric(String str) {
        return str.matches("\\d+");
    }

    private ListPhotosAndContainerOutput handleNumericContainerEir(Integer subBusinessUnitId2, String containerEir, Integer languageId, ListPhotosAndContainerOutput output, String dbDateTimeFormat) {
        Integer eirId = Integer.valueOf(containerEir);
        List<Object[]> spResult = eirRepository.callListarFotosEir(subBusinessUnitId2, eirId, languageId);
        if (spResult != null && !spResult.isEmpty()) {
            output.getDataResult().addAll(buildEirContainerResponse(spResult));
        } else {
            List<Eir> eirList = eirRepository.findByEirIdAndSubBusinessUnit(eirId, subBusinessUnitId2);
            output.getDataResult().addAll(buildSingleOrMultiEirResponse(eirList, dbDateTimeFormat));
        }
        return output;
    }

    private ListPhotosAndContainerOutput handleNonNumericContainerEir(Integer subBusinessUnitId2, String containerEir, Integer languageId, ListPhotosAndContainerOutput output, String dbDateTimeFormat, Integer containerDummyId) {
        List<Eir> eirData = eirRepository.findEirByContainerNumber(containerEir, subBusinessUnitId2, containerDummyId);
        if (eirData.size() == 1) {
            Integer eirId = eirData.get(0).getId();
            List<Object[]> spResult = eirRepository.callListarFotosEir(subBusinessUnitId2, eirId, languageId);
            if (spResult != null && !spResult.isEmpty()) {
                output.getDataResult().addAll(buildEirContainerResponse(spResult));
            } else {
                output.getDataResult().addAll(buildSingleOrMultiEirResponse(eirData, dbDateTimeFormat));
            }
        } else if (eirData.size() > 1) {
            output.getDataResult().addAll(buildPartialEirResponse(eirData, dbDateTimeFormat));
        } else {
            return createErrorResponse("No EIR records found for container: " + containerEir);
        }
        return output;
    }

    private List<EirContainerResponse> buildEirContainerResponse(List<Object[]> spResult) {
        List<EirContainerResponse> responseList = new ArrayList<>();
        for (Object[] row : spResult) {
            responseList.add(buildEirContainerResponseFromRow(row));
        }
        return responseList;
    }

    private EirContainerResponse buildEirContainerResponseFromRow(Object[] row) {
        EirContainerResponse r = new EirContainerResponse();
        r.setEirId(getIntegerValue(row, 0));
        r.setContainerNumber(getStringValue(row, 1));
        r.setOriginDestination(getStringValue(row, 2));
        r.setEirDate(getStringValue(row, 3));
        r.setObservation(getStringValue(row, 4));
        r.setType(getStringValue(row, 5));
        r.setClient(getStringValue(row, 6));
        r.setEirTypeDescription(getStringValue(row, 7));
        r.setContainerType(getStringValue(row, 8));
        r.setDocument(getStringValue(row, 9));
        r.setTransportCompany(getStringValue(row, 10));
        r.setShippingLine(getStringValue(row, 11));
        r.setConductorId(getIntegerValue(row, 12));
        r.setConductor(getStringValue(row, 13));
        r.setInspectorId(getIntegerValue(row, 14));
        r.setInspector(getStringValue(row, 15));
        r.setConductorSignatureDate(getStringValue(row, 16));
        r.setConductorSignatureUrl(getStringValue(row, 17));
        r.setInspectorSignatureUrl(getStringValue(row, 18));
        r.setEirChassisId(getIntegerValue(row, 19));
        r.setPersonChassisInspectorId(getIntegerValue(row, 20));
        r.setInspectorChassis(getStringValue(row, 21));
        r.setUrlSignatureInspectorChassis(getStringValue(row, 22));
        return r;
    }

    private Integer getIntegerValue(Object[] row, int index) {
        return row.length > index && row[index] != null ? (Integer) row[index] : null;
    }

    private String getStringValue(Object[] row, int index) {
        return row.length > index && row[index] != null ? (String) row[index] : "";
    }

    private List<EirContainerResponse> buildSingleOrMultiEirResponse(List<Eir> eirList, String dbDateTimeFormat) {
        List<EirContainerResponse> responseList = new ArrayList<>();
        DateTimeFormatter fallbackFormatter = DateTimeFormatter.ofPattern(dbDateTimeFormat);
        for (Eir e : eirList) {
            responseList.add(buildSingleOrMultiEirResponseFromEir(e, fallbackFormatter));
        }
        return responseList;
    }

    private EirContainerResponse buildSingleOrMultiEirResponseFromEir(Eir e, DateTimeFormatter fallbackFormatter) {
        EirContainerResponse r = new EirContainerResponse();
        r.setEirId(e.getId());
        r.setContainerNumber(e.getContainer() != null ? e.getContainer().getContainerNumber() : null);
        r.setOriginDestination(e.getCatOrigin() != null ? e.getCatOrigin().getLongDescription() : "");
        r.setEirDate(formatEirDate(e, fallbackFormatter));
        r.setObservation(e.getObservation());
        r.setType(e.getCatMovement() != null ? e.getCatMovement().getDescription() : null);
        r.setClient(e.getClientCompany() != null ? e.getClientCompany().getLegalName() : null);
        r.setEirTypeDescription(e.getCatMovement() != null ? e.getCatMovement().getVariable2() : null);
        r.setContainerType(buildContainerType(e));
        r.setTransportCompany(e.getTransportCompany() != null ? e.getTransportCompany().getLegalName() : "");
        r.setDocument(buildDocumentList(e));
        r.setShippingLine(e.getShippingLine() != null ? e.getShippingLine().getName() : "");
        r.setConductor(buildPersonName(e.getDriverPerson()));
        r.setInspector(buildPersonName(e.getInspectorPerson()));
        r.setConductorSignatureDate(formatDate(e.getSignatureDriver(), fallbackFormatter));
        r.setConductorSignatureUrl(e.getSignatureDriverURL());
        r.setInspectorSignatureUrl(e.getSignatureInspectorURL());
        if (e.getEirChassis() != null) {
            r.setEirChassisId(e.getEirChassis().getId());
            r.setInspectorChassis(buildPersonName(e.getEirChassis().getInspectorPersonChassis()));
            r.setUrlSignatureInspectorChassis(e.getEirChassis().getUrlSignatureChassisInspector());
        }
        return r;
    }

    private List<EirContainerResponse> buildPartialEirResponse(List<Eir> eirData, String dbDateTimeFormat) {
        List<EirContainerResponse> partialList = new ArrayList<>();
        DateTimeFormatter fallbackFormatter = DateTimeFormatter.ofPattern(dbDateTimeFormat);
        for (Eir e : eirData) {
            partialList.add(buildPartialEirResponseFromEir(e, fallbackFormatter));
        }
        return partialList;
    }

    private EirContainerResponse buildPartialEirResponseFromEir(Eir e, DateTimeFormatter fallbackFormatter) {
        EirContainerResponse r = new EirContainerResponse();

        r.setEirId(e.getId());
        r.setContainerNumber(getContainerNumber(e));
        r.setOriginDestination(getOriginDestination(e));
        r.setEirDate(formatEirDate(e, fallbackFormatter));
        r.setObservation(e.getObservation());
        r.setType(getMovementType(e));
        r.setClient(getClientCompanyName(e));
        r.setEirTypeDescription(getMovementDescription(e));
        r.setContainerType(buildContainerType(e));
        r.setTransportCompany(getTransportCompany(e));
        r.setDocument(buildDocumentList(e));
        r.setShippingLine(getShippingLineName(e));
        r.setConductor(buildPersonName(e.getDriverPerson()));
        r.setInspector(buildPersonName(e.getInspectorPerson()));
        r.setConductorSignatureDate(formatDate(e.getSignatureDriver(), fallbackFormatter));
        r.setConductorSignatureUrl(e.getSignatureDriverURL());
        r.setInspectorSignatureUrl(e.getSignatureInspectorURL());

        if (e.getEirChassis() != null) {
            r.setEirChassisId(e.getEirChassis().getId());
            r.setInspectorChassis(buildPersonName(e.getEirChassis().getInspectorPersonChassis()));
            r.setUrlSignatureInspectorChassis(e.getEirChassis().getUrlSignatureChassisInspector());
        }

        return r;
    }

    private String getContainerNumber(Eir e) {
        return e.getContainer() != null ? e.getContainer().getContainerNumber() : null;
    }

    private String getOriginDestination(Eir e) {
        return e.getCatOrigin() != null ? e.getCatOrigin().getLongDescription() : "";
    }

    private String getMovementType(Eir e) {
        return e.getCatMovement() != null ? e.getCatMovement().getDescription() : null;
    }

    private String getClientCompanyName(Eir e) {
        return e.getClientCompany() != null ? e.getClientCompany().getLegalName() : null;
    }

    private String getMovementDescription(Eir e) {
        return e.getCatMovement() != null ? e.getCatMovement().getVariable2() : null;
    }

    private String getTransportCompany(Eir e) {
        return e.getTransportCompany() != null ? e.getTransportCompany().getLegalName() : "";
    }

    private String getShippingLineName(Eir e) {
        return e.getShippingLine() != null ? e.getShippingLine().getName() : "";
    }

    private String formatEirDate(Eir e, DateTimeFormatter formatter) {
        String inDateStr = formatDate(e.getTruckArrivalDate(), formatter);
        String outDateStr = formatDate(e.getTruckDepartureDate(), formatter);
        return !inDateStr.isEmpty() && !outDateStr.isEmpty() ? inDateStr + " - " + outDateStr : inDateStr;
    }

    private String buildContainerType(Eir e) {
        String sizeDesc = (e.getCatSizeCnt() != null && e.getCatSizeCnt().getDescription() != null)
                ? e.getCatSizeCnt().getDescription().trim() : "";
        String typeDesc = (e.getCatContainerType() != null && e.getCatContainerType().getDescription() != null)
                ? e.getCatContainerType().getDescription().trim() : "";
        return (sizeDesc + " " + typeDesc).trim();
    }

    private String buildDocumentList(Eir e) {
        List<String> docList = eirDocumentCargoDetailRepository.findCargoDocumentsByEirId(e.getId());
        if (!docList.isEmpty()) {
            StringJoiner joiner = new StringJoiner(", ");
            docList.forEach(joiner::add);
            return joiner.toString();
        } else {
            return "";
        }
    }

    private String buildPersonName(Person person) {
        if (person == null) {
            return "";
        }
        return (person.getNames() + " " + person.getFirstLastName() + " " + (person.getSecondLastName() == null ? "" : person.getSecondLastName())).trim();
    }

    private String formatDate(LocalDateTime dt, DateTimeFormatter formatter) {
        if (dt == null) {
            return "";
        }
        try {
            return dt.format(formatter);
        } catch (Exception ex) {
            return dt.toString();
        }
    }
}