package com.maersk.sd1.sde.service;
import com.maersk.sd1.sde.dto.MercplusRepairMethodListOutput;
import com.maersk.sd1.common.repository.CatalogRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Log4j2
@RequiredArgsConstructor
public class MercplusRepairMethodListService {

    private final CatalogRepository catalogRepository;


    @Transactional(readOnly = true)
    public List<MercplusRepairMethodListOutput> getRepairMethodList() {
        try {
            log.info("Starting getRepairMethodList...");
            // Retrieve data from DB
            List<MercplusRepairMethodListOutput> result = catalogRepository.findAllRepairMethods();
            log.info("Completed getRepairMethodList. Found {} record(s).", result.size());
            return result;
        } catch (Exception e) {
            log.error("Error while retrieving repair methods", e);
            throw new RuntimeException("Error retrieving repair methods: " + e.getMessage(), e);
        }
    }
}
