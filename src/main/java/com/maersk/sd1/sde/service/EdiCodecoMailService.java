package com.maersk.sd1.sde.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.model.System;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sde.dto.EdiCodecoMailInputDto;
import com.maersk.sd1.sde.dto.EdiCodecoMailOutputDto;
import com.maersk.sd1.sde.exception.EdiCodecoMailException;
import jakarta.persistence.EntityManager;
import jakarta.persistence.ParameterMode;
import jakarta.persistence.StoredProcedureQuery;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class EdiCodecoMailService {

    private static final Logger logger = LogManager.getLogger(EdiCodecoMailService.class);

    private final CatalogRepository catalogRepository;
    private final GateTransmissionSentMailRepository gateTransmissionSentMailRepository;
    private final GateTransmissionSettingRepository gateTransmissionSettingRepository;
    private final AzureStorageConfigRepository azureStorageConfigRepository;
    private final GateTransmissionSentRepository gateTransmissionSentRepository;
    private final SystemRepository systemRepository;
    private final EntityManager entityManager;

    @Autowired
    public EdiCodecoMailService(CatalogRepository catalogRepository,
                                GateTransmissionSentMailRepository gateTransmissionSentMailRepository,
                                GateTransmissionSettingRepository gateTransmissionSettingRepository,
                                AzureStorageConfigRepository azureStorageConfigRepository,
                                GateTransmissionSentRepository gateTransmissionSentRepository,
                                SystemRepository systemRepository,
                                EntityManager entityManager) {
        this.catalogRepository = catalogRepository;
        this.gateTransmissionSentMailRepository = gateTransmissionSentMailRepository;
        this.gateTransmissionSettingRepository = gateTransmissionSettingRepository;
        this.azureStorageConfigRepository = azureStorageConfigRepository;
        this.gateTransmissionSentRepository = gateTransmissionSentRepository;
        this.systemRepository = systemRepository;
        this.entityManager = entityManager;
    }

    @Transactional
    public EdiCodecoMailOutputDto sendEdiCodecoMail(EdiCodecoMailInputDto.Input input) {
        EdiCodecoMailOutputDto outputDto = new EdiCodecoMailOutputDto();

        try {
            Integer isTelexFormat = getTelexFormat();
            GateTransmissionSentMail sentMail = getSentMail(input.getEdiCodecoEnvioCorreoId());
            GateTransmissionSetting setting = getSetting(input.getSeteoEdiCodecoId());
            String plantillaId = getPlantillaId(input, isTelexFormat, setting);
            List<Integer> gateTransmissionSentIds = getGateTransmissionSentIds(sentMail);
            String destinatarios = getDestinatarios(gateTransmissionSentIds);
            String adjuntos = getAdjuntos(gateTransmissionSentIds);
            Integer systemId = getSystemId();
            String emailCampos = getEmailCampos(input.getNombreArchivo());

            callStoredProcedure(input, outputDto, plantillaId, destinatarios, adjuntos, systemId, emailCampos);

        } catch (Exception e) {
            logger.error("Error in sendEdiCodecoMail", e);
            outputDto.setRespEstado(0);
            outputDto.setRespMensaje("Exception: " + e.getMessage());
            outputDto.setRespCorreo(null);
        }

        return outputDto;
    }

    private Integer getTelexFormat() {
        Catalog telexCatalog = catalogRepository.findByAlias("sd1_messagetype_gatetrans_telex");
        if (telexCatalog == null) {
            throw new EdiCodecoMailException("No Catalog found for alias: sd1_messagetype_gatetrans_telex");
        }
        return telexCatalog.getId();
    }

    private GateTransmissionSentMail getSentMail(Integer mailId) {
        return gateTransmissionSentMailRepository.findById(mailId)
                .orElseThrow(() -> new EdiCodecoMailException("No GateTransmissionSentMail found for ID:" + mailId));
    }

    private GateTransmissionSetting getSetting(Integer seteoEdiCodecoId) {
        return gateTransmissionSettingRepository.findById(seteoEdiCodecoId)
                .orElseThrow(() -> new EdiCodecoMailException("No GateTransmissionSetting found for ID:" + seteoEdiCodecoId));
    }

    private String getPlantillaId(EdiCodecoMailInputDto.Input input, Integer isTelexFormat, GateTransmissionSetting setting) {
        if (isTelexFormat != null && input.getTipoEstructuraId().compareTo(isTelexFormat) == 0) {
            return getAzureReference(setting.getAzureIdTelex(), "azure_id_telex");
        } else {
            return getAzureReference(setting.getAzureIdGateTransmission(), "azure_id_codeco");
        }
    }

    private String getAzureReference(String azureId, String azureIdType) {
        if (azureId == null) {
            throw new EdiCodecoMailException(azureIdType + " is null in GateTransmissionSetting.");
        }
        AzureStorageConfig cfg = azureStorageConfigRepository.findById1(azureId);
        if (cfg == null) {
            throw new EdiCodecoMailException("No AzureStorageConfig found for " + azureIdType + ":" + azureId);
        }
        return cfg.getReference01();
    }

    private List<Integer> getGateTransmissionSentIds(GateTransmissionSentMail sentMail) {
        List<Integer> gateTransmissionSentIds = new ArrayList<>();
        if (sentMail.getListGateTransmissionSentId() != null && !sentMail.getListGateTransmissionSentId().trim().isEmpty()) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                List<Map<String, Object>> items = mapper.readValue(
                        sentMail.getListGateTransmissionSentId(), new TypeReference<>() {});
                for (Map<String, Object> row : items) {
                    Object val = row.get("edi_codeco_envio_id");
                    if (val != null) {
                        gateTransmissionSentIds.add(Integer.valueOf(val.toString()));
                    }
                }
            } catch (Exception e) {
                throw new EdiCodecoMailException("Error parsing listGateTransmissionSentId JSON.", e);
            }
        }
        return gateTransmissionSentIds;
    }

    private String getDestinatarios(List<Integer> gateTransmissionSentIds) {
        List<GateTransmissionSent> transmissions = gateTransmissionSentRepository.findByIdIn(gateTransmissionSentIds);
        Set<String> uniqueRecipients = new HashSet<>();
        for (GateTransmissionSent gts : transmissions) {
            if (gts.getRecipientMail() != null) {
                String[] splitted = gts.getRecipientMail().split(";");
                for (String s : splitted) {
                    if (!s.trim().isEmpty()) {
                        uniqueRecipients.add(s.trim());
                    }
                }
            }
        }
        return String.join(";", uniqueRecipients);
    }

    private String getAdjuntos(List<Integer> gateTransmissionSentIds) {
        List<GateTransmissionSent> transmissions = gateTransmissionSentRepository.findByIdIn(gateTransmissionSentIds);
        List<Map<String, String>> attachmentList = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();

        for (GateTransmissionSent gts : transmissions) {
            if (hasProcessRecords(gts)) {
                attachmentList.addAll(parseProcessRecords(gts.getProcessRecords(), mapper));
            }
        }

        return convertAttachmentsToJson(attachmentList, mapper);
    }

    private boolean hasProcessRecords(GateTransmissionSent gts) {
        return gts.getProcessRecords() != null && !gts.getProcessRecords().trim().isEmpty();
    }

    private List<Map<String, String>> parseProcessRecords(String processRecords, ObjectMapper mapper) {
        List<Map<String, String>> attachmentList = new ArrayList<>();
        try {
            List<Map<String, Object>> items = mapper.readValue(processRecords, new TypeReference<>() {});
            for (Map<String, Object> row : items) {
                Map<String, String> attach = new HashMap<>();
                attach.put("name", (row.get("name") != null) ? row.get("name").toString() : "");
                attach.put("url", (row.get("url") != null) ? row.get("url").toString() : "");
                attachmentList.add(attach);
            }
        } catch (Exception e) {
            throw new EdiCodecoMailException("Error parsing archivos_procesar JSON.", e);
        }
        return attachmentList;
    }

    private String convertAttachmentsToJson(List<Map<String, String>> attachmentList, ObjectMapper mapper) {
        try {
            return mapper.writeValueAsString(attachmentList);
        } catch (Exception e) {
            throw new EdiCodecoMailException("Error converting attachments to JSON.", e);
        }
    }

    private Integer getSystemId() {
        System systemSd1 = systemRepository.findByName("SD1");
        if (systemSd1 == null) {
            throw new EdiCodecoMailException("No System found with name SD1.");
        }
        return systemSd1.getId();
    }

    private String getEmailCampos(String nombreArchivo) {
        Map<String, String> emailFields = new HashMap<>();
        emailFields.put("nombre_archivo", nombreArchivo);
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(emailFields);
        } catch (Exception e) {
            throw new EdiCodecoMailException("Error building emailCampos JSON.", e);
        }
    }

    private void callStoredProcedure(EdiCodecoMailInputDto.Input input, EdiCodecoMailOutputDto outputDto, String plantillaId, String destinatarios, String adjuntos, Integer systemId, String emailCampos) {
        try {
            StoredProcedureQuery query = entityManager.createStoredProcedureQuery("ges.email_procesar");
            query.registerStoredProcedureParameter("plantilla_id", String.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("destinatario", String.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("copia", String.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("copia_oculta", String.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("titulo", String.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("campos", String.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("sistema_id", Integer.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("origen", String.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("referencia", Integer.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("indicador_enviar", String.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("adjuntos", String.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("resp_estado", Integer.class, ParameterMode.OUT);
            query.registerStoredProcedureParameter("resp_mensaje", String.class, ParameterMode.OUT);
            query.registerStoredProcedureParameter("resp_correo", String.class, ParameterMode.OUT);

            query.setParameter("plantilla_id", plantillaId);
            query.setParameter("destinatario", destinatarios);
            query.setParameter("copia", null);
            query.setParameter("copia_oculta", null);
            query.setParameter("titulo", null);
            query.setParameter("campos", emailCampos);
            query.setParameter("sistema_id", systemId);
            query.setParameter("origen", "sde.servicio_codeco_enviar_correo_edi");
            query.setParameter("referencia", input.getEdiCodecoEnvioCorreoId());
            query.setParameter("indicador_enviar", "1");
            query.setParameter("adjuntos", adjuntos);

            query.execute();

            outputDto.setRespEstado((Integer) query.getOutputParameterValue("resp_estado"));
            outputDto.setRespMensaje((String) query.getOutputParameterValue("resp_mensaje"));
            outputDto.setRespCorreo((String) query.getOutputParameterValue("resp_correo"));
        } catch (Exception e) {
            throw new EdiCodecoMailException("Exception: " + e.getMessage(), e);
        }
    }
}





