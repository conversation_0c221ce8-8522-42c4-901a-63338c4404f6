package com.maersk.sd1.sde.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class SendContainerInformationOutput {

    public SendContainerInformationOutput(Integer seteoEdiCodecoId, Integer catCanalEnvioId, String canalEnvio,
                                          String correoDestinatario, String correoCodecoDestino,
                                          Integer catStructureFormatId) {
        this.seteoEdiCodecoId = seteoEdiCodecoId;
        this.catCanalEnvioId = catCanalEnvioId;
        this.canalEnvio = canalEnvio;
        this.correoDestinatario = correoDestinatario;
        this.correoCodecoDestino = correoCodecoDestino;
        this.catStructureFormatId = catStructureFormatId;
    }

    public SendContainerInformationOutput()
    {

    }

    @JsonProperty("seteo_edi_codeco_id")
    private Integer seteoEdiCodecoId;

    @JsonProperty("cat_canal_envio_id")
    private Integer catCanalEnvioId;

    @JsonProperty("canal_envio")
    private String canalEnvio;

    @JsonProperty("correo_destinatario")
    private String correoDestinatario;

    @JsonProperty("correo_codeco_destino")
    private String correoCodecoDestino;

    @JsonProperty("cat_structure_format_id")
    private Integer catStructureFormatId;
}
