package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.CodecoEnviarArchivosSftpInput;
import com.maersk.sd1.sde.dto.CodecoEnviarArchivosSftpOutput;
import com.maersk.sd1.sde.service.CodecoEnviarArchivosSftpService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDE/module/sde/SDECoparnResendServiceImp")
public class CodecoEnviarArchivosSftpController {

    private static final Logger logger = LogManager.getLogger(CodecoEnviarArchivosSftpController.class);

    private final CodecoEnviarArchivosSftpService codecoEnviarArchivosSftpService;

    @Autowired
    public CodecoEnviarArchivosSftpController(CodecoEnviarArchivosSftpService codecoEnviarArchivosSftpService) {
        this.codecoEnviarArchivosSftpService = codecoEnviarArchivosSftpService;
    }

    @PostMapping("/servicioCodecoEnviarArchivosSftp")
    public ResponseEntity<ResponseController<CodecoEnviarArchivosSftpOutput>> servicioCodecoEnviarArchivosSftp(
            @Valid @RequestBody CodecoEnviarArchivosSftpInput.Root request) {
        try {

            if(request.getPrefix() == null || request.getPrefix().getInput() == null) {
                throw new IllegalArgumentException("The request must contain a valid input.");
            }

            CodecoEnviarArchivosSftpInput.Input input = request.getPrefix().getInput();
            CodecoEnviarArchivosSftpOutput output = codecoEnviarArchivosSftpService.fetchPendingRecords(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            CodecoEnviarArchivosSftpOutput output = new CodecoEnviarArchivosSftpOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.internalServerError().body(new ResponseController<>(output));
        }
    }
}