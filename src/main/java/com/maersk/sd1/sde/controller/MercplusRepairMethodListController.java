package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.MercplusRepairMethodListOutput;
import com.maersk.sd1.sde.dto.MercplusRepairMethodListInput;
import com.maersk.sd1.sde.service.MercplusRepairMethodListService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("modulesde/ModuleSDE/module/sde/SDEMercPlusService")
@Log4j2
@RequiredArgsConstructor
public class MercplusRepairMethodListController {

    private final MercplusRepairMethodListService mercplusRepairMethodListService;


    @PostMapping("/sdemercplusRepairMethodList")
    public ResponseEntity<ResponseController<List<MercplusRepairMethodListOutput>>> getRepairMethods(
            @RequestBody @Valid MercplusRepairMethodListInput.Root request) {
        try {
            // In this case, we do not have any specific fields to validate inside Input,
            // but we respect the structure with Root/Prefix.
            List<MercplusRepairMethodListOutput> repairMethods = mercplusRepairMethodListService.getRepairMethodList();
            return ResponseEntity.ok(new ResponseController<>(repairMethods));
        } catch (Exception e) {
            log.error("An error occurred while processing the request.", e);
            return ResponseEntity.internalServerError().body(new ResponseController<>(null));
        }
    }
}
