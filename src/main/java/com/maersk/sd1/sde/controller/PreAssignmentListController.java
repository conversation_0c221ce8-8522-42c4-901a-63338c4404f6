package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.controller.dto.PreAssignmentListInput;
import com.maersk.sd1.sde.controller.dto.PreAssignmentListOutput;
import com.maersk.sd1.sde.service.PreAssignmentListService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDE/module/sde/SDEPreasignacionServiceImp")
public class PreAssignmentListController {

    private static final Logger logger = LogManager.getLogger(PreAssignmentListController.class);

    private final PreAssignmentListService preAssignmentListService;

    public PreAssignmentListController(PreAssignmentListService preAssignmentListService) {
        this.preAssignmentListService = preAssignmentListService;
    }

    @PostMapping("/sdepreasignacionListar")
    public ResponseEntity<ResponseController<PreAssignmentListOutput>> sdgPreAssignmentList(
            @RequestBody @Valid PreAssignmentListInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                return ResponseEntity.status(400).body(new ResponseController<>("Invalid data"));
            }
            PreAssignmentListInput.Input input = request.getPrefix().getInput();
            if(input.getSubBusinessUnitId() == null) {
                return ResponseEntity.status(400).body(new ResponseController<>("subBusinessUnitId cannot be null"));
            }
            PreAssignmentListOutput response = preAssignmentListService.preAssignmentList(input);
            return ResponseEntity.ok(new ResponseController<>(response));
        } catch (Exception e) {
            logger.error("Error in sdgPreAssignmentList", e);
            PreAssignmentListOutput out = new PreAssignmentListOutput();
            out.setRespStatus(0);
            out.setRespMessage(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(out));
        }
    }
}