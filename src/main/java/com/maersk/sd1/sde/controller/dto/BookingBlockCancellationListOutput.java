package com.maersk.sd1.sde.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class BookingBlockCancellationListOutput {

    @JsonProperty("resp_estado")
    private Integer respStatus;

    @JsonProperty("resp_mensaje")
    private String respMessage;

    @JsonProperty("data_list")
    private List<BookingBlockCancellationData> dataList;

    @JsonProperty("total_registros")
    private Integer totalRegisters;

    @Data
    public static class BookingBlockCancellationData {
        @JsonProperty("id_cb")
        private Integer idCB;

        @JsonProperty("tipo")
        private Integer type;

        @JsonProperty("tipo_desc")
        private String typeDescription;

        @JsonProperty("booking_liberado")
        private Boolean bookingReleased;

        @JsonProperty("documento_carga_id")
        private Integer cargoDocumentId;

        @JsonProperty("booking")
        private String booking;

        @JsonProperty("nave_viaje")
        private String vesselVoyage;

        @JsonProperty("operacion")
        private Integer operation;

        @JsonProperty("operacion_desc")
        private String operationDescription;

        @JsonProperty("motivo_cb")
        private Integer cancellationReason;

        @JsonProperty("motivo_cb_desc")
        private String cancellationReasonDescription;

        @JsonProperty("fecha_cancel_bloqueo")
        private String cancellationDate;

        @JsonProperty("usuario_registro_id")
        private Integer registeredUserId;

        @JsonProperty("usuario_registro_nombre")
        private String registeredUserFirstName;

        @JsonProperty("usuario_registro_apellido")
        private String registeredUserLastName;

        @JsonProperty("motivo_liberacion")
        private Integer releaseReason;

        @JsonProperty("motivo_liberacion_desc")
        private String releaseReasonDescription;

        @JsonProperty("fecha_liberacion")
        private String releaseDate;

        @JsonProperty("usuario_liberacion_id")
        private Integer releaseUserId;

        @JsonProperty("usuario_liberacion_nombre")
        private String releaseUserFirstName;

        @JsonProperty("usuario_liberacion_apellido")
        private String releaseUserLastName;

        @JsonProperty("origen_cb")
        private Integer cancellationOrigin;

        @JsonProperty("origen_cb_desc")
        private String cancellationOriginDescription;

        @JsonProperty("origen_liberacion")
        private Integer bookingReleaseOriginId;

        @JsonProperty("origen_liberacion_desc")
        private String releaseOriginDescription;
    }
}