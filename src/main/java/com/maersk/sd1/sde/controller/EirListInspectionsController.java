package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.EirListInspectionsInputDTO;
import com.maersk.sd1.sde.dto.EirListInspectionsOutputDTO;
import com.maersk.sd1.sde.service.EirListInspectionsService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/ModuleSDE/module/sde/SDEEIRServiceImp")
public class EirListInspectionsController {

    private static final Logger logger = LogManager.getLogger(EirListInspectionsController.class);

    private final EirListInspectionsService eirListInspectionsService;


    @PostMapping("/sdeeirListarInspecciones")
    public ResponseEntity<ResponseController<EirListInspectionsOutputDTO>> listInspections(@RequestBody @Valid EirListInspectionsInputDTO.Root request) {
        EirListInspectionsOutputDTO response = new EirListInspectionsOutputDTO();
        try {

            if(request.getPrefix() == null || request.getPrefix().getInput() == null) {
                logger.error("Invalid request");
                return ResponseEntity.badRequest().body(new ResponseController<>(response));
            }

            Integer eirId = request.getPrefix().getInput().getEirId();
            Integer languageId = request.getPrefix().getInput().getLanguageId();

            response = eirListInspectionsService.listInspections(eirId, languageId);
            return ResponseEntity.ok(new ResponseController<>(response));
        } catch (Exception ex) {
            logger.error("An error occurred while listing inspections", ex);
            return ResponseEntity.internalServerError().body(new ResponseController<>(response));
        }
    }
}