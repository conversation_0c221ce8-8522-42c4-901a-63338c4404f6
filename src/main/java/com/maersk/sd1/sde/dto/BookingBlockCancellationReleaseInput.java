package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class BookingBlockCancellationReleaseInput {

    @Data
    public static class Input {

        @JsonProperty("cancel_block_booking_id")
        @NotNull(message = "cancel_block_booking_id cannot be null")
        private Integer cancelBlockBookingId;

        @JsonProperty("comment")
        @Size(max = 200, message = "comment cannot exceed 200 characters")
        private String comment;

        @JsonProperty("reason_category")
        @NotNull(message = "reason_category cannot be null")
        private Integer reasonCategory;

        @JsonProperty("user_id")
        @NotNull(message = "user_id cannot be null")
        private Integer userId;

        @JsonProperty("language_id")
        @NotNull(message = "language_id cannot be null")
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}
