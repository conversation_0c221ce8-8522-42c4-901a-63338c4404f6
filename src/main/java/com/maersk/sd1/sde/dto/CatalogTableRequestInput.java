package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

public class CatalogTableRequestInput {
    @Data
    public static class Input {

        @JsonProperty("business_unit_id")
        @NotNull
        private BigDecimal businessUnitId;

        @JsonProperty("sub_business_unit_id")
        private BigDecimal subBusinessUnitId;

        @JsonProperty("language_id")
        @NotNull
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}
