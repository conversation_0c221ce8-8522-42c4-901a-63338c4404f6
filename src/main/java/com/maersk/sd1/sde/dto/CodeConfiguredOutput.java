package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.util.List;

/**
 * Output DTO containing response status and list of configuration items
 * that replicate the final SELECT result of the stored procedure.
 */
@Data
public class CodeConfiguredOutput {

    @JsonProperty("resp_estado")
    private Integer respEstado;

    @JsonProperty("resp_mensaje")
    private String respMensaje;

    @JsonProperty("items")
    private List<CodeConfiguredItemDto> items;

    @Data
    public static class CodeConfiguredItemDto {

        /**
         * Corresponds to the row in the final SELECT.
         * Using English names in the DTO but referencing Spanish columns in the logic.
         */
        @JsonProperty("row_id")
        private Integer rowId; // from "_id"

        @JsonProperty("gate_transmission_setting_id")
        private Integer gateTransmissionSettingId; // from "seteo_edi_codeco_id"

        @JsonProperty("sub_business_unit_id")
        private Long subBusinessUnitId; // from "sub_unidad_negocio_id"

        @JsonProperty("local_sub_business_unit_id")
        private Long localSubBusinessUnitId; // from "sub_unidad_negocio_local_id"

        @JsonProperty("event_id")
        private Integer eventId; // from "evento_id"

        @JsonProperty("event_name")
        private String eventName; // from "evento"

        @JsonProperty("shipping_line_id")
        private Integer shippingLineId; // from "linea_naviera_id"

        @JsonProperty("shipping_line_company")
        private String shippingLineCompany; // from "linea_naviera"

        @JsonProperty("movement_cat_id")
        private Integer movementCatId; // from "cat_movimiento_id"

        @JsonProperty("movement_description")
        private String movementDescription; // from "movimiento"

        @JsonProperty("empty_full_cat_id")
        private Integer emptyFullCatId; // from "cat_empty_full_id"

        @JsonProperty("empty_full_description")
        private String emptyFullDescription; // from "empty_full"

        @JsonProperty("transaction_type")
        private String transactionType; // from "tipo_transaccion"

        @JsonProperty("system_delivery")
        private String systemDelivery; // from "sistema_entrega"

        @JsonProperty("receptor_identifier")
        private String receptorIdentifier; // from "identificador_receptor"

        @JsonProperty("emitter_identifier")
        private String emitterIdentifier; // from "identificador_emisor"

        @JsonProperty("activity_location")
        private String activityLocation; // from "locacion_actividad"

        @JsonProperty("delivery_channel_cat_id")
        private Integer deliveryChannelCatId; // from "cat_canal_envio_id"

        @JsonProperty("delivery_channel_description")
        private String deliveryChannelDescription; // from "canal_envio"

        @JsonProperty("generate_file_mode_cat_id")
        private Integer generateFileModeCatId; // from "cat_modo_generar_archivo_id"

        @JsonProperty("extension_file_send")
        private String extensionFileSend; // from "extension_archivo_enviar"

        @JsonProperty("structure_type_name")
        private String structureTypeName; // from "tipo_estructura_nombre"

        @JsonProperty("structure_type_id")
        private Integer structureTypeId; // from "tipo_estructura_id"
    }
}
