package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class DisplayDTO {
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime gateInDate;
    private String eir;
    private String containerId;
    private String containerDimension;
    private String containerType;
    private String containerIsoCode;
    private Integer containerTare;
    private String machineCondition;
    private String shippingLine;
    private String movement;
    private Integer activityZoneId;
    private String local;
    @JsonIgnore
    private Integer elapsedMinutes;
    private Integer timeControl;
    private Integer typeReefer;
    private Integer brandMotor;
    private String reeferType;
    private String engineBrand;
    private Integer emrId;
    private String status;
    private Boolean isPartialInspection;
    private String statusAlias;
}
