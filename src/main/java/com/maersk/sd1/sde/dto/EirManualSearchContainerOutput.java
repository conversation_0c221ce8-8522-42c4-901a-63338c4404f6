package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class EirManualSearchContainerOutput {

    // Indicates final state of the operation: 0=Fail,1=Success,2=Success with warnings
    @JsonProperty("result_state")
    private Integer resultState;

    // Restrictive or informative message
    @JsonProperty("result_message")
    private String resultMessage;

    // Container data
    @JsonProperty("container_id")
    private Integer containerId;

    @JsonProperty("container_number")
    private String containerNumber;

    @JsonProperty("size_id")
    private Integer sizeId;

    @JsonProperty("type_container_id")
    private Integer typeContainerId;

    @JsonProperty("size_type")
    private String sizeType;

    @JsonProperty("tare")
    private Integer tare;

    @JsonProperty("measure_tare")
    private String measureTare;

    @JsonProperty("payload")
    private Integer payload;

    @JsonProperty("measure_payload")
    private String measurePayload;

    @JsonProperty("iso_code_id")
    private Integer isoCodeId;

    @JsonProperty("iso_code")
    private String isoCode;

    @JsonProperty("grade_id")
    private Integer gradeId;

    @JsonProperty("grade")
    private String grade;

    @JsonProperty("reefer_type")
    private String reeferType;

    @JsonProperty("shipping_line_container_id")
    private Integer shippingLineContainerId;

    @JsonProperty("shipping_line_container")
    private String shippingLineContainer;

    @JsonProperty("demurrage_date")
    private String demurrageDate;

    @JsonProperty("structure_damage")
    private Boolean structureDamage;

    @JsonProperty("machinery_damage")
    private Boolean machineryDamage;

    @JsonProperty("bl_booking_number")
    private String blOrBookingNumber;

    @JsonProperty("vessel_voyage")
    private String vesselVoyage;

    @JsonProperty("shipping_line_doc")
    private String shippingLineDoc;

    @JsonProperty("customer_id")
    private Long customerId;

    @JsonProperty("customer_name")
    private String customerName;

    @JsonProperty("programacion_nave_detalle_id")
    private Integer vesselProgrammingDetailId;

    @JsonProperty("documento_carga_detalle_id")
    private Integer cargoDocumentDetailId;

    // Seal info
    @JsonProperty("seal_1_enabled")
    private Boolean seal1Enabled;

    @JsonProperty("seal_1_mandatory")
    private Boolean seal1Mandatory;

    @JsonProperty("seal_2_enabled")
    private Boolean seal2Enabled;

    @JsonProperty("seal_2_mandatory")
    private Boolean seal2Mandatory;

    @JsonProperty("seal_3_enabled")
    private Boolean seal3Enabled;

    @JsonProperty("seal_3_mandatory")
    private Boolean seal3Mandatory;

    @JsonProperty("seal_4_enabled")
    private Boolean seal4Enabled;

    @JsonProperty("seal_4_mandatory")
    private Boolean seal4Mandatory;
}
