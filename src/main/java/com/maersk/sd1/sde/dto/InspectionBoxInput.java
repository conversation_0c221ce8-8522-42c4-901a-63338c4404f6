package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class InspectionBoxInput {

    @Data
    public static class Input {

        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;

        @JsonProperty("sub_unidad_negocio_id")
        private Integer subBusinessUnitId;

        @JsonProperty("sub_unidad_negocio_local_id")
        private Integer subBusinessUnitLocalId;

        @JsonProperty("eir_id")
        private Integer eirId;

        @JsonProperty("contenedor")
        private String container;

        @JsonProperty("en_stock")
        private String inStock;

        @JsonProperty("filterBox")
        private Integer filterBox;

        @JsonProperty("pending_inspection")
        private String pendingInspection;

        @JsonProperty("usuario_id")
        private Integer userId;

        @JsonProperty("Page")
        private Integer page;

        @JsonProperty("Size")
        private Integer size;

        @JsonProperty("idioma_id")
        private Integer languageId = 1;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private InspectionBoxInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private InspectionBoxInput.Prefix sde;

        public InspectionBoxInput.Input getInput() {
            return sde != null ? sde.getInput() : null;
        }
    }
}
