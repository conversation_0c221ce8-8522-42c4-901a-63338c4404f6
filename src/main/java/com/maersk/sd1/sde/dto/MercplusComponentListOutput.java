package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MercplusComponentListOutput {

    @JsonProperty("cat_component_id")
    private Integer catComponentId;

    @JsonProperty("component")
    private String component;

    public MercplusComponentListOutput(Integer catComponentId, String component) {
        this.catComponentId = catComponentId;
        this.component = component;
    }

    // No-args constructor for frameworks
    public MercplusComponentListOutput() {
    }
}
