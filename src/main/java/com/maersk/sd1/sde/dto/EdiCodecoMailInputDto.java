package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;


public class EdiCodecoMailInputDto {
    @Data
    public static class Input {

        @JsonProperty("edi_codeco_envio_correo_id")
        @NotNull
        private Integer ediCodecoEnvioCorreoId;

        @JsonProperty("seteo_edi_codeco_id")
        @NotNull
        private Integer seteoEdiCodecoId;

        @JsonProperty("nombre_archivo")
        @Size(max = 500)
        @NotNull
        private String nombreArchivo;

        @JsonProperty("tipo_estructura_id")
        @NotNull
        private Integer tipoEstructuraId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}
