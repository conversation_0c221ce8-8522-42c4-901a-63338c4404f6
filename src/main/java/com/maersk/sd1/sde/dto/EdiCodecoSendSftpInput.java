package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

public class EdiCodecoSendSftpInput {

    @Data
    public static class Input {
        @JsonProperty("seteo_edi_codeco_id")
        @NotNull
        private Integer gateTransmissionSettingId;

        @JsonProperty("referencia_id")
        @NotNull
        private Integer referenceId;

        @JsonProperty("archivos_repositorio")
        @Size(max = 2147483647)
        private String repositoryFiles;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}
