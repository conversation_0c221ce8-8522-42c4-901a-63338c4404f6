package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.BookingRegisterInput;
import com.maersk.sd1.sds.dto.BookingRegisterOutput;
import com.maersk.sd1.sds.service.BookingRegisterService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSBookingServiceImp")
public class BookingRegisterController {

    private static final Logger logger = LogManager.getLogger(BookingRegisterController.class.getName());

    private final BookingRegisterService service;

    @Autowired
    public BookingRegisterController(BookingRegisterService service) {
        this.service = service;
    }

    @PostMapping("/sdsbookingRegistrar")
    public ResponseEntity<ResponseController<BookingRegisterOutput>> bookingRegisterService(@RequestBody BookingRegisterInput.Root request) {
        try {
            return service.bookingRegisterService(request);
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ResponseController<>("An error occurred while processing the request: " + e.getMessage()));
        }
    }
}
