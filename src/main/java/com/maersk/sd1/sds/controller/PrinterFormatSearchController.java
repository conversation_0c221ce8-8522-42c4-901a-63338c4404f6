package com.maersk.sd1.sds.controller;

import com.maersk.sd1.sds.dto.PrinterFormatSearchInputDTO;
import com.maersk.sd1.sds.dto.PrinterFormatSearchOutputDTO;
import com.maersk.sd1.sds.service.PrinterFormatSearchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSPrinterServiceImp")
public class PrinterFormatSearchController {

    private final PrinterFormatSearchService printerFormatSearchService;

    @Autowired
    public PrinterFormatSearchController(PrinterFormatSearchService printerFormatSearchService) {
        this.printerFormatSearchService = printerFormatSearchService;
    }

    @PostMapping("/sdsprinterFormatSearch")
    public ResponseEntity<List<PrinterFormatSearchOutputDTO.Root>> searchPrinterFormat(@RequestBody PrinterFormatSearchInputDTO.Root request) {

        try {
            String printername = request.getPrefix().getInput().getPrinterName();
            int subBusinessUnitId = request.getPrefix().getInput().getSubBusinessUnitId();

            List<PrinterFormatSearchOutputDTO.Root> response = printerFormatSearchService.searchPrinterFormat(printername, subBusinessUnitId);

            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (IllegalArgumentException e) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}