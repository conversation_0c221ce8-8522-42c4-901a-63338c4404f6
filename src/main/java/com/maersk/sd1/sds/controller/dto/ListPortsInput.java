package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class ListPortsInput {

    @Data
    public static class Input {
        // There are no fields needed for this procedure input.
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull(message = "Input object cannot be null.")
        @Valid
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        @NotNull(message = "Request root object cannot be null.")
        @Valid
        private Prefix prefix;
    }

    private ListPortsInput() {
        // Private constructor to hide the implicit public one.
    }
}