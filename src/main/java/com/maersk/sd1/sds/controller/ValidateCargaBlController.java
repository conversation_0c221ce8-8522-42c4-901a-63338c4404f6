package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.ValidateCargaBlOutputDTO;
import com.maersk.sd1.sds.dto.ValidateCargaBlInput;
import com.maersk.sd1.sds.service.ValidateCargaBlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSRuleServiceImp")
public class ValidateCargaBlController {
    private final ValidateCargaBlService validateCargaBlService;

    @Autowired
    public ValidateCargaBlController(ValidateCargaBlService validateCargaBlService) {
        this.validateCargaBlService = validateCargaBlService;
    }

    @PostMapping("/validatecargabl")
    public ResponseEntity<ResponseController<List<ValidateCargaBlOutputDTO>>> ruleGeneralGetService(@RequestBody ValidateCargaBlInput.Root input) {
        return validateCargaBlService.ruleGeneralGetService(input);
    }
}
