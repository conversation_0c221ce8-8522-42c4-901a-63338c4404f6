package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Builder
@Data
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class BookingMonitoringInput {

    @Data
    public static class Input {

        @JsonProperty("sub_unidad_negocio_id")
        @NotNull
        private Integer businessSubUnitId;

        @JsonProperty("tipo_listado_id")
        @NotNull
        private Integer listingTypeId;

        @JsonProperty("seteo_edi_coparn_id")
        @NotNull
        private Integer ediCoparnSetupId;

        @JsonProperty("nave_nombre")
        @Size(max = 100)
        private String vesselName;

        @JsonProperty("viaje")
        @Size(max = 10)
        private String voyage;

        @JsonProperty("numero_booking")
        @Size(max = 25)
        private String bookingNumber;

        @JsonProperty("fecha_recepcion_desde")
        private LocalDateTime fromReceptionDate;

        @JsonProperty("fecha_recepcion_hasta")
        private LocalDateTime toReceptionDate;

        @JsonProperty("fecha_procesado_desde")
        private LocalDateTime fromProcessedDate;

        @JsonProperty("fecha_procesado_hasta")
        private LocalDateTime toProcessedDate;

        @JsonProperty("todos_depositos")
        @NotNull
        private String isAllDeposits;

        @JsonProperty("idioma_id")
        @NotNull
        private Integer languageId;

        @JsonProperty("page")
        @NotNull
        private Integer page;

        @JsonProperty("size")
        @NotNull
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private BookingMonitoringInput.Prefix sds;

        public BookingMonitoringInput.Input getInput() {
            return sds != null ? sds.getInput() : null;
        }
    }
}
