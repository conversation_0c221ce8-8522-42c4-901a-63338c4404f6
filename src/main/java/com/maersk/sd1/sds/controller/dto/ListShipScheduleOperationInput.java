package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ListShipScheduleOperationInput {
    @Data
    public static class Input {

        @NotNull
        @JsonProperty("programacion_nave_id")
        private Integer vesselProgrammingId;

        @NotNull
        @JsonProperty("idioma_id")
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDG")
        private Prefix prefix;
    }
}