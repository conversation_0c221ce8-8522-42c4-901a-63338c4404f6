package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.ListPortsInput;
import com.maersk.sd1.sds.controller.dto.ListPortsOutput;
import com.maersk.sd1.sds.service.ListPortsService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSProgramacionNaveServiceImp")
public class ListPortsController {

    private static final Logger logger = LogManager.getLogger(ListPortsController.class);

    private final ListPortsService listPortsService;

    @PostMapping("/sdslistarPuertosVs")
    public ResponseEntity<ResponseController<List<ListPortsOutput>>> listPorts(
            @RequestBody @Valid ListPortsInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid input payload structure."));
            }
            List<ListPortsOutput> ports = listPortsService.getAllPorts();
            return ResponseEntity.ok(new ResponseController<>(ports));
        } catch (Exception e) {
            logger.error("An error occurred while processing the port list request.", e);
            return ResponseEntity.internalServerError()
                    .body(new ResponseController<>(e.getMessage()));
        }
    }
}