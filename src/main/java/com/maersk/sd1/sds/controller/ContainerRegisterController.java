package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.ContainerRegisterInput;
import com.maersk.sd1.sds.dto.ContainerRegisterOutput;
import com.maersk.sd1.sds.service.ContainerRegisterService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSContenedorServiceImp")
public class ContainerRegisterController {

    private static final Logger logger = LogManager.getLogger(ContainerRegisterController.class);

    private final ContainerRegisterService containerRegisterService;

    @Autowired
    public ContainerRegisterController(ContainerRegisterService containerRegisterService) {
        this.containerRegisterService = containerRegisterService;
    }

    @PostMapping("/sdscontenedorRegistrar")
    public ResponseEntity<ResponseController<ContainerRegisterOutput>> registerContainer(@RequestBody @Valid ContainerRegisterInput.Root request) {
        try {

            if(request.getPrefix() == null || request.getPrefix().getInput() == null){
                ContainerRegisterOutput output = new ContainerRegisterOutput();
                output.setRespMensaje("Invalid request");
                output.setRespEstado(0);
                output.setRespNewId(0);
                return ResponseEntity.badRequest().body(new ResponseController<>(output));
            }

            ContainerRegisterInput.Input input = request.getPrefix().getInput();
            ContainerRegisterOutput output = containerRegisterService.registerContainer(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            ContainerRegisterOutput output = new ContainerRegisterOutput();
            output.setRespMensaje(e.toString());
            output.setRespEstado(0);
            output.setRespNewId(0);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(output));
        }
    }
}