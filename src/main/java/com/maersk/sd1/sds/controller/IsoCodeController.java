package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.Constants;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.IsoCodeSearchOutputDTO;
import com.maersk.sd1.sds.controller.dto.IsoCodeSeachInput;
import com.maersk.sd1.sds.service.IsoCodeService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDS/module/sds/")
public class IsoCodeController {

    private static final Logger logger = LogManager.getLogger(IsoCodeController.class.getName());

    private final IsoCodeService isoCodeService;

    @PostMapping("SDSCodigoIsoServiceImp/sdsisoCodeSearch/")
    public ResponseEntity<ResponseController<List<IsoCodeSearchOutputDTO>>> searchIsoCodes(@RequestBody IsoCodeSeachInput.Root request) {
        try {
            if (request == null || request.getPrefix() == null) {
                return ResponseEntity.ok(new ResponseController<>(Constants.INVALID_INPUT));
            }
            IsoCodeSeachInput.Input input = request.getPrefix().getInput();
            if(input.getIsoCode() == null){
                return ResponseEntity.ok(new ResponseController<>("ISO Code should not be null"));
            }
            List<IsoCodeSearchOutputDTO> results = isoCodeService.searchIsoDetails(input.getContainerType(), input.getContainerSize(), input.getIsoCode());
            return ResponseEntity.ok(new ResponseController<>(results));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(500).body(new ResponseController<>(null));
        }
    }
}