package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.ActivityLogListInput;
import com.maersk.sd1.sds.dto.ActivityLogListOutput;
import com.maersk.sd1.sds.service.ActivityLogListService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSActivityLogServiceImp")
public class ActivityLogListController {

    private static final Logger logger = LogManager.getLogger(ActivityLogListController.class);

    private final ActivityLogListService activityLogListService;

    @Autowired
    public ActivityLogListController(ActivityLogListService activityLogListService) {
        this.activityLogListService = activityLogListService;
    }

    @PostMapping("/sdsactivityLogList")
    public ResponseEntity<ResponseController<ActivityLogListOutput>> getActivityLogList(@RequestBody @Valid ActivityLogListInput.Root request) {
        try {

            if(request.getPrefix().getInput().getCurrentUserId() == null){
               return ResponseEntity.badRequest().body(new ResponseController<>(new ActivityLogListOutput()));
            }

            ActivityLogListOutput output = activityLogListService.getActivityLogList(request);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while fetching activity log list.", e);
            ActivityLogListOutput errorOutput = new ActivityLogListOutput();
            errorOutput.setRespEstado(0);
            errorOutput.setRespMensaje(e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(errorOutput));
        }
    }
}
