package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.GeneralDataInput;
import com.maersk.sd1.sds.dto.GeneralDataOutput;
import com.maersk.sd1.sds.service.GeneralDataService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSPrincipalServiceImp")
public class GeneralDataController {

    private static final Logger logger = LogManager.getLogger(GeneralDataController.class.getName());

    private final GeneralDataService service;

    @Autowired
    public GeneralDataController(GeneralDataService service) {
        this.service = service;
    }

    @PostMapping("/sdsdatosGenerales")
    public ResponseEntity<ResponseController<GeneralDataOutput>> generalDataService(@RequestBody GeneralDataInput.Root request) {
        try {
            return service.generalDataService(request);
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ResponseController<>("An error occurred while processing the request: " + e.getMessage()));
        }
    }
}
