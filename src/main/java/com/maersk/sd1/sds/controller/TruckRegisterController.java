package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.TruckRegisterInput;
import com.maersk.sd1.sds.dto.TruckRegisterOutput;
import com.maersk.sd1.sds.service.TruckRegisterService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSVehiculoServiceImp")
public class TruckRegisterController {

    private static final Logger logger = LogManager.getLogger(TruckRegisterController.class);


    private final TruckRegisterService truckRegisterService;


    public TruckRegisterController(TruckRegisterService truckRegisterService) {
        this.truckRegisterService = truckRegisterService;
    }

    @PostMapping("/sdsvehiculoRegistrar")
    public ResponseEntity<ResponseController<TruckRegisterOutput>> registerTruck(
            @RequestBody @Valid TruckRegisterInput.Root request) {
        try {
            TruckRegisterInput.Input input = request.getPrefix().getInput();

            TruckRegisterOutput output = truckRegisterService.registerTruck(input);

            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the truck registration request.", e);
            TruckRegisterOutput output = new TruckRegisterOutput();
            output.setRespMensaje(e.toString());
            output.setRespEstado(0);
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}
