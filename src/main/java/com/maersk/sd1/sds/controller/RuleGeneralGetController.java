package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.RuleGeneralGetInputDTO;
import com.maersk.sd1.sds.dto.RuleGeneralGetOutputDTO;
import com.maersk.sd1.sds.service.RuleGeneralGetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSRuleServiceImp")
public class  RuleGeneralGetController {

    private final RuleGeneralGetService ruleGeneralGetService;

    @Autowired
    public RuleGeneralGetController(RuleGeneralGetService ruleGeneralGetService) {
        this.ruleGeneralGetService = ruleGeneralGetService;
    }

    @PostMapping("/sdsruleGeneralGet")
    public ResponseEntity<ResponseController<List<RuleGeneralGetOutputDTO>>> ruleGeneralGetService(@RequestBody RuleGeneralGetInputDTO.Root input) {
        return ruleGeneralGetService.ruleGeneralGetService(input);
    }
}
