package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class ApproveBookingInput {

    @Data
    public static class Input {
        @JsonProperty("bookingId")
        private Integer bookingId;

        @JsonProperty("registeredUserId")
        private Integer registeredUserId;

        @JsonProperty("categoryOriginApproval")
        private Integer categoryOriginApproval;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private ApproveBookingInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private ApproveBookingInput.Prefix prefix;
    }
}
