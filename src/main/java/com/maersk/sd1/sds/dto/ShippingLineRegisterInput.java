package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class ShippingLineRegisterInput {

    @Data
    public static class Input {

        @JsonProperty("nombre")
        @NotNull
        @Size(max = 100)
        private String nombre;

        @JsonProperty("activo")
        @NotNull
        private Boolean activo;

        @JsonProperty("usuario_registro_id")
        @NotNull
        private Integer usuarioRegistroId;

        @JsonProperty("linea_naviera")
        @NotNull
        @Size(max = 10)
        private String lineaNaviera;

        @JsonProperty("color")
        @Size(max = 100)
        private String color;

        @JsonProperty("idioma_id")
        @NotNull
        private Integer idiomaId;
    }

    @Data
    public static class Prefix {

        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {

        @JsonProperty("SDS")
        private Prefix prefix;
    }
}
