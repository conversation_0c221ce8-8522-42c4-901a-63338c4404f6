package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class BookingEdiProcessUpdatesOutputDTO {

    @Data
    public static class Output {
        @JsonProperty("documentoCargaId")
        private Integer documentoCargaId;

        @JsonProperty("productoId")
        private Integer productoId;

        @JsonProperty("catEmbalajeId")
        private Double catEmbalajeId;

        @JsonProperty("cantidadManifestada")
        private Integer cantidadManifestada;

        @JsonProperty("pesoManifestado")
        private Integer pesoManifestado;

        @JsonProperty("volumenManifestado")
        private Integer volumenManifestado;

        @JsonProperty("catUnidadMedidaPesoId")
        private Double catUnidadMedidaPesoId;

        @JsonProperty("diceContener")
        private Boolean diceContener;

        @JsonProperty("esCargaPeligrosa")
        private Boolean esCargaPeligrosa;

        @JsonProperty("esCargaRefrigerada")
        private Boolean esCargaRefrigerada;

        @JsonProperty("mercaderia")
        private String mercaderia;

        @JsonProperty("catCondicionCargaId")
        private Double catCondicionCargaId;

        @JsonProperty("gateoutEmptyLiquidado")
        private Boolean gateoutEmptyLiquidado;

        @JsonProperty("activo")
        private Boolean activo;

        @JsonProperty("catOrigenCreacionId")
        private Integer catOrigenCreacionId;

        @JsonProperty("usuarioRegistroId")
        private Double usuarioRegistroId;

        @JsonProperty("fechaRegistro")
        private String fechaRegistro;

        @JsonProperty("catTipoContenedorManifestadoId")
        private Double catTipoContenedorManifestadoId;

        @JsonProperty("catTamanoManifestadoId")
        private Double catTamanoManifestadoId;

        @JsonProperty("catUnidadMedidaCantidadId")
        private Double catUnidadMedidaCantidadId;

        @JsonProperty("bookingDetalleId")
        private Integer bookingDetalleId;

        @JsonProperty("traceDocCargaDetalle")
        private String traceDocCargaDetalle;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private com.maersk.sd1.sds.dto.BookingEdiProcessUpdatesOutputDTO.Output output;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private com.maersk.sd1.sds.dto.BookingEdiProcessUpdatesOutputDTO.Prefix prefix;

        public Root() {
            this.prefix = new com.maersk.sd1.sds.dto.BookingEdiProcessUpdatesOutputDTO.Prefix();
            this.prefix.setOutput(new com.maersk.sd1.sds.dto.BookingEdiProcessUpdatesOutputDTO.Output());
        }
    }
}
