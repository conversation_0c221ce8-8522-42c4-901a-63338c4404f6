package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.maersk.sd1.sds.controller.dto.ShippingLineOutput;
import com.maersk.sd1.seg.dto.BusinessUnitListOutput;
import lombok.Data;

import java.util.List;

@Data
public class GetCatalogsByTablesOutput {

    @JsonProperty("shipping_lines")
    private ShippingLineOutput shippingLineList;

    @JsonProperty("companies")
    private CompanyListDTO companieList;

    @JsonProperty("depots")
    private DepositListOutput depotList;

    @JsonProperty("products")
    private ProductListOutput productList;

    @JsonProperty("imos")
    private ListImoOutput imoList;

    @JsonProperty("business_units")
    private BusinessUnitListOutput businessUnitList;

    @JsonProperty("currencies")
    private CurrencyDTO currencyList;

    @JsonProperty("coparn_configuration")
    private List<CoparnConfigurationOutput.DataItem> coparnConfiguration;

    @JsonProperty("response_status")
    private Integer responseStatus;

    @JsonProperty("response_message")
    private String responseMessage;
}
