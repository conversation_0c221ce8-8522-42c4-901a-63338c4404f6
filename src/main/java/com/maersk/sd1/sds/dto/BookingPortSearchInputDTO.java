package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class BookingPortSearchInputDTO {

    @Data
    public static class Input {
        @JsonProperty("ship_programming_detail_id")
        private Integer shipProgrammingDetailId;

        @JsonProperty("port_ids")
        private List<Integer> portIds;

        @JsonProperty("port_name")
        private String portName;
    }

    @Data
    public static class Prefix {

        @JsonProperty("F")
        private BookingPortSearchInputDTO.Input input;
    }

    @Data
    public static class Root {

        @JsonProperty("SDS")
        private BookingPortSearchInputDTO.Prefix prefix;

        public Root() {
            this.prefix = new BookingPortSearchInputDTO.Prefix();
            this.prefix.setInput(new BookingPortSearchInputDTO.Input());
        }
    }
}
