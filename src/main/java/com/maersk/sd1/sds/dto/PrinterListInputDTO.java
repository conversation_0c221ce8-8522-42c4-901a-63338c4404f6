package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class PrinterListInputDTO {

    @Data
    public static class Input {

        @JsonProperty("printer_id")
        private Integer printerId;

        @JsonProperty("sub_business_unit_id")
        private Integer subBusinessUnitId;

        @JsonProperty("printer_name")
        private String printerName;

        @JsonProperty("active")
        private Character active;

        @JsonProperty("page")
        private Integer page;

        @JsonProperty("size")
        private Integer size;
    }

    @Data
    public static class Prefix {

        @JsonProperty("F")
        private PrinterListInputDTO.Input input;
    }

    @Data
    public static class Root {

        @JsonProperty("SDS")
        private PrinterListInputDTO.Prefix prefix;

        public Root() {
            this.prefix = new PrinterListInputDTO.Prefix();
            this.prefix.setInput(new PrinterListInputDTO.Input());
        }
    }
}
