package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.model.Booking;
import com.maersk.sd1.common.model.BookingDetail;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.BookingDetailRepository;
import com.maersk.sd1.common.repository.BookingRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.sds.dto.ApproveBookingInput;
import com.maersk.sd1.sds.dto.ApproveBookingOutput;
import com.maersk.sd1.sds.dto.RegisterBookingDetailsInput;
import com.maersk.sd1.sds.dto.RegisterBookingDetailsOutput;
import jakarta.transaction.Transactional;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

@Service
public class RegisterBookingDetailsService {

    private static final Logger logger = LogManager.getLogger(RegisterBookingDetailsService.class.getName());

    private final BookingDetailRepository bookingDetailRepository;
    private final BookingRepository bookingRepository;
    private final CatalogRepository catalogRepository;
    private final ApproveBookingService approveBookingService;
    private final MessageLanguageService messageLanguageService;

    @Autowired
    public RegisterBookingDetailsService(@Qualifier("bookingDetailRepository") BookingDetailRepository bookingDetailRepository, BookingRepository bookingRepository, CatalogRepository catalogRepository, ApproveBookingService approveBookingService, MessageLanguageService messageLanguageService) {
        this.bookingDetailRepository = bookingDetailRepository;
        this.bookingRepository = bookingRepository;
        this.catalogRepository = catalogRepository;
        this.approveBookingService = approveBookingService;
        this.messageLanguageService = messageLanguageService;
    }

    @Transactional
    public ResponseEntity<ResponseController<RegisterBookingDetailsOutput>> registerBookingDetailsService(RegisterBookingDetailsInput.Root request) {
        if (request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
            logger.error("Invalid request");
            return ResponseEntity.badRequest().body(new ResponseController<>("Invalid request"));
        }

        RegisterBookingDetailsInput.Input input = request.getPrefix().getInput();
        Integer bookingId = input.getBookingId();
        Integer containerSizeCategoryId = input.getContainerSizeCategoryId();
        Integer containerTypeCategoryId = input.getContainerTypeCategoryId();
        Integer reservedQuantity = input.getReservedQuantity();
        Integer requiredMaxLoad = input.getRequiredMaxLoad();
        Integer bookingCreationSourceCategoryId = input.getBookingCreationSourceCategoryId();
        Integer registeredUserId = input.getRegisteredUserId();
        Integer languageId = input.getLanguageId();
        boolean isBookingApproved = false;
        String remarkRulesName = "";
        String catalogTypeContainerDryAlias = "31049";
        String catalogTypeContainerHcAlias = "31053";
        String catalogTypeApprovalManualAlias = "47769";

        Integer isContainerTypeDry = catalogRepository.findIdByAlias(catalogTypeContainerDryAlias);
        Integer isContainerTypeHc = catalogRepository.findIdByAlias(catalogTypeContainerHcAlias);
        Integer isApprovalManual = catalogRepository.findIdByAlias(catalogTypeApprovalManualAlias);

        RegisterBookingDetailsOutput output = new RegisterBookingDetailsOutput();

        try {
            List<Object[]> res = bookingRepository.findBookingApprovedAndRemarkRulesName(bookingId);
            if (res != null && !res.isEmpty()) {
                isBookingApproved = (Boolean) res.getFirst()[0];
                remarkRulesName = (String) res.getFirst()[1];
            }

            BookingDetailParams params = new BookingDetailParams();
            params.setBookingId(bookingId);
            params.setContainerSizeCategoryId(containerSizeCategoryId);
            params.setContainerTypeCategoryId(containerTypeCategoryId);
            params.setReservedQuantity(reservedQuantity);
            params.setRequiredMaxLoad(requiredMaxLoad);
            params.setBookingCreationSourceCategoryId(bookingCreationSourceCategoryId);
            params.setRegisteredUserId(registeredUserId);
            params.setRemarkRulesName(remarkRulesName);

            if ("FLAG_TO_FLEX".equals(remarkRulesName) && (containerTypeCategoryId.equals(isContainerTypeDry) || containerTypeCategoryId.equals(isContainerTypeHc))) {
                reservedQuantity = 1;
                params.setReservedQuantity(reservedQuantity);
                params.setTraceBkDetail("insbkdetail_toflex");
                for (int i = 1; i <= reservedQuantity; i++) {
                    BookingDetail bookingDetail = createBookingDetail(params);
                    BookingDetail savedBookingDetail = bookingDetailRepository.save(bookingDetail);
                    output.setResponseId(savedBookingDetail.getId());
                }
            } else {
                params.setTraceBkDetail("insbkdetail");
                BookingDetail bookingDetail = createBookingDetail(params);
                BookingDetail savedBookingDetail = bookingDetailRepository.save(bookingDetail);
                output.setResponseId(savedBookingDetail.getId());
            }

            output.setResponseState(1);
            output.setResponseMessage(messageLanguageService.getMessage("GENERAL", 9, languageId));

            if (isBookingApproved) {
                ApproveBookingInput.Root approveBookingInput = getRoot(bookingId, isApprovalManual);
                ApproveBookingOutput approveOutput = approveBookingService.approveBookingService(approveBookingInput);
                if (approveOutput.getResultState() != 1) {
                    output.setResponseState(0);
                    output.setResponseId(0);
                    output.setResponseMessage("An error occurred while processing the request: " + approveOutput.getResultMessage());
                }
            }

        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            output.setResponseState(0);
            output.setResponseId(0);
            output.setResponseMessage("An error occurred while processing the request: " + e.getMessage());
        }
        return ResponseEntity.ok(new ResponseController<>(output));
    }

    private BookingDetail createBookingDetail(BookingDetailParams params) {
        BookingDetail bookingDetail = new BookingDetail();
        Booking booking = new Booking();
        booking.setId(params.getBookingId());
        bookingDetail.setBooking(booking);

        Catalog catSize = new Catalog();
        catSize.setId(params.getContainerSizeCategoryId());
        bookingDetail.setCatSize(catSize);

        Catalog catContainerType = new Catalog();
        catContainerType.setId(params.getContainerTypeCategoryId());
        bookingDetail.setCatContainerType(catContainerType);

        bookingDetail.setReservationQuantity(params.getReservedQuantity());
        bookingDetail.setAttendedQuantity(0);
        bookingDetail.setMaximumLoadRequired(params.getRequiredMaxLoad());

        Catalog catOriginBookingCreation = new Catalog();
        catOriginBookingCreation.setId(params.getBookingCreationSourceCategoryId());
        bookingDetail.setCatOriginBookingCreation(catOriginBookingCreation);

        bookingDetail.setActive(true);

        User registrationUser = new User();
        registrationUser.setId(params.getRegisteredUserId());
        bookingDetail.setRegistrationUser(registrationUser);

        bookingDetail.setTraceBkDetail(params.getTraceBkDetail());
        bookingDetail.setRemarkRulesName(params.getRemarkRulesName());

        LocalDateTime registrationDate = new Date().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        bookingDetail.setRegistrationDate(registrationDate);

        return bookingDetail;
    }

    private static ApproveBookingInput.Root getRoot(Integer bookingId, Integer isApprovalManual) {
        ApproveBookingInput.Root approveBookingInput = new ApproveBookingInput.Root();
        ApproveBookingInput.Input approveBookingInputData = new ApproveBookingInput.Input();
        ApproveBookingInput.Prefix approveBookingInputPrefix = new ApproveBookingInput.Prefix();
        approveBookingInputData.setBookingId(bookingId);
        approveBookingInputData.setRegisteredUserId(1);
        approveBookingInputData.setCategoryOriginApproval(isApprovalManual);
        approveBookingInputPrefix.setInput(approveBookingInputData);
        approveBookingInput.setPrefix(approveBookingInputPrefix);
        return approveBookingInput;
    }

    @Getter
    @Setter
    private static class BookingDetailParams {
        private Integer bookingId;
        private Integer containerSizeCategoryId;
        private Integer containerTypeCategoryId;
        private Integer reservedQuantity;
        private Integer requiredMaxLoad;
        private Integer bookingCreationSourceCategoryId;
        private Integer registeredUserId;
        private String remarkRulesName;
        private String traceBkDetail;
    }
}