package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.repository.SystemRuleRepository;
import com.maersk.sd1.sds.dto.RuleGeneralGetInputDTO;
import com.maersk.sd1.sds.dto.RuleGeneralGetOutputDTO;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class RuleGeneralGetService {

    private final SystemRuleRepository systemRuleRepository;

    @Autowired
    public RuleGeneralGetService(SystemRuleRepository systemRuleRepository) {
        this.systemRuleRepository = systemRuleRepository;
    }

    public ResponseEntity<ResponseController<List<RuleGeneralGetOutputDTO>>> ruleGeneralGetService(RuleGeneralGetInputDTO.Root request) {
        List<RuleGeneralGetOutputDTO> output = new ArrayList<>();
        try {
            log.info("Received request: " + request);

            RuleGeneralGetInputDTO.Input input = request.getPrefix().getInput();

            String subBusinessUnitLocalAlias = input.getSubBusinessUnitLocalAlias();
            String systemRuleId = input.getSystemRuleId();
            String typeRule = input.getTypeRule();

            log.info("typeRule: " + typeRule);


            String jsonResult = systemRuleRepository.findRuleByIdAndActiveTrue(systemRuleId);
            log.info("jsonResult: " + jsonResult);

            JSONArray jsonArray = new JSONArray(jsonResult);

            List<String> typeRuleList = new ArrayList<>();
            if (typeRule != null) {
                JSONArray typeRuleArray = new JSONArray(typeRule);
                for (int j = 0; j < typeRuleArray.length(); j++) {
                    typeRuleList.add(typeRuleArray.getString(j));
                }
            }

            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String businessUnitAlias = jsonObject.getString("business_unit_alias");
                String typeRuleStr = jsonObject.getString("type_rule");
                Boolean action = null;

                if (jsonObject.has("action")) {
                    action = jsonObject.getBoolean("action");
                }

                if (businessUnitAlias.equals(subBusinessUnitLocalAlias) && (typeRule == null || typeRuleList.contains(typeRuleStr))) {
                    RuleGeneralGetOutputDTO ruleGeneralGetOutput = new RuleGeneralGetOutputDTO();
                    ruleGeneralGetOutput.setTypeRule(typeRuleStr);
                    ruleGeneralGetOutput.setAction(action);
                    output.add(ruleGeneralGetOutput);
                }
            }

            log.info("Output: " + output);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            log.error("Error occurred: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ResponseController<>(Collections.emptyList()));
        }
    }
}
