package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonFormat (shape = JsonFormat.Shape.ARRAY)
public class UserEmailTemplateRegisterOutput {

    @JsonProperty("resp_estado")
    private Integer respStatus;

    @JsonProperty("resp_mensaje")
    private String respMessage;
}
