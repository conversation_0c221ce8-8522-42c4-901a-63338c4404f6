package com.maersk.sd1.seg.controller;

import com.maersk.sd1.seg.dto.LanguageListInput;
import com.maersk.sd1.seg.dto.LanguageListOutput;
import com.maersk.sd1.seg.service.LanguageListService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller that exposes an endpoint for listing Languages.
 */

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSEG/module/seg/ADMIdiomaService")
public class LanguageListController {

    private static final Logger logger = LogManager.getLogger(LanguageListController.class);

    private final LanguageListService languageListService;

    /**
     * Endpoint to list Languages following the structure from the sample.
     *
     * @param request The request input with filters, pagination, etc.
     * @return A ResponseEntity with the LanguageListOutput wrapped in a ResponseController.
     */
    @PostMapping("/segidiomaListar")
    public ResponseEntity<ResponseController<LanguageListOutput>> listLanguages(@RequestBody @Valid LanguageListInput.Root request) {
        try {
            LanguageListOutput output = languageListService.listLanguages(request);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while listing languages.", e);
            // Return an unsuccessful response.
            LanguageListOutput output = new LanguageListOutput();
            output.setTotalRecords(0L);
            // We can set records to null or empty.
            output.setRecords(null);
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}
