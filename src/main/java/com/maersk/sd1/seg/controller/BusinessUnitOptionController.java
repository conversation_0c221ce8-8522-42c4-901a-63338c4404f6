package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.controller.dto.BusinessUnitOptionInput;
import com.maersk.sd1.seg.controller.dto.BusinessUnitOptionOutput;
import com.maersk.sd1.seg.service.BusinessUnitOptionService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMUnidadNegocioServiceImp")
public class BusinessUnitOptionController {

    private static final Logger logger = LogManager.getLogger(BusinessUnitOptionController.class);

    private final BusinessUnitOptionService businessUnitOptionService;

    public BusinessUnitOptionController(BusinessUnitOptionService businessUnitOptionService) {
        this.businessUnitOptionService = businessUnitOptionService;
    }

    @PostMapping("/segunidadNegocioOpciones")
    public ResponseEntity<ResponseController<BusinessUnitOptionOutput>> getBusinessUnitOptions(@RequestBody @Valid BusinessUnitOptionInput.Root request) {
        try {
            BusinessUnitOptionOutput output = businessUnitOptionService.getBusinessUnitOptions();
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            BusinessUnitOptionOutput output = new BusinessUnitOptionOutput();
            output.setRespMessage(e.toString());
            output.setRespStatus(0);
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}