package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class RoleObtainUserInput {
    @Data
    public static class Input {
        @JsonProperty("usuario_id")
        @NotNull(message = "usuario_id must not be null")
        private Integer usuarioId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        private Prefix prefix;
    }

    private RoleObtainUserInput() {
        // Private constructor to hide the implicit public one
    }
}