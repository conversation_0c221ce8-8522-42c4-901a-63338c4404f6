package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.Constants;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.controller.dto.UserValidateInput;
import com.maersk.sd1.seg.controller.dto.UserValidateOutput;
import com.maersk.sd1.seg.service.UserValidateService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSEG/module/seg/dsinland")
public class UserValidateController {

    private static final Logger logger =  LogManager.getLogger(UserValidateController.class.getName());

    private final UserValidateService userValidateService;

    @PostMapping("/segusuarioValidar")
    public ResponseEntity<ResponseController<UserValidateOutput>> notificationRegister(@RequestBody UserValidateInput.Root request) {

        try {
            if (request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                return ResponseEntity.ok(new ResponseController<>(Constants.INVALID_INPUT));
            }

            String id = request.getPrefix().getInput().getId();
            String email = request.getPrefix().getInput().getEmail();

            if (id == null || email == null) {
                return ResponseEntity.ok(new ResponseController<>("ID and Email must not be null"));
            }
            UserValidateOutput userValidateOutput = userValidateService.validateUser(request);
            return ResponseEntity.ok(new ResponseController<>(userValidateOutput));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            UserValidateOutput userValidateOutput = new UserValidateOutput();
            return ResponseEntity.status(500).body(new ResponseController<>(userValidateOutput));
        }
    }
}