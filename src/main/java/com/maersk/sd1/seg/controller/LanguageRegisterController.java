package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.dto.LanguageRegisterInput;
import com.maersk.sd1.seg.dto.LanguageRegisterOutput;
import com.maersk.sd1.seg.service.LanguageRegisterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;

@RestController
@RequestMapping("/ModuleSEG/module/seg/SEGIdiomaServiceImp")
public class LanguageRegisterController {
    private static final Logger logger = LogManager.getLogger(LanguageRegisterController.class.getName());

    private final LanguageRegisterService service;

    @Autowired
    public LanguageRegisterController(LanguageRegisterService service) {
        this.service = service;
    }

    @PostMapping("/segIdiomaregistrar")
    public ResponseEntity<ResponseController<LanguageRegisterOutput>> languageRegisterService(@RequestBody LanguageRegisterInput.Root request) {
        try {
            return service.languageRegisterService(request);
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ResponseController<>("An error occurred while processing the request: " + e.getMessage()));
        }
    }
}
