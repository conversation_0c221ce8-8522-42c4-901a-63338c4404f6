package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
public class ServiceGetOutput {

    @JsonProperty("resp_estado")
    private Integer respStatus;

    @JsonProperty("resp_mensaje")
    private String respMessage;

    @JsonProperty("servicio")
    private ServiceData service;

    @JsonProperty("lista_roles")
    private List<RolData> listRoles;

    @Data
    public static class ServiceData {
        @JsonProperty("servicio_id")
        private Integer serviceId;

        @JsonProperty("nombre")
        private String names;

        @JsonProperty("indicador_protegido")
        private Character proctectedIndicator;

        @JsonProperty("estado")
        private Boolean status;

        @JsonProperty("usuario_registro_id")
        private Integer userRegisterId;

        @JsonProperty("fecha_registro")
        private String fetchRegister;

        @JsonProperty("usuario_modificacion_id")
        private Integer userModificationId;

        @JsonProperty("fecha_modificacion")
        private String fetchModification;
    }

    @Data
    public static class RolData {
        @JsonProperty("rol_id")
        private Integer rolId;

        @JsonProperty("servicio_id")
        private Integer serviceId;

        @JsonProperty("nombre")
        private String names;
    }
}
