package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class BusinessUnitDeleteValidationOutput {

    @JsonProperty("resp_estado")
    private Integer respStatus;

    @JsonProperty("resp_mensaje")
    private String respMessage;

    @JsonProperty("nombre")
    private List<String> roleNames;

    @JsonProperty("descripcion")
    private List<String> catalogDescriptions;

    @JsonProperty("usuario_config_nombres")
    private List<String> userFullNames;

    @JsonProperty("razon_social")
    private List<String> companyLegalNames;

    @JsonProperty("terminos")
    private List<String> termInfo;

    @JsonProperty("faqs")
    private List<String> faqInfo;
}