package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class RuleDeleteOutputDTO {

    @Data
    public static class Output {

        @JsonProperty("resp_estado")
        private Integer respEstado;

        @JsonProperty("resp_mensaje")
        private String respMensaje;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Output output;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        private Prefix prefix;

        public Root() {
            this.prefix = new Prefix();
            this.prefix.setOutput(new Output());
        }
    }
}
