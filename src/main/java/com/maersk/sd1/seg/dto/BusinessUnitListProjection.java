package com.maersk.sd1.seg.dto;

public interface BusinessUnitListProjection {
    Integer getBusinessUnitId();
    String getName();
    Boolean getStatus();
    Integer getParentBusinessUnitId();
    String getParentBusinessUnitName();
    Integer getSystemId();
    String getSystemName();
    String getSystemIcon();
    String getBusinessUnitAlias();

    String getConfiguration();
    String getCurrency();
}
