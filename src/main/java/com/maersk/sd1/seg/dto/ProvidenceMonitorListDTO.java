package com.maersk.sd1.seg.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class ProvidenceMonitorListDTO {
    private Integer providenceMonitoringId;
    private String manifestNumber;
    private String dinNumber;
    private String partialDelivery;
    private LocalDateTime pickupDate;
    private String lastPickup;
    private String ballotNumber;
    private LocalDateTime ballotDate;
    private String statusCode;
    private String statusGloss;
    private String errorCode;
    private String errorGloss;
    private String xmlRequest;
    private String xmlResponse;
    private Integer userRegistrationId;
    private String userRegistrationNames;
    private String userRegistrationSurnames;
    private LocalDateTime registrationDate;
    private Integer userModificationId;
    private String userModificationNames;
    private String userModificationSurnames;
    private LocalDateTime modificationDate;

    // Constructor matching the query result fields order
    public ProvidenceMonitorListDTO(
            Integer providenceMonitoringId,
            String manifestNumber,
            String dinNumber,
            String partialDelivery,
            LocalDateTime pickupDate,
            String lastPickup,
            String ballotNumber,
            LocalDateTime ballotDate,
            String statusCode,
            String statusGloss,
            String errorCode,
            String errorGloss,
            String xmlRequest,
            String xmlResponse,
            Integer userRegistrationId,
            String userRegistrationNames,
            String userRegistrationSurnames,
            LocalDateTime registrationDate,
            Integer userModificationId,
            String userModificationNames,
            String userModificationSurnames,
            LocalDateTime modificationDate
    ) {
        this.providenceMonitoringId = providenceMonitoringId;
        this.manifestNumber = manifestNumber;
        this.dinNumber = dinNumber;
        this.partialDelivery = partialDelivery;
        this.pickupDate = pickupDate;
        this.lastPickup = lastPickup;
        this.ballotNumber = ballotNumber;
        this.ballotDate = ballotDate;
        this.statusCode = statusCode;
        this.statusGloss = statusGloss;
        this.errorCode = errorCode;
        this.errorGloss = errorGloss;
        this.xmlRequest = xmlRequest;
        this.xmlResponse = xmlResponse;
        this.userRegistrationId = userRegistrationId;
        this.userRegistrationNames = userRegistrationNames;
        this.userRegistrationSurnames = userRegistrationSurnames;
        this.registrationDate = registrationDate;
        this.userModificationId = userModificationId;
        this.userModificationNames = userModificationNames;
        this.userModificationSurnames = userModificationSurnames;
        this.modificationDate = modificationDate;
    }
}
