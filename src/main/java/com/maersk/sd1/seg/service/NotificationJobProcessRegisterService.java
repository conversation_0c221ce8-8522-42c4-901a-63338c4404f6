package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.model.NotificationJob;
import com.maersk.sd1.common.model.NotificationJobHistory;
import com.maersk.sd1.seg.dto.NotificationJobProcessRegisterOutput;
import com.maersk.sd1.common.repository.NotificationJobHistoryRepository;
import com.maersk.sd1.common.repository.NotificationJobRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
public class NotificationJobProcessRegisterService {

    private static final Logger logger = LogManager.getLogger(NotificationJobProcessRegisterService.class);

    private final NotificationJobRepository notificationJobRepository;
    private final NotificationJobHistoryRepository notificationJobHistoryRepository;

    @Transactional
    public NotificationJobProcessRegisterOutput registerNotificationJobProcess(
            Integer notificacionJobId,
            Character indicadorEmailEjecutado,
            Character indicadorWebEjecutado,
            Character indicadorAppEjecutado,
            Integer usuarioRegistroId
    ) {
        NotificationJobProcessRegisterOutput output = new NotificationJobProcessRegisterOutput();
        try {
            NotificationJob job = notificationJobRepository.findById(notificacionJobId)
                    .orElseThrow(() -> new IllegalArgumentException("NotificationJob not found with id " + notificacionJobId));

            job.setLastExecutionDate(LocalDateTime.now());
            notificationJobRepository.saveAndFlush(job);

            NotificationJobHistory jobHistory = new NotificationJobHistory();
            jobHistory.setNotificacionJob(job);
            jobHistory.setDate(LocalDateTime.now());
            jobHistory.setIndicatorExecutedEmail(indicadorEmailEjecutado);
            jobHistory.setIndicatorWebExecuted(indicadorWebEjecutado);
            jobHistory.setIndicatorAppExecuted(indicadorAppEjecutado);

            notificationJobHistoryRepository.saveAndFlush(jobHistory);

            output.setRespNewId(jobHistory.getId());
            output.setRespEstado(1);
            output.setRespMensaje("Record successfully created.");
        } catch (Exception e) {
            logger.error("Error in registerNotificationJobProcess", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            output.setRespNewId(0);
        }
        return output;
    }
}
