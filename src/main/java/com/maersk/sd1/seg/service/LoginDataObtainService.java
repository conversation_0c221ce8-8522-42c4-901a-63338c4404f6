package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.seg.controller.dto.SystemValidationInput;
import com.maersk.sd1.seg.controller.dto.SystemValidationOutput;
import com.maersk.sd1.seg.dto.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.List;

import com.maersk.sd1.common.model.*;

@RequiredArgsConstructor
@Log4j2
@Service
public class LoginDataObtainService {

    private final BusinessUnitRepository businessUnitRepository;
    private final BusinessUnitConfigRepository businessUnitConfigRepository;
    private final RoleBusinessUnitRepository roleBusinessUnitRepository;
    private final MenuRepository menuRepository;
    private final TermRepository termRepository;
    private final FaqRepository faqRepository;
    private final UserQuestionsRepository userQuestionsRepository;
    private final SystemLanguageRepository systemLanguageRepository;
    private final SystemValidationService systemValidationService;
    private final ReglaListService reglaListService;

    public LoginDataObtainOutput getLoginData(Long userId, Long systemId) {
        log.info("Fetching login data for userId: {} and systemId: {}", userId, systemId);
        try {
            LoginDataObtainOutput output = new LoginDataObtainOutput();

            List<BusinessUnitProjection> busUnits = businessUnitRepository.findUserBusinessUnits(userId, systemId);
            output.setBusinessUnits(
                    busUnits.stream().map(b -> {
                        LoginDataObtainOutput.BusinessUnitItem item = new LoginDataObtainOutput.BusinessUnitItem();
                        item.setBusinessUnitId(b.getUnidadNegocioId());
                        item.setBusinessUnitName(b.getUnidadNegocio());
                        item.setBusinessUnitAlias(b.getAliasUnidadNegocio());

                        item.setPredefined(0);
                        item.setChildBusinessUnitId(null);

                        List<BusinessUnit> children = businessUnitRepository.findBusinessUnitChildren(b.getUnidadNegocioId());
                        List<LoginDataObtainOutput.SubUnitItem> subUnitItems = children.stream().map(ch -> {
                            LoginDataObtainOutput.SubUnitItem su = new LoginDataObtainOutput.SubUnitItem();
                            su.setBuId(ch.getId());
                            su.setName(ch.getName());
                            su.setAlias(ch.getBusinesUnitAlias());
                            return su;
                        }).toList();
                        item.setSubUnits(subUnitItems);

                        List<BusinessUnitConfig> configs = businessUnitConfigRepository.findBusinessUnitConfigsLOA(b.getUnidadNegocioId());
                        List<LoginDataObtainOutput.ConfigurationItem> configItems = configs.stream().map(cf -> {
                            LoginDataObtainOutput.ConfigurationItem ci = new LoginDataObtainOutput.ConfigurationItem();
                            ci.setConfigType(cf.getCatConfigurationType().getDescription());
                            ci.setConfigValue(cf.getValue());
                            return ci;
                        }).toList();
                        item.setConfigurations(configItems);
                        return item;
                    }).toList()
            );

            List<RoleBusinessUnitProjection> roleBusList = roleBusinessUnitRepository.findRolesForUser(userId);
            output.setRoles(
                    roleBusList.stream().map(rb -> {
                        LoginDataObtainOutput.RoleItem ri = new LoginDataObtainOutput.RoleItem();
                        ri.setRoleId(rb.getRolId());
                        ri.setBusinessUnitId(rb.getUnidadNegocioId());
                        ri.setRoleCode(rb.getId());
                        return ri;
                    }).toList()
            );

            List<Menu> parentMenus = menuRepository.findParentMenusForUserSystem(userId, systemId);
            output.setProjects(
                    parentMenus.stream().map(pm -> {
                        LoginDataObtainOutput.MenuItem mi = new LoginDataObtainOutput.MenuItem();
                        mi.setTemplate(pm.getTemplate());
                        mi.setTitle(pm.getTitle());
                        mi.setIcon(pm.getIcon());
                        mi.setMenuId(pm.getId());
                        mi.setOrder(pm.getOrder());
                        return mi;
                    }).toList()
            );

            List<Menu> projectsByBu = menuRepository.findProjectsByBusinessUnit(userId);
            output.setProjectsByBusinessUnit(
                    projectsByBu.stream().map(pb -> {
                        LoginDataObtainOutput.BusinessUnitProjectItem bpi = new LoginDataObtainOutput.BusinessUnitProjectItem();
                        bpi.setTemplate(pb.getTemplate());
                        bpi.setBusinessUnitId(null);
                        return bpi;
                    }).toList()
            );

            SystemValidationInput.Input input = new SystemValidationInput.Input();

            input.setUserId(userId.intValue());
            input.setSystemId(systemId.intValue());

            SystemValidationOutput validationResult = systemValidationService.validateSystem(input);
            output.setSystemValidation(validationResult);


            ReglaListInputDTO.Input reglaInput = new ReglaListInputDTO.Input();

            reglaInput.setSistemaId(systemId.intValue());

            ReglaListOutputDTO.Output rulesJson = reglaListService.getReglaList(reglaInput);
            output.setRules(rulesJson);

            List<Integer> termBus = termRepository.findBusinessUnitsForTerms(userId);
            output.setBusinessUnitsForTerms(termBus);

            List<Integer> faqBus = faqRepository.findBusinessUnitsForFaq(userId);
            output.setBusinessUnitsForFaq(faqBus);

            List<BusinessUnit> subUnitsFunction = businessUnitRepository.findSubUnitsFunction(userId);
            output.setSubUnitsFunction(
                    subUnitsFunction.stream().map(su -> {
                        LoginDataObtainOutput.BusinessUnitSubFunctionItem sf = new LoginDataObtainOutput.BusinessUnitSubFunctionItem();
                        sf.setBusinessUnitId(su.getId());
                        sf.setName(su.getName());
                        sf.setParentBusinessUnitId(su.getParentBusinessUnit() != null ? su.getParentBusinessUnit().getId() : null);
                        sf.setAlias(su.getBusinesUnitAlias());
                        return sf;
                    }).toList()
            );

            List<SystemLanguage> sysLangs = systemLanguageRepository.findLanguagesForSystem(systemId);
            output.setLanguages(
                    sysLangs.stream().map(sl -> {
                        LoginDataObtainOutput.LanguageItem li = new LoginDataObtainOutput.LanguageItem();
                        li.setLanguageId(sl.getLanguage().getId());
                        li.setCode(sl.getLanguage().getCode());
                        li.setName(sl.getLanguage().getName());
                        li.setDefaultFlag("0");
                        return li;
                    }).toList()
            );

            List<BusinessUnit> sysBUChildren = businessUnitRepository.findSystemChildrenBusUnits(systemId);
            output.setSystemBusinessUnitChildren(
                    sysBUChildren.stream().map(un -> {
                        LoginDataObtainOutput.BusinessUnitItem item = new LoginDataObtainOutput.BusinessUnitItem();
                        item.setBusinessUnitId(un.getId());
                        item.setBusinessUnitName(un.getName());
                        item.setBusinessUnitAlias(un.getBusinesUnitAlias());
                        item.setPredefined(0);
                        return item;
                    }).toList()
            );

            String hasQuestions = userQuestionsRepository.userHasQuestions(userId);
            output.setHasQuestions(hasQuestions);

            return output;
        } catch (Exception e) {
            log.error("Error fetching login data", e);
            throw new LoginDataObtainException("Error fetching login data: " + e.getMessage(), e);
        }
    }

    public static class LoginDataObtainException extends RuntimeException {
        public LoginDataObtainException(String message) {
            super(message);
        }

        public LoginDataObtainException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
