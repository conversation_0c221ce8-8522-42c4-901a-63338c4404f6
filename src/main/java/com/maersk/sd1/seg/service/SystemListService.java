package com.maersk.sd1.seg.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.model.System;
import com.maersk.sd1.common.repository.SystemRepository;
import com.maersk.sd1.seg.dto.SystemsListInput;
import com.maersk.sd1.seg.dto.SystemsListOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.criteria.Predicate;
import jakarta.validation.ValidationException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class SystemListService {

    private static final Logger logger = LogManager.getLogger(SystemListService.class);

    private final SystemRepository systemRepository;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Transactional(readOnly = true)
    public SystemsListOutput listSystems(SystemsListInput.Input input) {

        validateInput(input);
        Long totalRecords = countSystems(input);
        int page = (input.getPage() == null) ? 1 : input.getPage();
        Integer size;

        if (input.getSize() == null) {
            if (totalRecords == 0L) {
                size = 1;
            } else {
                size = totalRecords.intValue();
            }
        } else {
            size = input.getSize();
        }


        PageRequest pageRequest = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.ASC, "description"));
        Specification<System> spec = buildSpecification(input);
        Page<System> systemsPage = systemRepository.findAll(spec, pageRequest);

        SystemsListOutput output = new SystemsListOutput();
        output.setTotalRecords(totalRecords);
        output.setSystems(mapToDetailList(systemsPage.getContent()));
        return output;
    }

    public List<SystemsListOutput.SystemsListDetail> mapToDetailList(List<System> systems) {
        List<SystemsListOutput.SystemsListDetail> detailList = new ArrayList<>();
        for (System s : systems) {
            SystemsListOutput.SystemsListDetail detail = new SystemsListOutput.SystemsListDetail();
            detail.setSystemId(s.getId());
            detail.setName(s.getName());
            detail.setDescription(s.getDescription());
            detail.setStatus(s.getStatus());
            detail.setRegistrationDate(s.getRegistrationDate());
            detail.setModificationDate(s.getModificationDate());
            detail.setRegistrationUserId(s.getRegistrationUser() == null ? null : s.getRegistrationUser().getId());
            detail.setModificationUserId(s.getModificationUser() == null ? null : s.getModificationUser().getId());
            if (s.getRegistrationUser() != null) {
                detail.setRegistrationUserNames(s.getRegistrationUser().getNames());
                String firstLast = s.getRegistrationUser().getFirstLastName() != null ? s.getRegistrationUser().getFirstLastName() : "";
                String secondLast = s.getRegistrationUser().getSecondLastName() != null ? s.getRegistrationUser().getSecondLastName() : "";
                detail.setRegistrationUserLastNames((firstLast + " " + secondLast).trim());
            }
            if (s.getModificationUser() != null) {
                detail.setModificationUserNames(s.getModificationUser().getNames());
                String firstLast = s.getModificationUser().getFirstLastName() != null ? s.getModificationUser().getFirstLastName() : "";
                String secondLast = s.getModificationUser().getSecondLastName() != null ? s.getModificationUser().getSecondLastName() : "";
                detail.setModificationUserLastNames((firstLast + " " + secondLast).trim());
            }
            try {

                detail.setConfiguration(objectMapper.writeValueAsString(s.getConfiguration()));
            } catch (JsonProcessingException e) {
                logger.error("Error converting configuration to string", e);
                detail.setConfiguration(null);
            }
            detailList.add(detail);
        }

        return detailList;
    }

    public Long countSystems(SystemsListInput.Input input) {
        Specification<System> spec = buildSpecification(input);
        return systemRepository.count(spec);

    }

    public Specification<System> buildSpecification(SystemsListInput.Input input) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (input.getSystemId() != null) {
                predicates.add(cb.equal(root.get("id"), input.getSystemId()));
            }
            if (input.getName() != null && !input.getName().isEmpty()) {
                predicates.add(cb.like(cb.lower(root.get("name").as(String.class)), "%" + input.getName().toLowerCase() + "%"));
            }
            if (input.getDescription() != null && !input.getDescription().isEmpty()) {
                predicates.add(cb.like(cb.lower(root.get("description").as(String.class)), "%" + input.getDescription().toLowerCase() + "%"));
            }
            if (input.getStatus() != null) {
                predicates.add(cb.equal(root.get("status"), input.getStatus()));
            }
            LocalDate regMin = input.getRegistrationDateMin();
            LocalDate regMax = input.getRegistrationDateMax();
            if (regMin != null && regMax != null) {
                LocalDateTime start = regMin.atStartOfDay();
                LocalDateTime end = regMax.plusDays(1).atStartOfDay();
                predicates.add(cb.greaterThanOrEqualTo(root.get("registrationDate"), start));
                predicates.add(cb.lessThan(root.get("registrationDate"), end));
            }
            LocalDate modMin = input.getModificationDateMin();
            LocalDate modMax = input.getModificationDateMax();
            if (modMin != null && modMax != null) {
                LocalDateTime start = modMin.atStartOfDay();
                LocalDateTime end = modMax.plusDays(1).atStartOfDay();
                predicates.add(cb.greaterThanOrEqualTo(root.get("modificationDate"), start));
                predicates.add(cb.lessThan(root.get("modificationDate"), end));
            }
            if (input.getConfiguration() != null && !input.getConfiguration().isEmpty()) {
                String configString = input.getConfiguration().toString().toLowerCase();
                predicates.add(cb.like(cb.lower(root.get("configuration").as(String.class)), "%" + configString + "%"));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }

    public void validateInput(SystemsListInput.Input input) {
        if (input.getPage() != null && input.getPage() < 1) {
            throw new ValidationException("Page index must be at least 1");
        }
        if (input.getSize() != null && input.getSize() < 1) {
            throw new ValidationException("Size must be at least 1");
        }
    }
}