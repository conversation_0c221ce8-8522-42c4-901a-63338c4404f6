package com.maersk.sd1.sdf.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
public class DocumentationFullListOutput {

    @JsonProperty("total_records")
    private Long totalRecords;

    @JsonProperty("data")
    private List<DocumentationFullListItemDTO> data;

    @Data
    public static class DocumentationFullListItemDTO {

        @JsonProperty("RowID")
        private Integer rowId;

        @JsonProperty("transport_planning_id")
        private Integer transportPlanningId;

        @JsonProperty("document_type")
        private String documentType;

        @JsonProperty("document_number")
        private String documentNumber;

        @JsonProperty("movement_type")
        private String movementType;

        @JsonProperty("shipper")
        private String shipper;

        @JsonProperty("consignee")
        private String consignee;

        @JsonProperty("cat_move_type_trk_id")
        private Integer catTypeId;

        @JsonProperty("status")
        private String status;

        @JsonProperty("status_id")
        private Integer statusId;

        @JsonProperty("commodity")
        private String commodity;

        @JsonProperty("product")
        private String product;

        @JsonProperty("operation_type")
        private String operationType;

        @JsonProperty("shipping_line")
        private String shippingLine;

        @JsonProperty("user_registration_id")
        private Integer userRegistrationId;

        @JsonProperty("user_registration_name")
        private String userRegistrationName;

        @JsonProperty("user_registration_lastname")
        private String userRegistrationLastname;

        @JsonProperty("user_registration_date")
        private String userRegistrationDate;

        @JsonProperty("user_modification_id")
        private Integer userModificationId;

        @JsonProperty("user_modification_name")
        private String userModificationName;

        @JsonProperty("user_modification_lastname")
        private String userModificationLastname;

        @JsonProperty("user_modification_date")
        private String userModificationDate;

        @JsonProperty("creation_source")
        private String creationSource;

        @JsonProperty("detail")
        private String detail;

        @JsonProperty("comments")
        private String comments;

        @JsonProperty("transport_date")
        private String transportDate;

        @JsonProperty("RowID_Alternative")
        private Integer rowIdAlternative;
    }
}