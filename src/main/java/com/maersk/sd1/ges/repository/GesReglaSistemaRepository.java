package com.maersk.sd1.ges.repository;

import com.maersk.sd1.common.model.SystemRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface GesReglaSistemaRepository extends JpaRepository<SystemRule, Integer> {

    @Query(value = "SELECT RSI.regla FROM ges.regla_sistema RSI WITH (NOLOCK) WHERE RSI.id = :id", nativeQuery = true)
    String findReglaById(@Param("id") String id);
}
