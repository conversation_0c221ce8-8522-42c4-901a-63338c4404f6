package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Output DTO to represent the results of empresa_listar logic: total records, plus a list of companies.
 */
@Data
public class CompanyListOutputDTO {

    @JsonProperty("total_registros")
    private Long totalRecords;

    @JsonProperty("lista_empresas")
    private List<CompanyData> companyDataList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CompanyData {

        @JsonProperty("empresa_id")
        private Integer companyId;

        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;

        @JsonProperty("unidad_negocio_nombre")
        private String businessUnitName;

        @JsonProperty("tipo_documento")
        private String documentType;

        @JsonProperty("documento")
        private String document;

        @JsonProperty("razon_social")
        private String legalName;

        @JsonProperty("razon_comercial")
        private String commercialName;

        @JsonProperty("direccion")
        private String address;

        @JsonProperty("longitud")
        private String longitude;

        @JsonProperty("latitud")
        private String latitude;

        @JsonProperty("estado")
        private Boolean status;

        @JsonProperty("fecha_registro")
        private LocalDateTime registrationDate;

        @JsonProperty("fecha_modificacion")
        private LocalDateTime modificationDate;

        @JsonProperty("suspendido")
        private Boolean suspended = false;

        @JsonProperty("telefono")
        private String phone;

        @JsonProperty("correo")
        private String mail;

        @JsonProperty("abreviatura")
        private String abbreviation;

        @JsonProperty("roles")
        private List<RolData> roles;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RolData {
        @JsonProperty("catalogo_id")
        private Integer catalogId;

        @JsonProperty("descripcion")
        private String description;
    }
}

