package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class EmailPendingProcessInput {

    @Data
    public static class Input {
        // No required fields based on the stored procedure (no input parameters)
        // If needed, add fields here with appropriate validation
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @Valid
        @NotNull(message = "Input cannot be null")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        @Valid
        @NotNull(message = "Prefix cannot be null")
        private Prefix prefix;
    }
}

