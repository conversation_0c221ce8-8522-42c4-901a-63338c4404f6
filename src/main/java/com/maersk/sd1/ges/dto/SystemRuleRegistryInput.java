package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class SystemRuleRegistryInput {

    @Data
    public static class Input {
        @JsonProperty("id")
        @NotBlank(message = "System rule alias cannot be null or blank")
        @Size(max = 50)
        private String alias;

        @JsonProperty("sistema_id")
        @NotNull(message = "System ID cannot be null")
        private Long systemId;

        @JsonProperty("unidad_negocio_id")
        private Long businessUnitId;

        @JsonProperty("sub_unidad_negocio_id")
        private Long subBusinessUnitId;

        @JsonProperty("descripcion")
        @NotBlank(message = "Description cannot be null or blank")
        @Size(max = 100)
        private String description;

        @JsonProperty("regla")
        @NotBlank(message = "Rule cannot be null or blank")
        private String rule;

        @JsonProperty("activo")
        @NotNull(message = "Active cannot be null")
        private Boolean active;

        @JsonProperty("usuario_registro_id")
        @NotNull(message = "Registration user ID cannot be null")
        private Long userRegistrationId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private Prefix prefix;
    }
}

