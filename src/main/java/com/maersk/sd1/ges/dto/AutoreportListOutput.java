package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class AutoreportListOutput {

    @JsonProperty("resp_estado")
    private Integer respEstado;

    @JsonProperty("resp_mensaje")
    private String respMensaje;

    @JsonProperty("total_registros")
    private Long totalRegistros;

    @JsonProperty("records")
    private List<AutoreportListRecord> records;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AutoreportListRecord {
        @JsonProperty("automatic_report_id")
        private Integer automaticReportId;

        @JsonProperty("alias")
        private String alias;

        @JsonProperty("report_start_date")
        private String reportStartDate;

        @JsonProperty("report_end_date")
        private String reportEndDate;

        @JsonProperty("active")
        private Boolean active;

        @JsonProperty("recurrency_cat_id")
        private Integer recurrencyCatId;

        @JsonProperty("recurrency_description")
        private String recurrencyDescription;

        @JsonProperty("report_id")
        private Integer reportId;

        @JsonProperty("report_name")
        private String reportName;

        @JsonProperty("report_description")
        private String reportDescription;

        @JsonProperty("email_template_id")
        private Integer emailTemplateId;

        @JsonProperty("email_template_title")
        private String emailTemplateTitle;

        @JsonProperty("email_template_description")
        private String emailTemplateDescription;

        @JsonProperty("user_registration_id")
        private Integer userRegistrationId;

        @JsonProperty("registration_date")
        private String registrationDate;

        @JsonProperty("user_registration_name")
        private String userRegistrationName;

        @JsonProperty("user_registration_lastname")
        private String userRegistrationLastName;

        @JsonProperty("user_modification_id")
        private Integer userModificationId;

        @JsonProperty("modification_date")
        private String modificationDate;

        @JsonProperty("user_modification_name")
        private String userModificationName;

        @JsonProperty("user_modification_lastname")
        private String userModificationLastName;
    }
}
