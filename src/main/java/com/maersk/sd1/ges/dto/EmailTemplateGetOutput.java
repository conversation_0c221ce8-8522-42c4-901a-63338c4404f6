package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class EmailTemplateGetOutput {

    @JsonProperty("resp_estado")
    private Integer respEstado;

    @JsonProperty("resp_mensaje")
    private String respMensaje;

    // Individual fields from EmailTemplate
    @JsonProperty("email_plantilla_id")
    private Integer emailPlantillaId;

    @JsonProperty("email_plantilla_padre_id")
    private Integer emailPlantillaPadreId;

    @JsonProperty("contenido")
    private String contenido;

    @JsonProperty("destinatario")
    private String destinatario;

    @JsonProperty("copia")
    private String copia;

    @JsonProperty("copia_oculta")
    private String copiaOculta;

    @JsonProperty("titulo")
    private String titulo;

    @JsonProperty("estado")
    private Boolean estado;

    @JsonProperty("fecha_registro")
    private LocalDateTime fechaRegistro;

    @JsonProperty("fecha_modificacion")
    private LocalDateTime fechaModificacion;

    @JsonProperty("descripcion")
    private String descripcion;

    @JsonProperty("envio_usuario")
    private Boolean envioUsuario;

    @JsonProperty("indicador_habilitado")
    private Boolean indicadorHabilitado;

    @JsonProperty("menu_proyecto_id")
    private Integer menuProyectoId;

    @JsonProperty("id")
    private String id1;

    @JsonProperty("remitente")
    private String remitente;

    @JsonProperty("evento_despues_enviar")
    private String eventoDespuesEnviar;

    // Collections from relations
    @JsonProperty("email_atributos")
    private List<EmailAttributeDto> emailAtributos;

    @JsonProperty("email_plantilla_roles")
    private List<EmailTemplateRoleDto> emailPlantillaRoles;

    @Data
    public static class EmailAttributeDto {
        @JsonProperty("email_atributo_id")
        private Integer emailAtributoId;

        @JsonProperty("email_plantilla_id")
        private Integer emailPlantillaId;

        @JsonProperty("campo")
        private String campo;
    }

    @Data
    public static class EmailTemplateRoleDto {
        @JsonProperty("email_plantilla_rol_id")
        private Integer emailPlantillaRolId;

        @JsonProperty("rol_id")
        private Integer rolId;

        @JsonProperty("email_plantilla_id")
        private Integer emailPlantillaId;
    }
}

