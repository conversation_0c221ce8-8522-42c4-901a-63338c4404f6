package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class ListBusinessLineInput {

    @Data
    public static class Input {

        @JsonProperty("unidad_negocio_id")
        @NotNull
        private Integer unidadNegocioId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDG")
        private Prefix prefix;
    }
}

