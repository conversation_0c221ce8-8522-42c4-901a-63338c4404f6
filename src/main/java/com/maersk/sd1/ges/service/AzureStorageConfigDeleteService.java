package com.maersk.sd1.ges.service;

import com.maersk.sd1.common.model.AzureStorageConfig;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.AzureStorageConfigRepository;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.ges.dto.AzureStorageConfigDeleteOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
public class AzureStorageConfigDeleteService {

    private static final Logger logger = LogManager.getLogger(AzureStorageConfigDeleteService.class);


    private final AzureStorageConfigRepository azureStorageConfigRepository;

    private final UserRepository userRepository;

    @Autowired
    public AzureStorageConfigDeleteService(AzureStorageConfigRepository azureStorageConfigRepository, UserRepository userRepository) {
        this.azureStorageConfigRepository = azureStorageConfigRepository;
        this.userRepository = userRepository;
    }

    @Transactional
    public AzureStorageConfigDeleteOutput deleteAzureStorageConfig(Integer azureStorageConfigId, Integer usuarioModificacionId) {
        AzureStorageConfigDeleteOutput output = new AzureStorageConfigDeleteOutput();
        try {
            Optional<AzureStorageConfig> optionalConfig = azureStorageConfigRepository.findById(azureStorageConfigId);
            if (optionalConfig.isEmpty()) {
                output.setRespEstado(0);
                output.setRespMensaje("No se encontró el registro para eliminar");
                return output;
            }
            AzureStorageConfig config = optionalConfig.get();

            Optional<User> optionalUser = userRepository.findById(usuarioModificacionId);
            if (optionalUser.isEmpty()) {
                output.setRespEstado(0);
                output.setRespMensaje("El usuario de modificación no existe");
                return output;
            }

            User modifyingUser = optionalUser.get();

            config.setStatus(false);
            config.setModificationUser(modifyingUser);
            config.setModificationDate(LocalDateTime.now());

            azureStorageConfigRepository.save(config);

            output.setRespEstado(1);
            output.setRespMensaje("Registro eliminado correctamente");
        } catch (Exception e) {
            logger.error("Error while deleting AzureStorageConfig", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}
