package com.maersk.sd1.ges.service;

import com.maersk.sd1.common.model.Report;
import com.maersk.sd1.ges.dto.ReportObtainOutput;
import com.maersk.sd1.common.repository.ReportRepository;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class ReportObtainService {

    private static final Logger logger = LogManager.getLogger(ReportObtainService.class);

    private final ReportRepository reportRepository;

    public ReportObtainService(ReportRepository reportRepository) {
        this.reportRepository = reportRepository;
    }

    @Transactional
    public ReportObtainOutput getReport(Integer reportId) {
        logger.info("Starting getReport for reporte_id: {}", reportId);
        ReportObtainOutput output = new ReportObtainOutput();

        try {
            Report report = reportRepository.findByReportId(reportId)
                    .orElseThrow(() -> {
                        logger.error("Report not found for id: {}", reportId);
                        return new RuntimeException("Report not found");
                    });

            List<Integer> roleIds = reportRepository.findRolIdsByReportId(reportId);

            output.setReporteId(report.getId());
            output.setMenuId(report.getMenu() != null ? report.getMenu().getId() : null);
            output.setName(report.getName());
            output.setDescription(report.getDescription());
            output.setNameStore(report.getNameStore());
            output.setParameters(report.getParameters());
            output.setColumns(report.getColumns());
            output.setStatus(report.getStatus());
            output.setRoles(roleIds);

            output.setRespEstado(1);
            output.setRespMensaje("Success");

        } catch (Exception e) {
            logger.error("Error in getReport for reporte_id: {}", reportId, e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }

        return output;
    }
}

