package com.maersk.sd1.ges.service;

import com.maersk.sd1.common.model.SftpConfig;
import com.maersk.sd1.common.model.SftpPendingSend;
import com.maersk.sd1.ges.dto.SftpConfirmOutput;
import com.maersk.sd1.common.repository.SftpPendingSendRepository;
import jakarta.persistence.EntityManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDateTime;

@Service
public class SftpConfirmService {

    private static final Logger logger = LogManager.getLogger(SftpConfirmService.class);

    private final SftpPendingSendRepository sftpPendingSendRepository;
    private final EntityManager entityManager;

    @Autowired
    public SftpConfirmService(SftpPendingSendRepository sftpPendingSendRepository, EntityManager entityManager) {
        this.sftpPendingSendRepository = sftpPendingSendRepository;
        this.entityManager = entityManager;
    }

    @Transactional
    public SftpConfirmOutput confirmSftpPendingSend(Integer sftpPendienteEnviarId, Integer usuarioId) {
        SftpConfirmOutput output = new SftpConfirmOutput();
        try {
            SftpPendingSend sftpPendingSend = sftpPendingSendRepository.findById(sftpPendienteEnviarId)
                    .orElseThrow(() -> new IllegalArgumentException(String.format("No sftpPendingSend found for ID: %d", sftpPendienteEnviarId)));

            sftpPendingSend.setSentIndicator(true);
            sftpPendingSend.setModificationDate(LocalDateTime.now());
            sftpPendingSendRepository.save(sftpPendingSend);

            Integer reference = Integer.valueOf(sftpPendingSend.getReference());

            SftpConfig config = sftpPendingSend.getSftpConfig();
            if (config != null && config.getEventAfterUpload() != null) {
                callDynamicProcedure(config.getEventAfterUpload(), sftpPendienteEnviarId, reference);
            }

            output.setResponseStatus(1);
            output.setResponseMessage("Updated successfully");
            logger.info("SFTP pending send with ID {} updated successfully.", sftpPendienteEnviarId);
        } catch (Exception e) {
            logger.error("Error updating sftp pending send with ID {}", sftpPendienteEnviarId, e);

            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            String errorMessage = e.getMessage();
            output.setResponseStatus(0);
            output.setResponseMessage(errorMessage +" for sftpPendienteEnviarId: " + sftpPendienteEnviarId + " and usuarioId: " + usuarioId);
        }
        return output;
    }

    public void callDynamicProcedure(String eventAfterUpload, Integer sftpPendienteEnviarId, Integer reference) {
        logger.info("Calling dynamic procedure '{}', with params: sftpPendienteEnviarId={}, reference={}",
                eventAfterUpload, sftpPendienteEnviarId, reference);
        try {
            String dynamicSql = String.format("EXEC %s %d, %d", eventAfterUpload, sftpPendienteEnviarId, reference);
            entityManager.createNativeQuery(dynamicSql).executeUpdate();
        } catch (Exception exProc) {
            logger.error("Error calling dynamic procedure: {}", eventAfterUpload, exProc);
            throw exProc;
        }
    }
}