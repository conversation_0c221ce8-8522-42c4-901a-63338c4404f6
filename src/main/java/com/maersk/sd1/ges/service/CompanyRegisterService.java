package com.maersk.sd1.ges.service;

import com.maersk.sd1.ges.dto.CompanyRegisterInput;
import com.maersk.sd1.ges.dto.CompanyRegisterOutput;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public class CompanyRegisterService {
    private static final Logger logger = LogManager.getLogger(CompanyRegisterService.class);

    private final CompanyRepository companyRepository;
    private final CompanyContactRepository companyContactRepository;
    private final CompanyAddressRepository companyAddressRepository;
    private final CompanyRoleRepository companyRoleRepository;
    private final CompanyConfigRepository companyConfigRepository;
    private final CompanyBusinessLineRepository companyBusinessLineRepository;

    @Autowired
    public CompanyRegisterService(CompanyRepository companyRepository, CompanyContactRepository companyContactRepository, CompanyAddressRepository companyAddressRepository, CompanyRoleRepository companyRoleRepository, CompanyConfigRepository companyConfigRepository, CompanyBusinessLineRepository companyBusinessLineRepository) {
        this.companyRepository = companyRepository;
        this.companyContactRepository = companyContactRepository;
        this.companyAddressRepository = companyAddressRepository;
        this.companyRoleRepository = companyRoleRepository;
        this.companyConfigRepository = companyConfigRepository;
        this.companyBusinessLineRepository = companyBusinessLineRepository;
    }

    @Transactional
    public CompanyRegisterOutput registerCompany(CompanyRegisterInput.Input input) {
        CompanyRegisterOutput output = new CompanyRegisterOutput();
        try {
            User registrationUser = User.builder().id(input.getRegisteredUserId()).build();

            User modificationUser = null;
            if (input.getModifiedUserId() != null) {
                modificationUser = User.builder().id(input.getModifiedUserId()).build();
            }
            BusinessUnit businessUnit = null;
            if (input.getBusinessUnitId() != null) {
                businessUnit = BusinessUnit.builder().id(input.getBusinessUnitId()).build();
            }
            Catalog docType = input.getDocumentType() != null ? Catalog.builder().id(input.getDocumentType()).build() : null;

            Company company = Company.builder()
                    .document(input.getDocument())
                    .legalName(input.getLegalName())
                    .commercialName(input.getTradeName())
                    .address(input.getAddress())
                    .longitude(input.getLongitude())
                    .latitude(input.getLatitude())
                    .phone(input.getPhone())
                    .mail(input.getEmail())
                    .abbreviation(input.getAbbreviation())
                    .status(true)
                    .suspended(false)
                    .registrationDate(LocalDateTime.now())
                    .modificationDate(LocalDateTime.now())
                    .registrationUser(registrationUser)
                    .modificationUser(modificationUser)
                    .businessUnit(businessUnit)
                    .catDocumentType(docType)
                    .build();

            company = companyRepository.save(company);

            if (input.getContacts() != null && !input.getContacts().isEmpty()) {
                List<CompanyContact> contactList = new ArrayList<>();
                for (CompanyRegisterInput.Contact dto : input.getContacts()) {
                    CompanyContact contact = CompanyContact.builder()
                            .id(dto.getId())
                            .names(dto.getFirstName())
                            .firstLastName(dto.getLastNameFather())
                            .secondLastName(dto.getLastNameMother())
                            .phone(dto.getPhone())
                            .mail(dto.getEmail())
                            .catContactType(dto.getContactTypeId() != null ? Catalog.builder().id(dto.getContactTypeId()).build() : null)
                            .company(company)
                            .build();
                    contactList.add(contact);
                }
                companyContactRepository.saveAll(contactList);
            }

            if (input.getAddresses() != null && !input.getAddresses().isEmpty()) {
                List<CompanyAddress> addressList = new ArrayList<>();
                for (CompanyRegisterInput.Address dto : input.getAddresses()) {
                    CompanyAddress address = CompanyAddress.builder()
                            .id(dto.getId())
                            .address(dto.getAddress())
                            .phone(dto.getPhone())
                            .mail(dto.getEmail())
                            .longitude(dto.getLongitude())
                            .latitude(dto.getLatitude())
                            .name(dto.getName())
                            .ubigeo(dto.getLocationId() != null ? Ubigeo.builder().id(dto.getLocationId()).build() : null)
                            .addressType(dto.getAddressTypeId() != null ? Catalog.builder().id(dto.getAddressTypeId()).build() : null)
                            .empresa(company)
                            .build();
                    addressList.add(address);
                }
                companyAddressRepository.saveAll(addressList);
            }

            if (input.getRoles() != null && !input.getRoles().isEmpty()) {
                List<CompanyRole> roleList = new ArrayList<>();
                for (CompanyRegisterInput.Role dto : input.getRoles()) {
                    CompanyRole role = CompanyRole.builder()
                            .id(null)
                            .company(company)
                            .catRoleType(Catalog.builder().id(dto.getRoleTypeId()).build())
                            .build();
                    roleList.add(role);
                }
                companyRoleRepository.saveAll(roleList);
            }

            if (input.getConfigurations() != null && !input.getConfigurations().isEmpty()) {
                List<CompanyConfig> configList = new ArrayList<>();
                for (CompanyRegisterInput.Configuration dto : input.getConfigurations()) {
                    CompanyConfig config = CompanyConfig.builder()
                            .id(CompanyConfigId.builder().companyId(company.getId()).typeConfigurationId(dto.getConfigurationTypeId()).build())
                            .company(company)
                            .catConfigurationType(Catalog.builder().id(dto.getConfigurationTypeId()).build())
                            .value(dto.getValue())
                            .build();
                    configList.add(config);
                }
                companyConfigRepository.saveAll(configList);
            }

            if (input.getCompanyBusinessLine() != null && !input.getCompanyBusinessLine().isEmpty()) {
                List<CompanyBusinessLine> businessLineList = new ArrayList<>();
                for (CompanyRegisterInput.BusinessLine dto : input.getCompanyBusinessLine()) {
                    CompanyBusinessLine cbl = CompanyBusinessLine.builder()
                            .id(CompanyTurnBusinessId.builder().companyId(company.getId()).turnBusinessId(dto.getBusinessLineId()).build())
                            .company(company)
                            .businessLine(BusinessLine.builder().id(dto.getBusinessLineId()).build())
                            .build();
                    businessLineList.add(cbl);
                }
                companyBusinessLineRepository.saveAll(businessLineList);
            }

            output.setResponseStatus(1);
            output.setResponseMessage("Registered successfully");
            return output;
        } catch (Exception ex) {
            logger.error("Error registering Company", ex);
            output.setResponseStatus(0);
            output.setResponseMessage("0 | " + ex.getMessage());
            return output;
        }
    }
}
