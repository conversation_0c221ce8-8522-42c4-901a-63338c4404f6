package com.maersk.sd1.ges.service;

import com.maersk.sd1.common.model.AutomaticReport;
import com.maersk.sd1.common.repository.AutomaticReportRepository;
import com.maersk.sd1.ges.dto.AutoreportDeleteOutput;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Service
public class AutoreportDeleteService {

    private static final Logger logger = LogManager.getLogger(AutoreportDeleteService.class);
    private final AutomaticReportRepository automaticReportRepository;

    @Transactional
    public AutoreportDeleteOutput deleteAutoreport(Integer autoreportId) {
        AutoreportDeleteOutput output = new AutoreportDeleteOutput();
        try {
            AutomaticReport automaticReport = automaticReportRepository.findById(autoreportId)
                    .orElseThrow(() -> new EntityNotFoundException("Automatic report not found for id " + autoreportId));

            // Set active to false
            automaticReport.setActive(false);
            automaticReportRepository.save(automaticReport);

            // Populate output
            output.setRespEstado(1);
            output.setRespMensaje("Automatic report deleted successfully");
            output.setAutoreportAlias(automaticReport.getAlias());
        } catch (Exception e) {
            logger.error("Error deleting automatic report with id {}: {}", autoreportId, e.getMessage(), e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}
