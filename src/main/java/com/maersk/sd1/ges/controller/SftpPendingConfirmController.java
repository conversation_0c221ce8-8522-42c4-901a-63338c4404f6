package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.SftpConfirmInput;
import com.maersk.sd1.ges.dto.SftpConfirmOutput;
import com.maersk.sd1.ges.service.SftpConfirmService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleADM/module/adm/sftpConfirmService")
public class SftpPendingConfirmController {

    private static final Logger logger = LogManager.getLogger(SftpPendingConfirmController.class);

    private final SftpConfirmService sftpConfirmService;
    
    @Autowired
    public SftpPendingConfirmController(SftpConfirmService sftpConfirmService) {
        this.sftpConfirmService = sftpConfirmService;
    }

    @PostMapping("/confirmPendingSend")
    public ResponseEntity<ResponseController<SftpConfirmOutput>> confirmPendingSend(@RequestBody @Valid SftpConfirmInput.Root request) {
        try {
            SftpConfirmInput.Input input = request.getPrefix().getInput();
            SftpConfirmOutput output = sftpConfirmService.confirmSftpPendingSend(
                    input.getSftpPendienteEnviarId(),
                    input.getUsuarioId()
            );
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing confirmPendingSend.", e);
            SftpConfirmOutput output = new SftpConfirmOutput();
            output.setResponseStatus(0);
            output.setResponseMessage(e.toString());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}
