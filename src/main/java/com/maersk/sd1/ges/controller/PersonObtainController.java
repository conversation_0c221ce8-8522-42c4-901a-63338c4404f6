package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.PersonObtainInput;
import com.maersk.sd1.ges.dto.PersonObtainOutput;
import com.maersk.sd1.ges.service.PersonObtainService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("ModuleADM/module/adm/ADMPersonaServiceImp")
public class PersonObtainController {

    private static final Logger logger = LogManager.getLogger(PersonObtainController.class.getName());

    private PersonObtainService personObtainService;

    @Autowired
    public PersonObtainController(PersonObtainService personObtainService) {
        this.personObtainService = personObtainService;
    }

    @PostMapping("/gespersonaObtener")
    public ResponseEntity<ResponseController<PersonObtainOutput>> obtainPerson(@RequestBody @Valid PersonObtainInput.Root request) {
        try {
            Integer personaId = request.getGes().getInput().getPersonaId();
            PersonObtainOutput output = personObtainService.getPerson(personaId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (PersonObtainService.InvalidPersonIdException re) {
            throw re;
        } catch (PersonObtainService.PersonNotFoundException e) {
            throw e;
        } catch (Exception e) {
            logger.error("An error occurred while processing obtainPerson.", e);
            PersonObtainOutput errorOutput = new PersonObtainOutput();
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }

    }

    @ExceptionHandler(PersonObtainService.InvalidPersonIdException.class)
    public ResponseEntity<String> handleInvalidPersonIdException(PersonObtainService.InvalidPersonIdException e) {
        return ResponseEntity.badRequest().body(e.getMessage());
    }

    @ExceptionHandler(PersonObtainService.PersonNotFoundException.class)
    public ResponseEntity<String> handlePersonNotFoundException(PersonObtainService.PersonNotFoundException e) {
        return ResponseEntity.status(404).body(e.getMessage());

    }
}