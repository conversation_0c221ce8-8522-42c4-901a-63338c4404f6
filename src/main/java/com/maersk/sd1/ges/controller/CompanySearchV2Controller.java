package com.maersk.sd1.ges.controller;


import com.maersk.sd1.ges.dto.CompanySearchV2Input;
import com.maersk.sd1.ges.dto.CompanySearchV2Output;
import com.maersk.sd1.ges.service.CompanySearchV2Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@RestController
@RequestMapping("moduleadm/ModuleADM/module/adm/ADMEmpresaServiceImp")
public class CompanySearchV2Controller {

    private static final Logger logger = LogManager.getLogger(CompanySearchV2Controller.class);

    private final CompanySearchV2Service companySearchService;

    @Autowired
    public CompanySearchV2Controller(CompanySearchV2Service companySearchService) {
        this.companySearchService = companySearchService;
    }

    @PostMapping("/gescompanySearchv2")
    public ResponseEntity<CompanySearchV2Output> search(@RequestBody @Valid CompanySearchV2Input.Root request) {
        try {
            CompanySearchV2Input.Input input = request.getPrefix().getInput();
            if (input.getBusinessUnitId() == null) {
                throw new IllegalArgumentException("business_unit_id cannot be null");
            }
            CompanySearchV2Output response = companySearchService.searchCompanies(input);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error in searchCompanies", e);
            // Return an empty output or a structured error as needed.
            CompanySearchV2Output output = new CompanySearchV2Output();
            return ResponseEntity.internalServerError().body(output);
        }
    }
}
