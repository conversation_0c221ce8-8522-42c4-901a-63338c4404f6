package com.maersk.sd1.ges.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class AzureStorageConfigGetInput {

    @Data
    public static class Input {
        @JsonProperty("azure_storage_config_id")
        @NotNull
        private Integer azureStorageConfigId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private Prefix prefix;
    }

    private AzureStorageConfigGetInput() {
        // Private constructor to hide the implicit public one
    }
}
