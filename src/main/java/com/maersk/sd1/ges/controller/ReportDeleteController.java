package com.maersk.sd1.ges.controller;

import com.maersk.sd1.ges.dto.ReportDeleteInput;
import com.maersk.sd1.ges.dto.ReportDeleteOutput;
import com.maersk.sd1.ges.service.ReportDeleteService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMReporteServiceImp")
public class ReportDeleteController {

    private static final Logger logger = LogManager.getLogger(ReportDeleteController.class);

    private final ReportDeleteService reportDeleteService;

    @Autowired
    public ReportDeleteController(ReportDeleteService reportDeleteService) {
        this.reportDeleteService = reportDeleteService;
    }

    @PostMapping("/gesreporteEliminar")
    public ResponseEntity<ResponseController<ReportDeleteOutput>> deleteReport(@RequestBody @Valid ReportDeleteInput.Root request) {
        try {
            Integer reportId = request.getPrefix().getInput().getReporteId();
            ReportDeleteOutput output = reportDeleteService.deleteReport(reportId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while deleting report.", e);
            ReportDeleteOutput output = new ReportDeleteOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}