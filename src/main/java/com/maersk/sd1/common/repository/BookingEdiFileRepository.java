package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.BookingEdiFile;
import com.maersk.sd1.sds.dto.BookingEdiFileProcessDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

public interface BookingEdiFileRepository extends JpaRepository<BookingEdiFile, Integer> {

    @Query("SELECT m.message FROM MessageLanguage m " +
            "WHERE UPPER(m.type) = UPPER(:messageType) " +
            "AND m.code = :messageCode " +
            "AND m.language.id = :languageId " +
            "AND m.active = true")
    String fetchTranslatedMessage(@Param("messageType") String messageType,
                                @Param("messageCode") Integer messageCode,
                                @Param("languageId") Integer languageId);

    @Query("SELECT SUM(bd.attendedQuantity) FROM BookingDetail bd " +
            "WHERE bd.booking.id = :bookingId AND bd.active = true")
    Integer findTotalAssign(@Param("bookingId") Integer bookingId);

    @Query("SELECT 1 FROM Booking b " +
            "WHERE b.vesselProgrammingDetail.id = :vesselProgrammingDetailId " +
            "AND UPPER(b.bookingNumber) = UPPER(:bookingNumber) " +
            "AND b.active = true")
    Optional<Integer> checkDuplicateBooking(@Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId,
                                               @Param("bookingNumber") String bookingNumber);

    @Query("SELECT a.id FROM CargoDocument a " +
            "WHERE a.cargoDocument = :bookingNumber " +
            "AND a.vesselProgrammingDetail.id = :vesselProgrammingDetailId " +
            "AND a.active = true")
    Integer findDocumentCargoId(@Param("bookingNumber") String bookingNumber,
                               @Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId);

    @Modifying
    @Query("UPDATE Booking b " +
            "SET b.bookingNumber = :bookingNumber, " +
            "    b.bookingIssueDate = :bookingIssueDate, " +
            "    b.vesselProgrammingDetail.id = :vesselProgrammingDetailId, " +
            "    b.loadingPort.id = :loadingPortId, " +
            "    b.dischargePort.id = :dischargePortId, " +
            "    b.destinationPort.id = :destinationPortId, " +
            "    b.shippingLine.id = :shippingLineId, " +
            "    b.clientCompany.id = :clientCompanyId, " +
            "    b.shipperCompany.id = :shipperCompanyId, " +
            "    b.emptyDepot.id = :emptyDepotId, " +
            "    b.fullDepot.id = :fullDepotId, " +
            "    b.product.id = :productId, " +
            "    b.temperatureC = :temperatureC, " +
            "    b.imo.id = :imoId, " +
            "    b.commodity = :commodity, " +
            "    b.modificationUser.id = :modificationUserId, " +
            "    b.subBusinessUnit.id = :subBusinessUnitId, " +
            "    b.modificationDate = CURRENT_TIMESTAMP, " +
            "    b.traceBooking = 'upd-booking', " +
            "    b.catMoveType.id = :moveTypeId, " +
            "    b.maerskDepotWithSd1 = :maerskDepotWithSd1, " +
            "    b.originDestinationDepot.id = :originDestinationDepotId " +
            "WHERE b.id = :bookingId")
    void updateBooking(@Param("bookingId") Integer bookingId,
                              @Param("bookingNumber") String bookingNumber,
                              @Param("bookingIssueDate") String bookingIssueDate,
                              @Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId,
                              @Param("loadingPortId") Integer loadingPortId,
                              @Param("dischargePortId") Integer dischargePortId,
                              @Param("destinationPortId") Integer destinationPortId,
                              @Param("shippingLineId") Integer shippingLineId,
                              @Param("clientCompanyId") Integer clientCompanyId,
                              @Param("shipperCompanyId") Integer shipperCompanyId,
                              @Param("emptyDepotId") Integer emptyDepotId,
                              @Param("fullDepotId") Integer fullDepotId,
                              @Param("productId") Integer productId,
                              @Param("temperatureC") String temperatureC,
                              @Param("imoId") Integer imoId,
                              @Param("commodity") String commodity,
                              @Param("modificationUserId") Integer modificationUserId,
                              @Param("subBusinessUnitId") Integer subBusinessUnitId,
                              @Param("moveTypeId") Integer moveTypeId,
                              @Param("maerskDepotWithSd1") Boolean maerskDepotWithSd1,
                              @Param("originDestinationDepotId") Integer originDestinationDepotId);

    @Modifying
    @Query("UPDATE CargoDocument cd SET " +
            "cd.vesselProgrammingDetail.id = :vesselProgrammingDetailId, " +
            "cd.cargoDocument = :bookingNumber, " +
            "cd.originPort.id = :loadingPortId, " +
            "cd.loadingPort.id = :loadingPortId, " +
            "cd.dischargePort.id = :dischargePortId, " +
            "cd.destinationPort.id = :destinationPortId, " +
            "cd.shippingLine.id = :shippingLineId, " +
            "cd.depot.id = :fullDepotId, " +
            "cd.shipperCompany.id = :shipperCompanyId, " +
            "cd.consigneeCompany.id = :clientCompanyId, " +
            "cd.shipperDetail = (SELECT c.legalName FROM Company c WHERE c.id = :empresaEmbarcadorId), " +
            "cd.consigneeDetail = (SELECT c.legalName FROM Company c WHERE c.id = :empresaClienteId), " +
            "cd.emptyDepot.id = :emptyDepotId, " +
            "cd.modificationUser.id = :modificationUserId, " +
            "cd.modificationDate = CURRENT_TIMESTAMP, " +
            "cd.traceCargoDocument = 'upd-booking', " +
            "cd.catMoveType.id = :moveTypeId, " +
            "cd.maerskDepotWithSd1 = :maerskDepotWithSd1, " +
            "cd.originDestinationDepot.id = :originDestinationDepotId " +
            "WHERE cd.id = :cargoDocumentId")
    void updateCargoDocument(@Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId,
                                    @Param("bookingNumber") String bookingNumber,
                                    @Param("loadingPortId") Integer loadingPortId,
                                    @Param("dischargePortId") Integer dischargePortId,
                                    @Param("destinationPortId") Integer destinationPortId,
                                    @Param("shippingLineId") Integer shippingLineId,
                                    @Param("fullDepotId") Integer fullDepotId,
                                    @Param("shipperCompanyId") Integer shipperCompanyId,
                                    @Param("clientCompanyId") Integer clientCompanyId,
                                    @Param("emptyDepotId") Integer emptyDepotId,
                                    @Param("modificationUserId") Integer modificationUserId,
                                    @Param("moveTypeId") Integer moveTypeId,
                                    @Param("maerskDepotWithSd1") Boolean maerskDepotWithSd1,
                                    @Param("originDestinationDepotId") Integer originDestinationDepotId,
                                    @Param("cargoDocumentId") Integer cargoDocumentId);

    @Modifying
    @Query("UPDATE CargoDocumentDetail cdd " +
            "SET cdd.product.id = :productId, " +
            "    cdd.isDangerousCargo = :isDangerousCargo, " +
            "    cdd.commodity = :commodity, " +
            "    cdd.modificationUser.id = :modificationUserId, " +
            "    cdd.modificationDate = CURRENT_TIMESTAMP, " +
            "    cdd.traceCargoDocumentDetail = 'upd-booking' " +
            "WHERE cdd.cargoDocument.id = :cargoDocumentId")
    void updateDocumentCargoDetail(@Param("productId") Integer productId,
                                   @Param("isDangerousCargo") Boolean isDangerousCargo,
                                   @Param("commodity") String commodity,
                                   @Param("modificationUserId") Integer modificationUserId,
                                   @Param("cargoDocumentId") Integer cargoDocumentId);

    @Modifying
    @Query("UPDATE BookingEdiFile bef SET bef.bkEdiOriginContent = :coparnFileContent WHERE bef.id = :bookingEdiFileId")
    void updateFileOriginalContent(Integer bookingEdiFileId, String coparnFileContent);

    @Query(value = "SELECT B FROM BookingEdiFile B WHERE B.bookingEdi.id = : id")
    Optional<BookingEdiFile> findByBookingEdiId(@Param("id") Integer id);

    @Query("SELECT new com.maersk.sd1.sds.dto.BookingEdiFileProcessDTO(" +
                    "a.azureUrl, a.bkEdiContent, a.bkEdiJson , b.originalBkEdiFileName) " +
                    "FROM BookingEdiFile a " +
                    "INNER JOIN BookingEdi b ON a.id = b.id " +
                    "WHERE b.id = :ediCoparnId")
    Optional<BookingEdiFileProcessDTO> findCoparnDataByEdiCoparnId(@Param("ediCoparnId") Integer ediCoparnId);
}
