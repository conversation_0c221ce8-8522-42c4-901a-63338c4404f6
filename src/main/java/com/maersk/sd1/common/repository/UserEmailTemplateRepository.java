package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.UserEmailTemplate;
import com.maersk.sd1.common.model.UserEmailTemplateId;
import com.maersk.sd1.seg.controller.dto.UserGetEditOutput;
import org.springframework.data.jpa.repository.JpaRepository;
import com.maersk.sd1.seg.dto.EmailTemplateOutputDTO;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface UserEmailTemplateRepository extends JpaRepository<UserEmailTemplate, UserEmailTemplateId> {

    @Modifying
    @Query("DELETE FROM UserEmailTemplate uet WHERE uet.user.id = :userId")
    void deleteByUserId(@Param("userId") Integer userId);

    @Query("SELECT new com.maersk.sd1.seg.controller.dto.UserGetEditOutput$UserEmailTemplateDTO(" +
            " uet.id.emailTemplateId, uet.status, uet.enabled ) " +
            "FROM UserEmailTemplate uet " +
            "WHERE uet.id.userId = :userId")
    List<UserGetEditOutput.UserEmailTemplateDTO> findUserEmailTemplatesByUserId(@Param("userId") Integer userId);

    @Modifying
    @Transactional
    @Query("DELETE FROM UserEmailTemplate uet WHERE uet.user.id = :userId")
    void deleteAllByUserId(@Param("userId") Integer userId);

    @Query("SELECT new com.maersk.sd1.seg.dto.EmailTemplateOutputDTO(" +
            "et.description, et.status) " +
            "FROM UserEmailTemplate uet " +
            "JOIN uet.emailTemplate et " +
            "WHERE uet.user.id = :userId")
    List<EmailTemplateOutputDTO> findEmailTemplatesByUserId(@Param("userId") Integer userId);
}