package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.VesselProgrammingDetail;
import com.maersk.sd1.sds.dto.ListBookingShipsOutputProjection;
import com.maersk.sd1.sds.dto.ListShipsDTO;
import com.maersk.sd1.sds.dto.VesselDetailDTO;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface VesselProgrammingDetailRepository extends JpaRepository<VesselProgrammingDetail, Integer> {

    @Query("SELECT new com.maersk.sd1.sds.dto.ListShipsDTO(vpd.id, ship.name, vp.voyage, " +
            "vpd.catOperation.id, " +
            "CONCAT(vpd.manifestYear, '-', vpd.manifestNumber), " +
            "COALESCE(FORMAT(vp.etaDate, 'dd/MM/yyyy'), ''), " +
            "COALESCE(FORMAT(vp.etdDate, 'dd/MM/yyyy'), '')) " +
            "FROM VesselProgrammingDetail vpd " +
            "JOIN vpd.vesselProgramming vp " +
            "JOIN vpd.catOperation operation " +
            "JOIN vp.vessel ship " +
            "WHERE vp.subBusinessUnit.id = :subBusinessUnitId " +
            "AND (:shipName IS NULL OR ship.name LIKE CONCAT(RTRIM(:shipName), '%')) " +
            "AND (:voyage IS NULL OR vp.voyage LIKE CONCAT(RTRIM(:voyage), '%')) " +
            "AND (:manifestYear IS NULL OR COALESCE(vpd.manifestYear, '') LIKE CONCAT(RTRIM(:manifestYear), '%')) " +
            "AND (:manifestNumber IS NULL OR COALESCE(vpd.manifestNumber, '') LIKE CONCAT(RTRIM(:manifestNumber), '%')) " +
            "AND operation.variable1 = 'I' " +
            "AND vpd.active = true " +
            "AND vp.active = true " +
            "ORDER BY vp.etaDate DESC, ship.name, vp.voyage, CONCAT(operation.description, ' - ', operation.longDescription)")
    List<ListShipsDTO> findShips(@Param("subBusinessUnitId") Integer subBusinessUnitId,
                                 @Param("shipName") String shipName,
                                 @Param("voyage") String voyage,
                                 @Param("manifestYear") String manifestYear,
                                 @Param("manifestNumber") String manifestNumber);
    @Query(value =  "SELECT " +
            "a.programacion_nave_detalle_id AS vesselProgrammingDetailId, " +
            "navex.nombre AS shipName, " +
            "b.viaje AS voyage, " +
            "sds.fn_CatalogoTraducidoDes(a.cat_operacion_id,:language_id) + ' - ' + " +
            "sds.fn_CatalogoTraducidoDesLarga(a.cat_operacion_id,:language_id) AS operationDescription, " +
            "CONCAT(a.manifiesto_ano,'-',a.manifiesto_numero) AS manifest, " +
            "ISNULL(FORMAT(b.fecha_eta, 'dd/MM/yyyy'), '') AS etaDate, " +
            "ISNULL(FORMAT(b.fecha_etd, 'dd/MM/yyyy'), '') AS etdDate " +
            "FROM sds.programacion_nave_detalle as a " +
            "INNER JOIN sds.programacion_nave as b ON a.programacion_nave_id = b.programacion_nave_id " +
            "INNER JOIN ges.catalogo as operacion ON a.cat_operacion_id = operacion.catalogo_id " +
            "INNER JOIN sds.nave as navex (nolock) ON b.nave_id = navex.nave_id " +
            "WHERE b.sub_unidad_negocio_id = :sub_business_unit_id " +
            "AND (:ship_name is NULL or navex.nombre LIKE rtrim(:ship_name)+'%') " +
            "AND (:voyage is NULL or b.viaje LIKE rtrim(:voyage)+'%') " +
            "AND operacion.variable_1 = 'E' " +
            "AND a.activo = 1 AND b.activo = 1 " +
            "ORDER by b.fecha_eta DESC, b.fecha_etd DESC, " +
            "navex.nombre, b.viaje, " +
            "operacion.descripcion + ' - ' + operacion.descricion_larga",
            nativeQuery = true)
    List<ListBookingShipsOutputProjection> findVesselProgrammingDetails(
            @Param("sub_business_unit_id") Integer subBusinessUnitId,
            @Param("language_id") Integer languageId,
            @Param("ship_name") String shipName,
            @Param("voyage") String voyage
    );

    @Query("SELECT new com.maersk.sd1.sds.dto.VesselDetailDTO(" +
            "    a.vesselProgramming.id, " +
            "    CONCAT(v.name, '/', b.voyage)" +
            ") " +
            "FROM VesselProgrammingDetail a " +
            "JOIN a.vesselProgramming b " +
            "JOIN b.vessel v " +
            "WHERE a.id = :programmingDetailId")
    VesselDetailDTO findVesselDetailByProgrammingDetailId(@Param("programmingDetailId") Integer programmingDetailId);

    @Query("SELECT CONCAT(vessel.name, '/', vp.voyage) FROM VesselProgrammingDetail vpd " +
            "JOIN VesselProgramming vp ON vpd.vesselProgramming.id = vp.id " +
            "JOIN Vessel vessel ON vp.vessel.id = vessel.id " +
            "WHERE vpd.id = :vesselProgrammingDetailId")
    Optional<String> findVesselDetail(@Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId);


    @Query("SELECT vpd FROM VesselProgrammingDetail vpd WHERE vpd.vesselProgramming.id = :programmingVesselId AND vpd.catOperation.id = :operationId AND  vpd.active = true")
    Optional<VesselProgrammingDetail> getProgrammingDetailByProgrammingVesselAndDepotOperation(Integer programmingVesselId, Integer operationId);

    @Query("SELECT COUNT(pnd.id ) " +
            "FROM VesselProgrammingDetail pnd " +
            "JOIN pnd.vesselProgramming pn " +
            "JOIN pn.vessel   n " +
            "WHERE pn.subBusinessUnit.id = :subUnidadNegocioId          AND pn.vessel.id = :viaje " +
            "AND pnd.catOperation.id = 43002 AND pnd.active = true AND pn.active = true AND n.id IN :naveIds ")
    Integer countByCriteria(@Param("subUnidadNegocioId") Integer subUnidadNegocioId,
                            @Param("viaje") String viaje,
                            @Param("naveIds") List<Integer> naveIds);

    @Query("SELECT pnd.id FROM VesselProgrammingDetail pnd " +
            "JOIN pnd.vesselProgramming pn JOIN pn.vessel n " +
            "WHERE pn.subBusinessUnit.id = :subUnidadNegocioId AND " +
            "pn.vessel.id = :viaje AND pnd.catOperation.id = 43002 " +
            "AND pnd.active = true AND pn.active = true AND n.id IN :naveIds            ")
    Integer findIdByCriteria(@Param("subUnidadNegocioId") Integer subUnidadNegocioId,
                             @Param("viaje") String viaje,
                             @Param("naveIds") List<Integer> naveIds);


    @Query("SELECT COUNT(a) FROM VesselProgrammingDetail a "
            + "INNER JOIN VesselProgramming b ON a.vesselProgramming.id = b.id "
            + "INNER JOIN Vessel c ON b.vessel.id = c.id "
            + "WHERE b.subBusinessUnit.id = :subUnidadNegocioId "
            + "AND b.voyage IN :viajes "
            + "AND a.catOperation.id = :catOperacionId "
            + "AND a.active = true AND b.active = true")
    Integer countProgramacionNaveDetalle(@Param("subUnidadNegocioId") Integer subUnidadNegocioId,
                                         @Param("viajes") List<String> viajes,
                                         @Param("catOperacionId") Integer catOperacionId);

    @Query("SELECT a.id FROM VesselProgrammingDetail a "
            + "INNER JOIN VesselProgramming b ON a.vesselProgramming.id = b.id "
            + "INNER JOIN Vessel c ON b.vessel.id = c.id "
            + "WHERE b.subBusinessUnit.id = :subUnidadNegocioId "
            + "AND b.voyage IN :viajes "
            + "AND a.catOperation.id = :catOperacionId "
            + "AND a.active = true AND b.active = true")
    Integer findProgramacionNaveDetalleId(@Param("subUnidadNegocioId") Integer subUnidadNegocioId,
                                          @Param("viajes") List<String> viajes,
                                          @Param("catOperacionId") Integer catOperacionId);

    @Query("SELECT pd FROM VesselProgrammingDetail pd WHERE pd.vesselProgramming.id = :programScheduleId " +
            "AND pd.catOperation.id = :operationCategoryId AND pd.active = :active")
    Optional<VesselProgrammingDetail> findByProgramScheduleIdAndOperationCategoryIdAndActive(@Param("programScheduleId") Integer programScheduleId,
                                                                                             @Param("operationCategoryId") Integer operationCategoryId,
                                                                                             @Param("active") Integer active);

    List<VesselProgrammingDetail> findByVesselProgrammingIdAndActiveIsTrueOrderByCatOperationIdAsc(Integer vesselProgrammingId);

    @Query("SELECT DISTINCT d.vesselProgramming.businessUnit.id FROM VesselProgrammingDetail d WHERE d.vesselProgramming.id = :vesselProgrammingId AND d.active = true")
    List<Long> findBusinessUnitIdByVesselProgrammingId(@Param("vesselProgrammingId") Integer vesselProgrammingId);

    @Query("SELECT COUNT(d.id) FROM VesselProgrammingDetail d " +
            "WHERE d.vesselProgramming.businessUnit.id = :unidadNegocioId " +
            "  AND d.catOperation.variable1 = 'E' " +
            "  AND d.active = true " +
            "  AND d.vesselProgramming.active = true")
    Long countExportDetailsByBusinessUnitId(@Param("unidadNegocioId") Integer unidadNegocioId);

    @Query("SELECT d.id, CONCAT(d.vesselProgramming.vessel.name, '/', d.vesselProgramming.voyage), " +
            "CONCAT(d.catOperation.description, ' - ', d.catOperation.longDescription) " +
            "FROM VesselProgrammingDetail d " +
            "WHERE d.vesselProgramming.businessUnit.id = :unidadNegocioId " +
            "  AND d.catOperation.variable1 = 'E' " +
            "  AND d.active = true " +
            "  AND d.vesselProgramming.active = true")
    List<Object[]> findExportDetailsByBusinessUnitId(@Param("unidadNegocioId") Integer unidadNegocioId);

    @Modifying
    @Query("UPDATE VesselProgrammingDetail v SET v.active=false, v.modificationDate=:modificationDate, v.modificationUser.id=:userId " +
            "WHERE v.id=:detailId AND v.active=true")
    int deactivateDetail(@Param("detailId") Integer detailId,
                         @Param("userId") Integer userId,
                         @Param("modificationDate") LocalDateTime modificationDate);

    Optional<VesselProgrammingDetail> findByIdAndActive(Integer id, Boolean active);

    @Query("SELECT vpd FROM VesselProgrammingDetail vpd " +
            "JOIN vpd.vesselProgramming vp " +
            "WHERE vp.subBusinessUnit.id = :subUnitId " +
            "AND vp.vessel.id = :vesselId " +
            "AND vp.voyage = :voyage " +
            "AND vpd.catOperation.id = :operationId " +
            "ORDER BY vpd.registrationDate DESC")
    List<VesselProgrammingDetail> findLatestByBusinessUnitVesselVoyageOperation(
            Integer subUnitId, Integer vesselId, String voyage, Integer operationId, Pageable pageable);

    @Modifying
    @Query("UPDATE VesselProgrammingDetail vpd " +
            "SET vpd.active = true, vpd.modificationUser.id = :modificationUserId, vpd.modificationDate = CURRENT_TIMESTAMP " +
            "WHERE vpd.id = :id AND vpd.active = false")
    void updateActiveStatus(Integer id, Integer modificationUserId);

    @Query("SELECT vpd.vesselProgramming.subBusinessUnit.id " +
            "FROM VesselProgrammingDetail vpd " +
            "WHERE vpd.id = :vesselProgrammingDetailId AND vpd.active = true")
    Integer findLocalSubBusinessUnitId(@Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId);

}

