package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Block;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.model.Yard;
import com.maersk.sd1.sdy.dto.BlockStackingInfo;
import com.maersk.sd1.sdy.dto.VirtualBlockConfigDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface BlockRepository extends JpaRepository<Block, Integer>, JpaSpecificationExecutor<Block> {

    @Modifying
    @Query("UPDATE Block b SET b.active = false, b.modificationUser = :user, b.modificationDate = :date WHERE b.id = :blockId")
    void deactivateBlock(@Param("blockId") Integer blockId,
                         @Param("user") User user,
                         @Param("date") LocalDateTime modificationDate);

    Optional<Block> findById(Integer blockId);

    List<Block> findByYardIdAndCatBlockTypeIdNotAndActiveTrue(Integer yardId, Integer catBlockTypeIdNot);

    @Query("SELECT b.levels FROM Block b WHERE b.id = :id")
    Integer findLevelsById(@Param("id") Integer id);

    List<Block> findByYardAndActive(Yard yard, Boolean active);

    @Query("select b.catBlockType.id from Block b where b.code = :blockCode and b.yard.id = :yardId")
    Integer findBlockByCodeAndYard(@Param("blockCode") String blockCode,
                                   @Param("yardId") Integer yardId);

    List<Block> findByIdIn(List<Integer> blockIds);

    @Query("SELECT new com.maersk.sd1.sdy.dto.BlockStackingInfo(" +
            "b.id, b.blockDirection, b.stackingDirection) " +
            "FROM Block b " +
            "WHERE b.id IN :blockIds")
    List<BlockStackingInfo> findBlockStackingInfoList(@Param("blockIds") List<Integer> blockIds);

    @Query("SELECT new com.maersk.sd1.sdy.dto.BlockStackingInfo(" +
            "b.id, b.blockDirection, b.stackingDirection) " +
            "FROM Block b " +
            "WHERE b.id = :blockId")
    BlockStackingInfo findBlockStackingInfo(@Param("blockId") Integer blockId);

    @Query("SELECT b FROM Block b WHERE b.yard.id = :yardId AND b.code = :blockCode AND b.active = true")
    Optional<Block> findByYardIdAndCodeAndActive(@Param("yardId") Integer yardId,
                                                 @Param("blockCode") String blockCode);

    @Query(value = """
            SELECT TOP 1
            c.fila as row_label,
                   c.columna as column_label,
                   n.indice as level_index
            FROM sdy.bloque b
            JOIN sdy.celda c ON c.bloque_id = b.bloque_id
            JOIN sdy.nivel n ON n.celda_id = c.celda_id
            WHERE b.cat_bloque_id = :virtualBlockCatId
            AND b.patio_id = :yardId
            AND b.codigo = :blockCode
            AND b.activo = 1
            """, nativeQuery = true)
    Map<String, Object> findOntruckBlockInfo(
            @Param("virtualBlockCatId") Integer virtualBlockCatId,
            @Param("yardId") Integer yardId,
            @Param("blockCode") String blockCode
    );

    @Query("SELECT new com.maersk.sd1.sdy.dto.VirtualBlockConfigDTO(" +
           "b.id, c.id, l.id) " +
           "FROM Block b " +
           "JOIN Cell c ON c.block.id = b.id " +
           "JOIN Level l ON l.cell.id = c.id " +
           "WHERE b.yard.id = :yardId " +
           "AND b.code = 'OnTruck' " +
           "AND b.catBlockType.id = :catVirtualBlockId")
    Optional<VirtualBlockConfigDTO> findOnTruckVirtualBlockConfig(
        @Param("yardId") Integer yardId,
        @Param("catVirtualBlockId") Integer catVirtualBlockId);

    @Query("SELECT new com.maersk.sd1.sdy.dto.VirtualBlockConfigDTO(" +
           "b.id, c.id, l.id) " +
           "FROM Block b " +
           "JOIN Cell c ON c.block.id = b.id " +
           "JOIN Level l ON l.cell.id = c.id " +
           "WHERE b.yard.id = :yardId " +
           "AND b.code = 'Out' " +
           "AND b.catBlockType.id = :catVirtualBlockId")
    Optional<VirtualBlockConfigDTO> findOutVirtualBlockConfig(
        @Param("yardId") Integer yardId,
        @Param("catVirtualBlockId") Integer catVirtualBlockId);
}