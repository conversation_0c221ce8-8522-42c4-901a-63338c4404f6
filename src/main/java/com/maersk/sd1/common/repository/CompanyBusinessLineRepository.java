package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.CompanyBusinessLine;
import com.maersk.sd1.common.model.CompanyTurnBusinessId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface CompanyBusinessLineRepository extends JpaRepository<CompanyBusinessLine, CompanyTurnBusinessId> {

    @Modifying
    @Query("DELETE FROM CompanyBusinessLine cbl WHERE cbl.company.id = :companyId")
    void deleteByCompanyId(@Param("companyId") Integer companyId);
}