package com.maersk.sd1.common.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.maersk.sd1.common.model.SystemRule;


import com.maersk.sd1.sds.dto.GetRuleGeneralJsonOutput;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;

public interface SystemRuleRepository extends JpaRepository<SystemRule, Integer>{

    @Query("select s from SystemRule s where s.alias = :alias and s.active = true")
    SystemRule findByAliasAndActiveTrue(@Param("alias") String alias);

    @Query("SELECT r.rule FROM SystemRule r " +
            "WHERE r.alias = :ruleSystemAlias " +
            "AND r.active = true")
    Optional<String> findRuleFromSystemRuleByAlias(@Param("ruleSystemAlias") String ruleSystemAlias);

    @Query(value = "SELECT sr FROM SystemRule sr WHERE sr.alias = :alias AND sr.active = :active")
    List<SystemRule> findSystemRuleByAliasAndActive(@Param("alias") String alias, @Param("active") Boolean active);

    @Procedure(name = "SystemRule.rule_general_get_out")
    Map<String, Object> ruleGeneralGetOut(
            @Param("sub_business_unit_local_alias") String subBusinessUnitLocalAlias,
            @Param("system_rule_id") String systemRuleId,
            @Param("type_rule") String typeRule
    );

    @Query("SELECT new com.maersk.sd1.sds.dto.GetRuleGeneralJsonOutput(sr.alias, sr.rule) FROM SystemRule sr WHERE sr.alias = :systemRuleId")
    GetRuleGeneralJsonOutput findRuleByAlias(@Param("systemRuleId") String systemRuleId);


    @Query("SELECT s.rule FROM SystemRule s WHERE s.alias = :id AND s.active = true")
    String findRuleByIdAndActiveTrue(@Param("id") String id);

    @Query("SELECT s.rule FROM SystemRule s WHERE s.alias = :id AND s.businessUnit.id = :businessUnitId AND s.active = true")
    String findRuleByIdAndBusinessUnitIdAndActiveTrue(@Param("id") String id, @Param("businessUnitId") Integer businessUnitId);

    @Query(value = "SELECT RUOJ.url FROM ges.regla_sistema RSI (NOLOCK) " +
            "CROSS APPLY OPENJSON(RSI.regla) " +
            "WITH (template_name VARCHAR(100) '$.template_name', " +
            "url VARCHAR(300) '$.url', " +
            "language VARCHAR(50) '$.language') AS RUOJ " +
            "WHERE id = 'sd1_template_urls' AND " +
            "RUOJ.template_name = :templateName AND " +
            "RUOJ.language = (SELECT TOP 1 codigo FROM seg.idioma " +
            "WHERE idioma_id = :languageId)",
            nativeQuery = true)
    String findTemplateUrl(@Param("templateName") String templateName,
                           @Param("languageId") int languageId);

    @Query("SELECT sr FROM SystemRule sr WHERE sr.businessUnit.id = :businessUnitId AND sr.alias = :alias AND (sr.subBusinessUnit IS NULL OR sr.subBusinessUnit.id = :subBusinessUnitId)")
    Optional<SystemRule> findByAliasAndBusinessUnitId(String alias, Integer businessUnitId, Integer subBusinessUnitId);

    @Procedure(name = "SystemRule.gateinGeneralEquipmentAppointmentValidate")
    Object[][] gateinGeneralEquipmentAppointmentValidate(@Param("sub_business_unit_local_id") Integer subBusinessUnitLocalId, @Param("equipment_number") String equipmentNumber);

    @Modifying
    @Query("UPDATE SystemRule sr " +
            "SET sr.active = false, " +
            "    sr.modificationDate = :modificationDate, " +
            "    sr.modificationUser.id = :modificationUserId " +
            "WHERE sr.id = :ruleId")
    void deactivateSystemRule(@Param("ruleId") Integer ruleId,
                              @Param("modificationUserId") Integer modificationUserId,
                              @Param("modificationDate") LocalDateTime modificationDate);

    SystemRule findByAlias(String alias);

    @Query("""
        SELECT sr FROM SystemRule sr
        WHERE (:systemRuleId IS NULL OR CAST(sr.id AS string) LIKE %:systemRuleId%)
        AND (:alias IS NULL OR sr.alias LIKE %:alias%)
        AND (:systemId IS NULL OR CAST(sr.system.id AS string) LIKE %:systemId%)
        AND (:businessUnitId IS NULL OR CAST(sr.businessUnit.id AS string) LIKE %:businessUnitId%)
        AND (:subBusinessUnitId IS NULL OR CAST(sr.subBusinessUnit.id AS string) LIKE %:subBusinessUnitId%)
        AND (:description IS NULL OR sr.description LIKE %:description%)
        AND (:rule IS NULL OR sr.rule LIKE %:rule%)
        AND (:active IS NULL OR sr.active = :active)
        AND (:registrationDateMin IS NULL OR sr.registrationDate >= :registrationDateMin)
        AND (:registrationDateMax IS NULL OR sr.registrationDate < :registrationDateMax)
        AND (:modificationDateMin IS NULL OR sr.modificationDate >= :modificationDateMin)
        AND (:modificationDateMax IS NULL OR sr.modificationDate < :modificationDateMax)
    """)
    Page<SystemRule> findByFilters(
            @Param("systemRuleId") String systemRuleId,
            @Param("alias") String alias,
            @Param("systemId") String systemId,
            @Param("businessUnitId") String businessUnitId,
            @Param("subBusinessUnitId") String subBusinessUnitId,
            @Param("description") String description,
            @Param("rule") String rule,
            @Param("active") Boolean active,
            @Param("registrationDateMin") LocalDateTime registrationDateMin,
            @Param("registrationDateMax") LocalDateTime registrationDateMax,
            @Param("modificationDateMin") LocalDateTime modificationDateMin,
            @Param("modificationDateMax") LocalDateTime modificationDateMax,
            Pageable pageable
    );
}