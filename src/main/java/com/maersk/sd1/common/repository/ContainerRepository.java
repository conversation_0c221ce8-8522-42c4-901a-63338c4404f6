package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Container;
import com.maersk.sd1.sdy.dto.ContainerPlanningInfoProjection;
import jakarta.persistence.Tuple;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Repository
public interface ContainerRepository extends JpaRepository<Container, Integer> {

    Container findByContainerNumber(String containerNumber);

    @Query("SELECT c.id FROM Container c WHERE LOWER(c.containerNumber) = LOWER(:containerNumber)")
    Integer findIdByContainerNumber(@Param("containerNumber") String containerNumber);

    @Query(value = """
            SELECT cnt.containerNumber, cnt.id FROM Container cnt WHERE cnt.containerNumber IN :containerNumbers
            """)
    List<Object[]> findIdsByNumbers(@Param("containerNumbers") List<String> containerNumbers);

    @Query("SELECT c.id FROM Container c WHERE UPPER(c.containerNumber) = UPPER(:containerNumber)")
    Integer findContenedorIdByNumeroContenedor(@Param("containerNumber") String containerNumber);

    @Query("SELECT c.id FROM Container c WHERE UPPER(c.containerNumber) = UPPER(:containerNumber)")
    Integer findEquipmentNotApplicableId(@Param("containerNumber") String containerNumber);



    @Query("SELECT c.containerNumber FROM Container c WHERE c.id = :id")
    String findContainerNumberById(@Param("id") Integer id);

    @Query("SELECT c FROM Container c WHERE UPPER(c.containerNumber) = UPPER(:containerNumber) AND c.active = true")
    Optional<Container> findByContainerNumberIgnoreCaseAndActiveTrue(@Param("containerNumber") String containerNumber);

    @Query(value = "SELECT sds.fn_obtener_tipo_reefer_default(:equipmentNumber)", nativeQuery = true)
    Integer fnGetDefaultReeferType(@Param("equipmentNumber") String equipmentNumber);

    @Query("SELECT sdg.fn_checkContainerGateoutAvailability(:subBusinessUnitId, :localSubBusinessUnitId, :catContainerSizeId, :catContainerTypeId, :catContainerGradeId, :bookingId, :requiredQuantity, :yardIntegration)")
    String fnCheckContainerGateoutAvailability(
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("localSubBusinessUnitId") Integer localSubBusinessUnitId,
            @Param("catContainerSizeId") Integer catContainerSizeId,
            @Param("catContainerTypeId") Integer catContainerTypeId,
            @Param("catContainerGradeId") Integer catContainerGradeId,
            @Param("bookingId") Integer bookingId,
            @Param("requiredQuantity") Integer requiredQuantity,
            @Param("yardIntegration") Short yardIntegration
    );

    @Query("SELECT c FROM Container c "
            + "WHERE c.active = true "
            + "  AND c.containerNumber IN :numbers "
            + "  AND c.shippingLine.id IN :shippingLineIds")
    List<Container> findByContainerNumberInAndShippingLineIds(@Param("numbers") List<String> containerNumbers,
                                                              @Param("shippingLineIds") List<Integer> shippingLineIds);


    @Query("SELECT c.id FROM Container c WHERE c.containerNumber = 'NO-CNT'")
    Integer findContainerDummyId();

    Optional<Container> findByContainerNumberIgnoreCase(String containerNumber);

    @Query("SELECT c FROM Container c WHERE LOWER(c.containerNumber) = LOWER(:containerNumber) AND c.id <> :excludeId")
    Container findByContainerNumberIgnoreCaseAndIdNot(String containerNumber, Integer excludeId);

    @Query("SELECT c FROM Container c " +
            " LEFT JOIN FETCH c.shippingLine sl " +
            " LEFT JOIN FETCH c.isoCode iso " +
            " LEFT JOIN FETCH c.catFamily cf " +
            " LEFT JOIN FETCH c.catSize cs " +
            " LEFT JOIN FETCH c.catContainerType cct " +
            " LEFT JOIN FETCH c.catGrade cg " +
            " LEFT JOIN FETCH c.catReeferType cr " +
            " LEFT JOIN FETCH c.catEngineBrand ce " +
            " WHERE c.id = :id")
    Optional<Container> findContainerWithDetailsById(@Param("id") Integer id);

    @Query("""
            SELECT c FROM Container c
            LEFT JOIN c.shippingLine sl
            LEFT JOIN c.catSize sz
            LEFT JOIN c.catContainerType ct
            WHERE
              (:containerId IS NULL OR c.id = :containerId)
              AND (:containerNumber IS NULL OR c.containerNumber LIKE CONCAT('%', :containerNumber, '%'))
              AND (:catSizeId IS NULL OR sz.id = :catSizeId)
              AND (:catContainerTypeId IS NULL OR ct.id = :catContainerTypeId)
              AND (:shippingLineName IS NULL OR sl.name LIKE CONCAT('%', :shippingLineName, '%'))
              AND (:active IS NULL OR c.active = :active)
           """
    )
    Page<Container> searchContainers(
            @Param("containerId") Integer containerId,
            @Param("containerNumber") String containerNumber,
            @Param("catSizeId") Integer catSizeId,
            @Param("catContainerTypeId") Integer catContainerTypeId,
            @Param("shippingLineName") String shippingLineName,
            @Param("active") Boolean active,
            Pageable pageable
    );

    @Modifying
    @Query("UPDATE Container c SET c.active = false, c.modificationUser.id = :usuarioModificacionId, c.modificationDate = :modDate, c.traceContainer = :traceValue WHERE c.id = :contenedorId")
    void softDeleteContainer(@Param("contenedorId") Integer containerId,
                             @Param("usuarioModificacionId") Integer userModificationId,
                             @Param("modDate") LocalDateTime modDate,
                             @Param("traceValue") String traceValue);

    @Query("SELECT c FROM Container c WHERE c.containerNumber IN :containerNumbers")
    List<Container> findByContainerNumbers(@Param("containerNumbers") List<String> containerNumbers);

    @Query("SELECT c FROM Container c WHERE c.id = :containerId AND COALESCE(c.isoCode.id, 0) <> :isoCode")
    Optional<Container> findByContainerIdAndIsoCodeMismatch(@Param("containerId") Integer containerId, @Param("isoCode") Integer isoCode);


    @Query("SELECT c.id FROM Container c WHERE upper(c.containerNumber) = 'NOT APPLICA'")
    Long getEquipmentNotApplicableId();

    List<Container> findByContainerNumberContainingIgnoreCaseOrderByContainerNumberAsc(String containerNumber);

    @Query("SELECT new com.maersk.sd1.sdy.dto.ContainerPlanningInfoProjection(" +
            " cont.containerNumber, " +
            " cont.id, " +
            " catSize.code, " +
            " b.id, " +
            " b.code, " +
            " catBlockType.code, " +
            " c.id, " +
            " c.row, " +
            " c.column, " +
            " c.rowIndex, " +
            " c.columnIndex, " +
            " l.id, " +
            " l.index ) " +
            "FROM Container cont " +
            "LEFT JOIN ContainerLocation loc ON cont.id = loc.container.id " +
            "LEFT JOIN Block b ON b.id = loc.block.id " +
            "LEFT JOIN Cell c ON c.id = loc.cell.id " +
            "LEFT JOIN Level l ON l.id = loc.level.id " +
            "LEFT JOIN Yard y ON y.id = b.yard.id " +
            "LEFT JOIN Catalog catBlockType ON catBlockType.id = b.catBlockType.id " +
            "LEFT JOIN Catalog catSize ON catSize.id = cont.catSize.id " +
            "WHERE TRIM(cont.containerNumber) = TRIM(:numeroContenedor) " +
            "  AND TRIM(y.code) = TRIM(:patioCodigo) " +
            "ORDER BY c.rowIndex ASC")
    List<ContainerPlanningInfoProjection> findContainerPlanningInfo(@Param("numeroContenedor") String numeroContenedor, @Param("patioCodigo") String patioCodigo);

    @Query("select cat.code from Container cont join cont.catSize cat where cont.id = :containerId")
    Optional<String> findContainerSizeCode(@Param("containerId") Integer containerId);

    @Query(value = """
            SELECT
                RST.contenedor_id AS container_id,
                STRING_AGG(ges.fn_CatalogTranslationDesc(RDT.cat_motivo_restriccion_id, :languageId), '-') AS restriction_motives,
                RST.cat_empty_full_id AS cat_empty_full_id
            FROM sde.restriccion_contenedor RST WITH (NOLOCK)
            JOIN sde.restriccion_contenedor_detalle RDT WITH (NOLOCK)
                ON RDT.restriccion_cnt_id = RST.restriccion_cnt_id
            WHERE
                RST.sub_unidad_negocio_id = :subUnidadNegocioId
                AND RST.restriccion_liberada = 0
                AND RST.activo = 1
                AND RDT.activo = 1
            GROUP BY RST.contenedor_id, RST.cat_empty_full_id
            """, nativeQuery = true)
    List<Map<String, Object>> findAllRestrictions(
            @Param("subUnidadNegocioId") Integer subUnidadNegocioId,
            @Param("languageId") Integer languageId
    );

    @Query(value = """
            SELECT
                alg.activity_log_id,
                alg.activity_alias,
                alg.data_output
            FROM sds.activity_log alg
            WHERE alg.container_number = :containerNumber
            AND alg.eir_number = :eirNumber
            AND alg.activity_alias = :activityAlias
            AND alg.cat_status_id = :statusId
            AND alg.activity_log_reference_id IS NULL
            AND alg.active = 1
            """, nativeQuery = true)
    Map<String, Object> findActivityLogOutput(
            @Param("containerNumber") String containerNumber,
            @Param("eirNumber") Integer eirNumber,
            @Param("activityAlias") String activityAlias,
            @Param("statusId") Integer statusId);

    @Query(value = """
            SELECT cl.container.id as container_id,
                   cl.id as container_location_id,
                   cl.block.id as block_id,
                   cl.cell.rowIndex as row_index,
                   cl.cell.columnIndex as column_index,
                   cl.level.index as level_index
            FROM ContainerLocation cl
            WHERE cl.block.id IN :blockIds
            AND cl.cell.rowIndex IN (SELECT CAST(value as int) FROM function('STRING_SPLIT', :rowIndices, ','))
            AND cl.container IS NOT NULL
            AND cl.active = true
            """, nativeQuery = true)
    List<Map<String, Object>> findBlockingContainerLocations(
            @Param("blockIds") List<Integer> blockIds,
            @Param("rowIndices") String rowIndices
    );


    @Query(value = """
                SELECT
                    PND.programacion_nave_detalle_id AS programacionNaveDetalleId,
                    CNT.contenedor_id AS contenedorId,
                    CNT.numero_contenedor AS numeroContenedor,
                    CNT.fecha_fabricacion AS fechaFabricacion,
                    NULL AS catFamiliaDesc,
                    NULL AS catTamanoDesc,
                    NULL AS catTipoConDesc,
                    CNT.linea_naviera_id AS lineaNombre,
                    NULL AS catClaseDesc,
                    NULL AS catTipoReeferDesc,
                    NVE.nave AS nave,
                    CAT_ZNE.codigo AS codigoGrupo,
                    EAZ.fecha_inicio AS fechaGatein,
                    DATEDIFF(DAY, EAZ.fecha_inicio, SYSDATETIME()) AS diasPermanencia,
                    DATEPART(YEAR, CNT.fecha_fabricacion) AS anioFabricacion,
                    NULL AS procedencia,
                    EIR_GI.cat_procedencia_id AS procedenciaId,
                    NULL AS categoria,
                    DCG_GI.documento_carga AS numeroDocumentoIngreso,
                    DCG_GO.documento_carga AS numeroDocumentoSalida,
                    :yardCode AS patioCodigo,
                    NULL AS origenBloque,
                    NULL AS origenColumna,
                    NULL AS origenFila,
                    NULL AS origenNivel,
                    NULL AS cantidadRemovidos,
                    TCON.container_id AS preasignado,
                    IIF(TCRS.restriction_motives IS NULL, 0, 1) AS restriccion,
                    NULL AS catEmptyFullDesc,
                    TCRS.restriction_motives AS catRestMotivesDesc,
                    ISNULL(ges.fn_CatalogTranslationDescLong(sdg.fn_GetEquipmentConditionID(EIR_GI.eir_id, :isContainer, 'S', 'CUR'), :languageId), '') AS catStructureConditionDesc,
                    ISNULL(ges.fn_CatalogTranslationDescLong(sdg.fn_GetEquipmentConditionID(EIR_GI.eir_id, :isContainer, 'M', 'CUR'), :languageId), '') AS catMachineryConditionDesc,
                    CMP.razon_social AS customer,
                    sds.fn_CatalogoTraducidoDes(EMR.cat_estado_estimado_id, :languageId) AS emrCondition,
                    NULL AS destinoBloque,
                    NULL AS destinoColumna,
                    NULL AS destinoFila,
                    NULL AS destinoNivel,
                    NULL AS destinoInstruccionMovimiento,
                    NULL AS instruccionMovimientoEstado,
                    CAT_DCG.descripcion AS catStatusDesc,
                    NULL AS placaVehiculo,
                    NULL AS activityLogOutput
                FROM (
                    SELECT container_id, cat_empty_full_id, gatein_eir_id, gateout_eir_id
                    FROM OPENJSON(:eirContainersJson)
                    WITH (
                        container_id INT,
                        cat_empty_full_id NUMERIC(18, 0),
                        gatein_eir_id INT,
                        gateout_eir_id INT
                    )
                ) TCON
                INNER JOIN sds.contenedor CNT (NOLOCK) ON CNT.contenedor_id = TCON.container_id
                INNER JOIN sde.eir EIR_GI (NOLOCK) ON EIR_GI.eir_id = TCON.gatein_eir_id
                LEFT OUTER JOIN ges.empresa CMP (NOLOCK) ON CMP.empresa_id = EIR_GI.empresa_cliente_id
                INNER JOIN sds.programacion_nave_detalle PND (NOLOCK) ON PND.programacion_nave_detalle_id = EIR_GI.programacion_nave_detalle_id
                INNER JOIN sds.programacion_nave PNV (NOLOCK) ON PNV.programacion_nave_id = PND.programacion_nave_id
                INNER JOIN sds.nave NVE (NOLOCK) ON NVE.nave_id = PNV.nave_id
                 LEFT OUTER JOIN (
                                   SELECT
                                            gatein_eir_id,
                                            eir_zone_activity_id
                                        FROM OPENJSON(:latestZoneActivitiesJson) WITH (
                                            gatein_eir_id INT,
                                            eir_zone_activity_id INT
                                        )
                                ) TEAZ ON TEAZ.gatein_eir_id = TCON.gatein_eir_id
                LEFT OUTER JOIN sde.eir_actividad_zona EAZ (NOLOCK) ON EAZ.eir_actividad_zona_id = TEAZ.eir_zone_activity_id
                LEFT OUTER JOIN sde.eir_zona EZN (NOLOCK) ON EZN.eir_zona_id = EAZ.eir_zona_id
                LEFT OUTER JOIN ges.catalogo CAT_ZNE (NOLOCK) ON CAT_ZNE.catalogo_id = EZN.cat_zona_contenedor_id
                INNER JOIN sde.eir_documento_carga_detalle EDCD_GI ON EDCD_GI.eir_id = EIR_GI.eir_id
                INNER JOIN sds.documento_carga_detalle DCD_GI ON DCD_GI.documento_carga_detalle_id = EDCD_GI.documento_carga_detalle_id
                LEFT OUTER JOIN sds.documento_carga DCG_GI ON DCG_GI.documento_carga_id = DCD_GI.documento_carga_id
                LEFT OUTER JOIN sde.eir_documento_carga_detalle EDCD_GO ON EDCD_GO.eir_id = TCON.gateout_eir_id
                LEFT OUTER JOIN sds.documento_carga_detalle DCD_GO ON DCD_GO.documento_carga_detalle_id = EDCD_GO.documento_carga_detalle_id
                LEFT OUTER JOIN sds.documento_carga DCG_GO ON DCG_GO.documento_carga_id = DCD_GO.documento_carga_id
                LEFT OUTER JOIN (
                   SELECT
                            container_id,
                            cat_empty_full_id,
                            restriction_motives
                        FROM OPENJSON(:containerRestrictionsJson) WITH (
                            container_id INT,
                            cat_empty_full_id INT,
                            restriction_motives NVARCHAR(MAX)
                        )
                ) TCRS ON (TCRS.container_id = TCON.container_id AND TCRS.cat_empty_full_id = TCON.cat_empty_full_id)
                LEFT OUTER JOIN (
                    SELECT eir_id, emr_id
                    FROM OPENJSON(:emrConditionsJson) WITH (
                        eir_id INT,
                        emr_id INT
                    )
                ) TEMR ON TEMR.eir_id = EIR_GI.eir_id
                LEFT OUTER JOIN sde.estimado_emr EMR (NOLOCK) ON EMR.estimado_emr_id = TEMR.emr_id
                INNER JOIN ges.catalogo CAT_DCG (NOLOCK) ON CAT_DCG.catalogo_id = DCD_GI.cat_condicion_carga_id
                LEFT OUTER JOIN ges.catalogo AS cntgradex (NOLOCK) ON CNT.cat_clase_id = cntgradex.catalogo_id
                WHERE
                    (:numeroContendores IS NULL OR :numeroContendores = '[]' OR CNT.numero_contenedor IN (SELECT contenedor FROM OPENJSON(:numeroContendores) WITH (contenedor VARCHAR(20))))
                    AND CNT.carga_maxima >= COALESCE(:cargaMaxima, 0)
                    AND (:catCategoriaId IS NULL OR CNT.cat_categoria_id = :catCategoriaId)
                    AND CNT.cat_familia_id = IIF(:catFamiliaId IS NULL, CNT.cat_familia_id, :catFamiliaId)
                    AND CNT.cat_tamano_id = IIF(:catTamanoId IS NULL, CNT.cat_tamano_id, :catTamanoId)
                    AND CNT.cat_tipo_contenedor_id = IIF(:catTipoContenedorId IS NULL, CNT.cat_tipo_contenedor_id, :catTipoContenedorId)
                    AND CNT.linea_naviera_id = IIF(:lineaNavieraId IS NULL, CNT.linea_naviera_id, :lineaNavieraId)
                    AND (:catClaseId IS NULL OR CNT.cat_clase_id = :catClaseId)
                    AND (:catTipoReeferId IS NULL OR CNT.cat_tipo_reefer_id = :catTipoReeferId)
                    AND (:groupCode IS NULL OR CAT_ZNE.codigo LIKE TRIM(:groupCode))
                    AND ISNULL(DCG_GO.documento_carga, '') LIKE '%' + TRIM(ISNULL(:numeroDocumentoSalida, '')) + '%'
                    AND ISNULL(DCG_GI.documento_carga, '') LIKE '%' + TRIM(ISNULL(:numeroDocumentoIngreso, '')) + '%'
                    AND (:naveId IS NULL OR NVE.nave_id = :naveId)
                    AND (:owner IS NULL OR CMP.razon_social LIKE '%' + :owner + '%')
                    AND (:emrCondition IS NULL OR sds.fn_CatalogoTraducidoDes(EMR.cat_estado_estimado_id, :languageId) LIKE '%' + :emrCondition + '%')
            ORDER BY TCON.container_id
            OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY
            """, nativeQuery = true)
    List<Tuple> findContainersForPlanning(
            @Param("latestZoneActivitiesJson") String latestZoneActivitiesJson,
            @Param("eirContainersJson") String eirContainersJson,
            @Param("containerRestrictionsJson") String containerRestrictionsJson,
            @Param("emrConditionsJson") String emrConditionsJson,
            @Param("isContainer") Integer isContainer,
            @Param("yardCode") String yardCode,
            @Param("languageId") Integer languageId,
            @Param("numeroContendores") String numeroContendores,
            @Param("cargaMaxima") Integer cargaMaxima,
            @Param("catCategoriaId") Integer catCategoriaId,
            @Param("catFamiliaId") Integer catFamiliaId,
            @Param("catTamanoId") Integer catTamanoId,
            @Param("catTipoContenedorId") Integer catTipoContenedorId,
            @Param("lineaNavieraId") Integer lineaNavieraId,
            @Param("catClaseId") Integer catClaseId,
            @Param("catTipoReeferId") Integer catTipoReeferId,
            @Param("groupCode") String groupCode,
            @Param("numeroDocumentoSalida") String numeroDocumentoSalida,
            @Param("numeroDocumentoIngreso") String numeroDocumentoIngreso,
            @Param("naveId") Integer naveId,
            @Param("owner") String owner,
            @Param("emrCondition") String emrCondition,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit
    );

    @Query(value = """
                SELECT COUNT(*)
                FROM (
                SELECT container_id, cat_empty_full_id, gatein_eir_id, gateout_eir_id
                FROM OPENJSON(:eirContainersJson)
                WITH (
                    container_id INT,
                    cat_empty_full_id NUMERIC(18, 0),
                    gatein_eir_id INT,
                    gateout_eir_id INT
                )
            ) TCON
            INNER JOIN sds.contenedor CNT (NOLOCK) ON CNT.contenedor_id = TCON.container_id
            INNER JOIN sde.eir EIR_GI (NOLOCK) ON EIR_GI.eir_id = TCON.gatein_eir_id
            LEFT OUTER JOIN ges.empresa CMP (NOLOCK) ON CMP.empresa_id = EIR_GI.empresa_cliente_id
            INNER JOIN sds.programacion_nave_detalle PND (NOLOCK) ON PND.programacion_nave_detalle_id = EIR_GI.programacion_nave_detalle_id
            INNER JOIN sds.programacion_nave PNV (NOLOCK) ON PNV.programacion_nave_id = PND.programacion_nave_id
            INNER JOIN sds.nave NVE (NOLOCK) ON NVE.nave_id = PNV.nave_id
             LEFT OUTER JOIN (
                               SELECT
                                        gatein_eir_id,
                                        eir_zone_activity_id
                                    FROM OPENJSON(:latestZoneActivitiesJson) WITH (
                                        gatein_eir_id INT,
                                        eir_zone_activity_id INT
                                    )
                            ) TEAZ ON TEAZ.gatein_eir_id = TCON.gatein_eir_id
            LEFT OUTER JOIN sde.eir_actividad_zona EAZ (NOLOCK) ON EAZ.eir_actividad_zona_id = TEAZ.eir_zone_activity_id
            LEFT OUTER JOIN sde.eir_zona EZN (NOLOCK) ON EZN.eir_zona_id = EAZ.eir_zona_id
            LEFT OUTER JOIN ges.catalogo CAT_ZNE (NOLOCK) ON CAT_ZNE.catalogo_id = EZN.cat_zona_contenedor_id
            INNER JOIN sde.eir_documento_carga_detalle EDCD_GI ON EDCD_GI.eir_id = EIR_GI.eir_id
            INNER JOIN sds.documento_carga_detalle DCD_GI ON DCD_GI.documento_carga_detalle_id = EDCD_GI.documento_carga_detalle_id
            LEFT OUTER JOIN sds.documento_carga DCG_GI ON DCG_GI.documento_carga_id = DCD_GI.documento_carga_id
            LEFT OUTER JOIN sde.eir_documento_carga_detalle EDCD_GO ON EDCD_GO.eir_id = TCON.gateout_eir_id
            LEFT OUTER JOIN sds.documento_carga_detalle DCD_GO ON DCD_GO.documento_carga_detalle_id = EDCD_GO.documento_carga_detalle_id
            LEFT OUTER JOIN sds.documento_carga DCG_GO ON DCG_GO.documento_carga_id = DCD_GO.documento_carga_id
            LEFT OUTER JOIN (
               SELECT
                        container_id,
                        cat_empty_full_id,
                        restriction_motives
                    FROM OPENJSON(:containerRestrictionsJson) WITH (
                        container_id INT,
                        cat_empty_full_id INT,
                        restriction_motives NVARCHAR(MAX)
                    )
            ) TCRS ON (TCRS.container_id = TCON.container_id AND TCRS.cat_empty_full_id = TCON.cat_empty_full_id)
            LEFT OUTER JOIN (
                SELECT eir_id, emr_id
                FROM OPENJSON(:emrConditionsJson) WITH (
                    eir_id INT,
                    emr_id INT
                )
            ) TEMR ON TEMR.eir_id = EIR_GI.eir_id
            LEFT OUTER JOIN sde.estimado_emr EMR (NOLOCK) ON EMR.estimado_emr_id = TEMR.emr_id
            INNER JOIN ges.catalogo CAT_DCG (NOLOCK) ON CAT_DCG.catalogo_id = DCD_GI.cat_condicion_carga_id
            LEFT OUTER JOIN ges.catalogo AS cntgradex (NOLOCK) ON CNT.cat_clase_id = cntgradex.catalogo_id
            WHERE
                (:numeroContendores IS NULL OR :numeroContendores = '[]' OR CNT.numero_contenedor IN (SELECT contenedor FROM OPENJSON(:numeroContendores) WITH (contenedor VARCHAR(20))))
                AND CNT.carga_maxima >= COALESCE(:cargaMaxima, 0)
                AND (:catCategoriaId IS NULL OR CNT.cat_categoria_id = :catCategoriaId)
                AND CNT.cat_familia_id = IIF(:catFamiliaId IS NULL, CNT.cat_familia_id, :catFamiliaId)
                AND CNT.cat_tamano_id = IIF(:catTamanoId IS NULL, CNT.cat_tamano_id, :catTamanoId)
                AND CNT.cat_tipo_contenedor_id = IIF(:catTipoContenedorId IS NULL, CNT.cat_tipo_contenedor_id, :catTipoContenedorId)
                AND CNT.linea_naviera_id = IIF(:lineaNavieraId IS NULL, CNT.linea_naviera_id, :lineaNavieraId)
                AND (:catClaseId IS NULL OR CNT.cat_clase_id = :catClaseId)
                AND (:catTipoReeferId IS NULL OR CNT.cat_tipo_reefer_id = :catTipoReeferId)
                AND (:groupCode IS NULL OR CAT_ZNE.codigo LIKE TRIM(:groupCode))
                AND ISNULL(DCG_GO.documento_carga, '') LIKE '%' + TRIM(ISNULL(:numeroDocumentoSalida, '')) + '%'
                AND ISNULL(DCG_GI.documento_carga, '') LIKE '%' + TRIM(ISNULL(:numeroDocumentoIngreso, '')) + '%'
                AND (:naveId IS NULL OR NVE.nave_id = :naveId)
                AND (:owner IS NULL OR CMP.razon_social LIKE '%' + :owner + '%')
                AND (:emrCondition IS NULL OR sds.fn_CatalogoTraducidoDes(EMR.cat_estado_estimado_id, :languageId) LIKE '%' + :emrCondition + '%')
            """, nativeQuery = true)
    Long countContainersForPlanning(
            @Param("latestZoneActivitiesJson") String latestZoneActivitiesJson,
            @Param("eirContainersJson") String eirContainersJson,
            @Param("containerRestrictionsJson") String containerRestrictionsJson,
            @Param("emrConditionsJson") String emrConditionsJson,
            @Param("languageId") Integer languageId,
            @Param("numeroContendores") String numeroContendores,
            @Param("cargaMaxima") Integer cargaMaxima,
            @Param("catCategoriaId") Integer catCategoriaId,
            @Param("catFamiliaId") Integer catFamiliaId,
            @Param("catTamanoId") Integer catTamanoId,
            @Param("catTipoContenedorId") Integer catTipoContenedorId,
            @Param("lineaNavieraId") Integer lineaNavieraId,
            @Param("catClaseId") Integer catClaseId,
            @Param("catTipoReeferId") Integer catTipoReeferId,
            @Param("groupCode") String groupCode,
            @Param("numeroDocumentoSalida") String numeroDocumentoSalida,
            @Param("numeroDocumentoIngreso") String numeroDocumentoIngreso,
            @Param("naveId") Integer naveId,
            @Param("owner") String owner,
            @Param("emrCondition") String emrCondition
    );

    @Query(value = """
            SELECT
                CNT.contenedor_id AS containerId,
                CAT_FML.descripcion AS catFamiliaDesc,
                CAT_SZE.descripcion AS catTamanoDesc,
                CAT_TPE.descripcion AS catTipoConDesc,
                CAT_CLS.descripcion AS catClaseDesc,
                CAT_TRF.descripcion AS catTipoReeferDesc,
                CAT_CTG.codigo AS categoria,
                CAT_EFT.descripcion AS catEmptyFullDesc,
                CASE
                    WHEN TCON.cat_empty_full_id <> :isFullId THEN CAT_PRC.descripcion
                    WHEN CAT_OPT_FLL.variable_1 = 'I' THEN :isGateintypeFullImportDescripcion
                    WHEN CAT_OPT_FLL.variable_1 = 'E' THEN :isGateintypeFullExportDescripcion
                    WHEN CAT_OPT_FLL.variable_1 = 'S' THEN :isGateintypeFullStorageDescripcion
                    ELSE NULL
                END AS procedencia,
                CASE
                    WHEN TCON.cat_empty_full_id <> :isFullId THEN EIR_GI.cat_procedencia_id
                    WHEN CAT_OPT_FLL.variable_1 = 'I' THEN :isGateintypeFullImportId
                    WHEN CAT_OPT_FLL.variable_1 = 'E' THEN :isGateintypeFullExportId
                    WHEN CAT_OPT_FLL.variable_1 = 'S' THEN :isGateintypeFullStorageId
                    ELSE NULL
                END AS procedenciaId
            FROM
                (
                    SELECT container_id, cat_empty_full_id, gatein_eir_id, gateout_eir_id
                    FROM OPENJSON(:eirContainersJson)
                    WITH (
                        container_id INT,
                        cat_empty_full_id NUMERIC(18, 0),
                        gatein_eir_id INT,
                        gateout_eir_id INT
                    )
                ) TCON
                INNER JOIN sds.contenedor CNT (NOLOCK) ON CNT.contenedor_id = TCON.container_id
                INNER JOIN sde.eir EIR_GI (NOLOCK) ON EIR_GI.eir_id = TCON.gatein_eir_id
                LEFT OUTER JOIN ges.catalogo CAT_FML (NOLOCK) ON CAT_FML.catalogo_id = CNT.cat_familia_id
                LEFT OUTER JOIN ges.catalogo CAT_SZE (NOLOCK) ON CAT_SZE.catalogo_id = CNT.cat_tamano_id
                LEFT OUTER JOIN ges.catalogo CAT_TPE (NOLOCK) ON CAT_TPE.catalogo_id = CNT.cat_tipo_contenedor_id
                LEFT OUTER JOIN ges.catalogo CAT_CLS (NOLOCK) ON CAT_CLS.catalogo_id = CNT.cat_clase_id
                LEFT OUTER JOIN ges.catalogo CAT_TRF (NOLOCK) ON CAT_TRF.catalogo_id = CNT.cat_tipo_reefer_id
                LEFT OUTER JOIN ges.catalogo CAT_CTG (NOLOCK) ON CAT_CTG.catalogo_id = CNT.cat_categoria_id
                LEFT OUTER JOIN ges.catalogo CAT_EFT (NOLOCK) ON CAT_EFT.catalogo_id = EIR_GI.cat_empty_full_id
                LEFT OUTER JOIN ges.catalogo CAT_PRC (NOLOCK) ON CAT_PRC.catalogo_id = EIR_GI.cat_procedencia_id
                LEFT OUTER JOIN sde.eir_documento_carga_detalle EDCD (NOLOCK) ON EDCD.eir_id = EIR_GI.eir_id
                LEFT OUTER JOIN sds.documento_carga_detalle DCD (NOLOCK) ON DCD.documento_carga_detalle_id = EDCD.documento_carga_detalle_id
                LEFT OUTER JOIN sds.documento_carga DCG (NOLOCK) ON DCG.documento_carga_id = DCD.documento_carga_id
                LEFT OUTER JOIN sds.programacion_nave_detalle PND (NOLOCK) ON PND.programacion_nave_detalle_id = DCG.programacion_nave_detalle_id
                LEFT OUTER JOIN ges.catalogo CAT_OPT_FLL (NOLOCK) ON CAT_OPT_FLL.catalogo_id = PND.cat_operacion_id
            """, nativeQuery = true)
    List<Map<String, Object>> fetchContainerAdditionalDetails(
            @Param("eirContainersJson") String eirContainersJson,
            @Param("isFullId") Integer isFullId,
            @Param("isGateintypeFullImportId") Integer isGateintypeFullImportId,
            @Param("isGateintypeFullExportId") Integer isGateintypeFullExportId,
            @Param("isGateintypeFullStorageId") Integer isGateintypeFullStorageId,
            @Param("isGateintypeFullImportDescripcion") String isGateintypeFullImportDescripcion,
            @Param("isGateintypeFullExportDescripcion") String isGateintypeFullExportDescripcion,
            @Param("isGateintypeFullStorageDescripcion") String isGateintypeFullStorageDescripcion
    );

    @Query(value = """
            SELECT
                TCON.gatein_eir_id AS gatein_eir_id,
                EAZ_PVT.eir_actividad_zona_id AS eir_zone_activity_id
            FROM
                (
                    SELECT container_id, cat_empty_full_id, gatein_eir_id, gateout_eir_id
                    FROM OPENJSON(:eirContainersJson)
                    WITH (
                        container_id INT,
                        cat_empty_full_id NUMERIC(18, 0),
                        gatein_eir_id INT,
                        gateout_eir_id INT
                    )
                ) TCON
                CROSS APPLY (
                    SELECT ROW_NUMBER() OVER (ORDER BY EAZ.eir_actividad_zona_id DESC) AS ind,
                    EAZ.eir_actividad_zona_id
                    FROM sde.eir_actividad_zona EAZ WITH (NOLOCK)
                    WHERE EAZ.eir_id = TCON.gatein_eir_id
                    AND EAZ.activo = 1
                ) AS EAZ_PVT
            WHERE
                EAZ_PVT.ind = 1
            """, nativeQuery = true)
    List<Map<String, Object>> findLatestZoneActivities(
            @Param("eirContainersJson") String eirContainersJson);

    @Query(value = """
    SELECT TOP (:requiredQuantity)
     CON.contenedor_id AS contenedor_id,
     CON.numero_contenedor AS numero_contenedor,
     CON.cat_familia_id AS cat_familia_id,
     CON.cat_tamano_id AS cat_tamano_id,
     CON.cat_tipo_contenedor_id AS cat_tipo_contenedor_id,
     CON.cat_clase_id AS cat_clase_id,
     CON.linea_naviera_id AS linea_naviera_id,
     CON.tara AS tara,
     CON.carga_maxima AS carga_maxima,
     CON.codigo_iso_id AS codigo_iso_id,
     CON.cat_tipo_reefer_id AS cat_tipo_reefer_id,
     CON.cat_marca_motor_id AS cat_marca_motor_id,
     CON.shipper_own AS shipper_own,
     ISNULL(EZO.cat_zona_contenedor_id,
            (SELECT sdg.fn_GetEquipmentConditionID(EIR.eir_id, :isContainer, 'S', 'CUR'))
     ) AS cat_tipo_actividad_id,
     CAT_EZO.codigo AS actividad_codigo,
     CAT_EZO.descricion_larga AS actividad_descripcion,
     LNV.linea_naviera AS linea_naviera,
     LNV.nombre AS linea_naviera_nombre
    FROM sds.contenedor CON (NOLOCK)
    INNER JOIN sde.eir EIR (NOLOCK)
        ON EIR.eir_id = :eirNumber
    LEFT OUTER JOIN sde.eir_actividad_zona EAZ (NOLOCK)
        ON EAZ.eir_id = EIR.eir_id
    LEFT OUTER JOIN sde.eir_zona EZO (NOLOCK)
        ON EZO.eir_id = EIR.eir_id
    LEFT OUTER JOIN ges.catalogo CAT_EZO (NOLOCK)
        ON CAT_EZO.catalogo_id = ISNULL(
            EZO.cat_zona_contenedor_id,
            (SELECT sdg.fn_GetEquipmentConditionID(EIR.eir_id, :isContainer, 'S', 'CUR'))
        )
    LEFT OUTER JOIN sds.linea_naviera LNV (NOLOCK)
        ON LNV.linea_naviera_id = CON.linea_naviera_id
    WHERE
     CON.numero_contenedor = :containerNumber
     AND EIR.activo = 1
     AND CON.activo = 1
     AND ISNULL(EAZ.activo, 1) = 1
     AND ISNULL(EAZ.eir_actividad_zona_id, 0) = ISNULL(
        (SELECT TOP 1 EIA.eir_actividad_zona_id
         FROM sde.eir_actividad_zona EIA (NOLOCK)
         WHERE EIA.eir_id = EIR.eir_id
         ORDER BY EIA.fecha_registro DESC), 0)
     AND ISNULL(EZO.eir_zona_id, 0) = ISNULL(
        (SELECT TOP 1 EZN.eir_zona_id
         FROM sde.eir_zona EZN (NOLOCK)
         WHERE EZN.eir_id = EIR.eir_id
         ORDER BY EZN.fecha_registro DESC), 0)
""", nativeQuery = true)
    List<Object[]> getEquipmentAdditionalData(
            @Param("requiredQuantity") Integer requiredQuantity,
            @Param("isContainer") Integer isContainer,
            @Param("eirNumber") String eirNumber,
            @Param("containerNumber") String containerNumber
    );

    /*ORDER BY CON.numero_contenedor
    OFFSET 0 ROWS FETCH NEXT :requiredQuantity ROWS ONLY*/

    @Query("""
        SELECT DISTINCT c FROM Container c
        INNER JOIN ContainerLocation cl ON cl.container.id = c.id
        INNER JOIN Eir eir ON eir.container.id = c.id
        INNER JOIN EirActivityZone eaz ON eaz.eir.id = eir.id
        INNER JOIN EirZone ez ON ez.id = eaz.eirZone.id
        WHERE c.active = true
        AND c.catSize.id = :containerSizeId
        AND c.catContainerType.id = :containerTypeId
        AND (:shippingLineIds IS NULL OR c.shippingLine.id IN :shippingLineIds)
        AND eir.subBusinessUnitLocal.id = :businessUnitId
        AND cl.active = true
        AND (:maxLoad IS NULL OR c.maximunPayload >= :maxLoad)
        AND (:gradeId IS NULL OR c.catGrade.id = :gradeId)
        AND (:reeferTypeId IS NULL OR c.catReeferType.id = :reeferTypeId)
        AND (:containerOk IS NULL OR :containerOk = false OR ez.catContainerZone.code = 'OK')
        AND (:referenceContainer IS NULL OR c.containerNumber <> :referenceContainer)
        AND eir.id = (SELECT MAX(e2.id) FROM Eir e2 WHERE e2.container.id = c.id)
        AND eaz.id = (SELECT MAX(eaz2.id) FROM EirActivityZone eaz2
                     INNER JOIN Eir e3 ON e3.id = eaz2.eir.id
                     WHERE e3.container.id = c.id)
        AND NOT EXISTS (
            SELECT 1 FROM ContainerRestriction cr
            WHERE cr.container.id = c.id
            AND cr.catEmptyFull.id = 43083
            AND cr.releasedRestriction = false
            AND cr.active = true
            AND cr.subBusinessUnit.id = :businessUnitId
        )
        AND NOT EXISTS (
            SELECT 1 FROM ContainerPreassignment cp
            WHERE cp.container.id = c.id
            AND cp.active = true
        )
        """)
    List<Container> findEligibleContainersForOutgoingPlanning(
        @Param("containerSizeId") Integer containerSizeId,
        @Param("containerTypeId") Integer containerTypeId,
        @Param("shippingLineIds") List<Integer> shippingLineIds,
        @Param("businessUnitId") Integer businessUnitId,
        @Param("maxLoad") Integer maxLoad,
        @Param("gradeId") Integer gradeId,
        @Param("reeferTypeId") Integer reeferTypeId,
        @Param("containerOk") Boolean containerOk,
        @Param("referenceContainer") String referenceContainer
    );

}