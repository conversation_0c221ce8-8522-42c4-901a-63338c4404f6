package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.OauthAccessHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface OauthAccessHistoryRepository extends JpaRepository<OauthAccessHistory, Long> {

    // find all by authenticationIds and where logoutDate is null
    List<OauthAccessHistory> findByAuthenticationIdInAndLogoutDateIsNull(List<String> authenticationIds);

    // We will set the logout date as now
    default void bulkLogout(List<OauthAccessHistory> sessions) {
        if (sessions != null && !sessions.isEmpty()) {
            LocalDateTime now = LocalDateTime.now();
            for (OauthAccessHistory hist : sessions) {
                hist.setLogoutDate(now);
            }
            saveAll(sessions);
        }
    }
}

