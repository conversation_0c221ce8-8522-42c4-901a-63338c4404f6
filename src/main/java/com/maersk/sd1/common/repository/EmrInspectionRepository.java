package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.dto.EquipmentConditionEMRInspectionDTO;
import com.maersk.sd1.common.model.EmrInspection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface EmrInspectionRepository extends JpaRepository<EmrInspection, Integer> {

    @Query("SELECT new com.maersk.sd1.common.dto.EquipmentConditionEMRInspectionDTO(e.id, e.catStatus.id, e.flagDamageBox, e.flagDamageMachine) " +
            "FROM EmrInspection e " +
            "WHERE e.eir.id = :eirId " +
            "AND e.active = true " +
            "ORDER BY e.registrationDate DESC")
    EquipmentConditionEMRInspectionDTO findTopByEirIdOrderByDateRegistrationDesc(
            @Param("eirId") Integer eirId);

    @Query("SELECT ei FROM EmrInspection ei " +
            "WHERE ei.eir.id IN :eirIds " +
            "AND ei.active = true")
    List<EmrInspection> findActiveInspectionsByEirIds(@Param("eirIds") List<Integer> eirIds);

    @Query("SELECT ei FROM EmrInspection ei WHERE ei.eir.id = :eirId")
    Optional<EmrInspection> findByEirId(@Param("eirId") Integer eirId);
}