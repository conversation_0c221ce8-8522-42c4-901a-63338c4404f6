package com.maersk.sd1.common.repository;

import com.maersk.sd1.sdg.dto.TbRestriction;

import com.maersk.sd1.common.dto.ContainerRestrictionDetailDTO;
import com.maersk.sd1.common.model.ContainerRestriction;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Set;

public interface ContainerRestrictionRepository extends JpaRepository<ContainerRestriction, Integer> {

    @Query("SELECT new com.maersk.sd1.common.dto.ContainerRestrictionDetailDTO(rc.id, rc.restrictionAnnotation, mot.description) " +
            "FROM ContainerRestriction rc " +
            "JOIN ContainerRestrictionDetail rcd ON rc.id = rcd.containerRestriction.id " +
            "JOIN Catalog mot ON rcd.catRestrictionReason.id = mot.id " +
            "WHERE rc.container.id = :containerId " +
            "AND rc.subBusinessUnit.id = :subBusinessUnitId " +
            "AND rc.releasedRestriction = false " +
            "AND rc.catEmptyFull.id = :catEmptyFullId " +
            "AND rc.active = true " +
            "AND rcd.active = true")
    List<ContainerRestrictionDetailDTO> findRestrictions(@Param("containerId") Integer containerId,
                                                         @Param("subBusinessUnitId") Integer subBusinessUnitId,
                                                         @Param("catEmptyFullId") Integer catEmptyFullId);

    @Query(value = """
            SELECT 1 cr
            FROM
            ContainerRestriction cr
            WHERE
            cr.container.id = :equipmentId
            AND cr.subBusinessUnit.id = :subBusinessUnitId
            AND cr.releasedRestriction = :releasedRestriction
            AND cr.catEmptyFull.id = :catEmptyFullId
            AND cr.active = :active
            """)
    ContainerRestriction getContainerRestrictionByEquipmentIdSubBusinessUnitIdReleasedRestrictionCatEmptyFullIdAndActive(@Param("equipmentId") Integer equipmentId, @Param("subBusinessUnitId") Integer subBusinessUnitId,@Param("releasedRestriction") Boolean releasedRestriction, @Param("catEmptyFullId") Integer  catEmptyFullId, @Param("active") Boolean active);



    @Query(value = "SELECT t2.cat_empty_full_id AS catEmptyFullId, " +
            "t2.contenedor_id AS containerId, " +
            "ISNULL(t2.anotacion_restriccion,'') AS restrictionRemark, " +
            "LTRIM(STUFF((SELECT ', ' + RTRIM(motresx.descripcion) " +
            "FROM sde.restriccion_contenedor_detalle AS restdet (NOLOCK) " +
            "INNER JOIN ges.catalogo AS motresx (NOLOCK) ON restdet.cat_motivo_restriccion_id = motresx.catalogo_id " +
            "WHERE restdet.restriccion_cnt_id = t2.restriccion_cnt_id and restdet.activo = 1 " +
            "ORDER BY motresx.descripcion ASC FOR XML PATH('') ),1,1,'')) AS numeroBooking " +
            "FROM sde.restriccion_contenedor AS t2 (NOLOCK) " +
            "WHERE t2.restriccion_liberada = 0 AND t2.activo = 1", nativeQuery = true)
    List<TbRestriction> getRestriccionContenedores();


    @Query("SELECT r " +
            "FROM ContainerRestriction r " +
            "WHERE r.container.id = :containerId " +
            "AND r.subBusinessUnit.id = :subBusinessUnitId " +
            "AND r.catEmptyFull.id = :catEmptyFullId " +
            "AND r.releasedRestriction = false " +
            "AND r.active = true")
    ContainerRestriction findContainerRestrictionByParameters(
            @Param("containerId") Integer containerId,
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("catEmptyFullId") Integer catEmptyFullId
    );

    @Query(value = """
          SELECT t2.cat_empty_full_id AS cat_empty_full_id,
                 t2.contenedor_id AS container_id,
                 ISNULL(t2.anotacion_restriccion, '') AS restrictionRemark,
                 LTRIM(STUFF((SELECT ', ' + RTRIM(motresx.descripcion)
                              FROM sde.restriccion_contenedor_detalle AS restdet (NOLOCK)
                              INNER JOIN ges.catalogo AS motresx (NOLOCK)
                              ON restdet.cat_motivo_restriccion_id = motresx.catalogo_id
                              WHERE restdet.restriccion_cnt_id = t2.restriccion_cnt_id
                              AND restdet.activo = 1
                              ORDER BY motresx.descripcion ASC
                              FOR XML PATH('')), 1, 1, '')) AS reasons
          FROM sde.restriccion_contenedor AS t2 (NOLOCK)
          WHERE t2.cat_empty_full_id IN :catEmptyFullIds
          AND t2.restriccion_liberada = 0
          AND t2.activo = 1
          AND t2.contenedor_id IN :containerIds
          AND t2.sub_unidad_negocio_id IN :subBusinessUnitIds
          """, nativeQuery = true)
    List<Object[]> findRestrictionsByContainerIdsSubBusinessUnitIdsAndCatEmptyFullIds(
            @Param("containerIds") Set<Integer> containerIds,
            @Param("subBusinessUnitIds") Set<Integer> subBusinessUnitIds,
            @Param("catEmptyFullIds") Set<Integer> catEmptyFullIds);

    @Query("SELECT r FROM ContainerRestriction r " +
            "WHERE r.catEmptyFull.id IN :catEmptyFullIds " +
            "AND r.container.id IN :containerIds " +
            "AND r.subBusinessUnit.id IN :subBusinessUnitIds " +
            "AND r.releasedRestriction = false " +
            "AND r.active = true")
    List<ContainerRestriction> findActiveRestrictions(@Param("catEmptyFullIds") List<Integer> catEmptyFullIds,
                                                      @Param("containerIds") List<Integer> containerIds,
                                                      @Param("subBusinessUnitIds") List<Integer> subBusinessUnitIds);


    @Query("SELECT cr FROM ContainerRestriction cr "
            + "WHERE cr.active = true "
            + "  AND cr.releasedRestriction = false "
            + "  AND cr.catEmptyFull.id = :emptyFullId "
            + "  AND cr.subBusinessUnit.id = :subBusinessUnitId "
            + "  AND cr.container.id = :containerId ")
    List<ContainerRestriction> findActiveRestriction(@Param("containerId") Integer containerId,
                                                     @Param("subBusinessUnitId") Long subBusinessUnitId,
                                                     @Param("emptyFullId") Integer emptyFullId);

    List<ContainerRestriction> findByContainerIdAndSubBusinessUnitIdAndCatEmptyFullIdAndReleasedRestrictionAndActive(
            Integer containerId,
            Integer subBusinessUnitId,
            Integer catEmptyFullId,
            Boolean releasedRestriction,
            Boolean active
    );

    @Query("SELECT cr FROM ContainerRestriction cr " +
            "WHERE cr.container.id = :containerId " +
            "  AND cr.subBusinessUnit.id = :subBusinessUnitId " +
            "  AND cr.catEmptyFull.id = :isFullCatalogId " +
            "  AND cr.releasedRestriction = false " +
            "  AND cr.active = true")
    List<ContainerRestriction> findActiveFullRestrictions(@Param("containerId") Integer containerId,
                                                          @Param("subBusinessUnitId") Integer subBusinessUnitId,
                                                          @Param("isFullCatalogId") Integer isFullCatalogId);

    @Query("SELECT cr FROM ContainerRestriction cr " +
            "WHERE cr.subBusinessUnit.id = :subBusinessUnitId " +
            "  AND cr.container.id = :containerId " +
            "  AND cr.releasedRestriction = false " +
            "  AND cr.active = true " +
            "  AND cr.catEmptyFull.id = :catEmptyFullId")
    List<ContainerRestriction> findActiveRestrictionsForContainer(@Param("subBusinessUnitId") Long subBusinessUnitId,
                                                                  @Param("containerId") Integer containerId,
                                                                  @Param("catEmptyFullId") Integer catEmptyFullId);

    @Query("""
    SELECT rc.container.id
    FROM ContainerRestriction rc
    JOIN rc.containerRestrictionDetails rcd
    WHERE rc.active = true
      AND rc.releasedRestriction = false
      AND rc.subBusinessUnit.id = :parentBUId
      AND rc.catEmptyFull.id = :isMtyId
      AND rcd.active = true
    """)
    List<Integer> findContainersWithActiveRestrictions(@Param("parentBUId") Integer parentBUId,
                                                       @Param("isMtyId") Integer isMtyId);

    @Query("""
    SELECT rc.container.id
    FROM ContainerRestriction rc
    JOIN rc.containerRestrictionDetails rcd
    WHERE rc.active = true
      AND rc.releasedRestriction = false
      AND rc.subBusinessUnit.id = :parentBUId
      AND rc.catEmptyFull.id = :isFullId
      AND rcd.active = true
    """)
    List<Integer> findContainersWithActiveRestrictionsFull(@Param("parentBUId") Integer parentBUId,
                                                          @Param("isFullId") Integer isFullId);

}