package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Booking;
import com.maersk.sd1.common.model.VesselProgrammingDetail;
import com.maersk.sd1.sde.dto.PreAssignmentListProcessDTO;
import com.maersk.sd1.sds.dto.BookingAndCompanyNameProjection;
import com.maersk.sd1.sds.dto.BookingDataDTO;
import com.maersk.sd1.sds.dto.BookingDetailsDTO;
import com.maersk.sd1.sds.dto.BookingProductDTO;
import com.maersk.sd1.sds.dto.BookingDetailsService9DTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import java.time.LocalDateTime;

import java.util.List;
import java.util.Optional;

@Repository
public interface BookingRepository extends JpaRepository<Booking, Integer> {

    @Query(value = "SELECT BOO.booking_id, BOO.unidad_negocio_id, BOO.numero_booking, BOO.fecha_emision_booking, "
            + "BOO.puerto_embarque_id, BOO.puerto_descarga_id, BOO.puerto_destino_id, BOO.linea_naviera_id, "
            + "BOO.empresa_cliente_id, BOO.empresa_embarcador_id, BOO.deposito_vacio_id, BOO.deposito_full_id, "
            + "BOO.producto_id, BOO.temperatura_c, BOO.imo_id, BOO.booking_aprobado, BOO.booking_aprobado_fecha, "
            + "BOO.usuario_aprobado_id, BOO.mercaderia, BOO.cat_estado_booking, BOO.es_coldtreatment, "
            + "BOO.es_atmosfera_controlada, BOO.cat_referencia_coparn_id, BOO.fecha_referencia_coparn, "
            + "BOO.cat_origen_creacion_booking_id, BOO.activo, BOO.fecha_registro, BOO.fecha_modificacion, "
            + "BOO.sub_unidad_negocio_id, PRONAV.programacion_nave_detalle_id, PRONAV.nombre_nave, PRONAV.viaje, "
            + "PRONAV.operacion, PRONAV.manifiesto, PRONAV.fecha_eta, PRONAV.fecha_etd, "
            + "LIN.linea_naviera + ' - ' + LIN.nombre AS shipping_line_name, "
            + "PUL.puerto + ' - ' + PUL.nombre + IIF(PAL.nombre is null,'','/ '+PAL.nombre) AS port_loading_name, "
            + "PUI.puerto + ' - ' + PUI.nombre + IIF(PAI.nombre is null,'','/ '+PAI.nombre) AS port_discharge_name, "
            + "PUE.puerto + ' - ' + PUE.nombre + IIF(PAE.nombre is null,'','/ '+PAE.nombre) AS port_destination_name, "
            + "EMC.documento + ' - ' + EMC.razon_social AS company_client_name, "
            + "IIF(EMS.documento IS NULL, NULL, (EMS.documento + ' - ' + EMS.razon_social)) AS company_shipper_name, "
            + "PRD.nombre_producto + ' ('+PRD.codigo_producto+')' AS product_name, "
            + "IIF(DEE.deposito_id IS NULL, NULL, (DEE.codigo_deposito + ' - ' + DEE.nombre_deposito)) AS deposit_empty_name, "
            + "IIF(DEV.deposito_id IS NULL, NULL, (DEV.codigo_deposito + ' - ' + DEV.nombre_deposito)) AS deposit_full_name, "
            + "IIF(TIM.imo_id IS NULL, NULL, (TIM.codigo_imo + ' ' + ges.fn_MensajeTraducido(TIM.language_message_alias,1,:languageId))) AS imo_name, "
            + "BOO.cat_move_type_id, BOO.maersk_depot_with_sd1, BOO.origin_destination_depot_id, "
            + "DOD.nombre_deposito AS origin_destination_depot, CMT.alias AS cat_move_type_alias "
            + "FROM sds.booking AS BOO (NOLOCK) "
            + "INNER JOIN sds.linea_naviera AS LIN (NOLOCK) ON LIN.linea_naviera_id = BOO.linea_naviera_id "
            + "INNER JOIN sds.puerto AS PUL (NOLOCK) ON PUL.puerto_id = BOO.puerto_embarque_id "
            + "LEFT OUTER JOIN ges.pais AS PAL ON PAL.pais_id = PUL.pais_id "
            + "INNER JOIN sds.puerto AS PUI (NOLOCK) ON PUI.puerto_id = BOO.puerto_descarga_id "
            + "LEFT OUTER JOIN ges.pais AS PAI ON PAI.pais_id = PUI.pais_id "
            + "INNER JOIN sds.puerto AS PUE (NOLOCK) ON PUE.puerto_id = BOO.puerto_destino_id "
            + "LEFT OUTER JOIN ges.pais AS PAE ON PAE.pais_id = PUE.pais_id "
            + "INNER JOIN ges.empresa AS EMC (NOLOCK) ON EMC.empresa_id = BOO.empresa_cliente_id "
            + "LEFT JOIN ges.empresa AS EMS (NOLOCK) ON EMS.empresa_id = BOO.empresa_embarcador_id "
            + "INNER JOIN sds.producto PRD (NOLOCK) ON PRD.producto_id = BOO.producto_id "
            + "LEFT JOIN sds.deposito DEE (NOLOCK) ON DEE.deposito_id = BOO.deposito_vacio_id "
            + "LEFT JOIN sds.deposito DEV (NOLOCK) ON DEV.deposito_id = BOO.deposito_full_id "
            + "LEFT JOIN sds.imo TIM (NOLOCK) ON TIM.imo_id = BOO.imo_id "
            + "LEFT JOIN sds.deposito DOD (NOLOCK) ON DOD.deposito_id = BOO.origin_destination_depot_id "
            + "LEFT JOIN ges.catalogo CMT (NOLOCK) ON CMT.catalogo_id = BOO.cat_move_type_id "
            + "LEFT JOIN (SELECT a.programacion_nave_detalle_id, navex.nombre as nombre_nave, b.viaje, "
            + "operacion.descripcion + ' - ' + operacion.descricion_larga as operacion, "
            + "concat(a.manifiesto_ano,'-',a.manifiesto_numero) as manifiesto, "
            + "ISNULL(FORMAT(b.fecha_eta, 'dd/MM/yyyy'), '') as fecha_eta, "
            + "ISNULL(FORMAT(b.fecha_etd, 'dd/MM/yyyy'), '')as fecha_etd "
            + "FROM [sds].[programacion_nave_detalle] AS a (NOLOCK) "
            + "INNER JOIN [sds].[programacion_nave] AS b (NOLOCK) ON a.programacion_nave_id = b.programacion_nave_id "
            + "INNER JOIN ges.catalogo AS operacion (NOLOCK) ON a.cat_operacion_id = operacion.catalogo_id "
            + "INNER JOIN sds.nave AS navex (NOLOCK) ON b.nave_id = navex.nave_id) AS PRONAV "
            + "ON PRONAV.programacion_nave_detalle_id = BOO.programacion_nave_detalle_id "
            + "WHERE BOO.booking_id = :bookingId;", nativeQuery = true)
    List<Object[]> getBookingById(@Param("bookingId") Integer bookingId, @Param("languageId") Integer languageId);

    @Query(value = "SELECT " +
            "BKX.numero_booking AS bookingNumber, " +
            "NAVEX.nombre AS shipName, " +
            "PRONAV.viaje AS trip, " +
            "sds.fn_CatalogoTraducidoDesLarga(PRNADE.cat_operacion_id, :languageId) AS operationName, " +
            "BKX.cat_estado_booking AS bookingState, " +
            "sds.fn_CatalogoTraducidoDes(BKX.cat_estado_booking, :languageId) AS stateName, " +
            "BKX.booking_aprobado AS isBookingApproved, " +
            "ges.fn_MensajeTraducido(CAST(BKX.booking_aprobado AS VARCHAR(1)), 1, :languageId) AS bookingApprovalDesc, " +
            "ISNULL(FORMAT([ges].[fn_HoraLocal](BKX.unidad_negocio_id, BKX.fecha_registro), 'dd/MM/yyyy hh:mm tt'), '') AS registrationDate " +
            "FROM sds.booking AS BKX (NOLOCK) " +
            "INNER JOIN sds.programacion_nave_detalle AS PRNADE (NOLOCK) ON BKX.programacion_nave_detalle_id = PRNADE.programacion_nave_detalle_id " +
            "INNER JOIN sds.programacion_nave AS PRONAV (NOLOCK) ON PRNADE.programacion_nave_id = PRONAV.programacion_nave_id " +
            "INNER JOIN sds.nave AS NAVEX (NOLOCK) ON PRONAV.nave_id = NAVEX.nave_id " +
            "WHERE BKX.sub_unidad_negocio_id = :subBusinessUnitId " +
            "AND BKX.numero_booking = :bookingNumber " +
            "AND BKX.activo = 1 AND PRNADE.activo = 1 AND PRONAV.activo = 1 " +
            "ORDER BY BKX.fecha_registro DESC", nativeQuery = true)
    List<Object[]> getBookingExistenceDetails(@Param("subBusinessUnitId") Integer subBusinessUnitId,
                                              @Param("bookingNumber") String bookingNumber,
                                              @Param("languageId") Integer languageId);

    @Query("SELECT b.approvedBooking, COALESCE(b.remarkRulesName, '') " +
            "FROM Booking b WHERE b.id = :bookingId")
    List<Object[]> findBookingApprovedAndRemarkRulesName(@Param("bookingId") Integer bookingId);

    @Query("SELECT b FROM Booking b WHERE b.id IN :bookingIds AND b.subBusinessUnit.id IN :subBusinessUnitIds AND b.active = true")
    List<Booking> findByBookingIdsInAndSubBusinessUnitIds(
            @Param("bookingIds") List<Integer> bookingIds,
            @Param("subBusinessUnitIds") List<Integer> subBusinessUnitIds
    );
    @Query("SELECT new com.maersk.sd1.sds.dto.BookingDetailsDTO(b.vesselProgrammingDetail.id, b.bookingNumber, b.approvedBooking) FROM Booking b WHERE b.id = :bookingId")
    List<BookingDetailsDTO> findBookingDetails(@Param("bookingId") Integer bookingId);

    @Query("SELECT b FROM Booking b WHERE b.id = :bookingId and b.subBusinessUnit.id = :businessUnitId")
    Booking findByBookingIdAndBusinessUnitId(@Param("bookingId") Integer bookingId,
                                             @Param("businessUnitId") Integer businessUnitId);

    @Modifying
    @Transactional
    @Query("UPDATE Booking b "
            + "SET b.catBookingStatus.id = 43061, "
            + "    b.modificationUser.id = :usuarioId, "
            + "    b.modificationDate = CURRENT_TIMESTAMP, "
            + "    b.traceBooking = 'liberado manual' "
            + "WHERE b.id = :bookingId "
            + "  AND b.catBookingStatus.id <> 43061")
    int updateBookingStatusToActive(Integer bookingId, Integer usuarioId);

    @Query("SELECT true FROM Booking b WHERE UPPER(b.bookingNumber) = UPPER(:bookingNumber) AND b.vesselProgrammingDetail.id = :vesselScheduleDetailId AND b.active = true")
    Boolean existsByBookingNumber(@Param("vesselScheduleDetailId") Integer vesselScheduleDetailId, @Param("bookingNumber") String bookingNumber);

    @Query("SELECT CASE WHEN COUNT(b) > 0 THEN TRUE ELSE FALSE END " +
            "FROM Booking b " +
            "WHERE b.bookingNumber = :bookingNumber AND b.subBusinessUnit.id = :subBusinessUnitId")
    boolean existsByBookingNumberAndSubBusinessUnitId(@Param("bookingNumber") String bookingNumber,
                                                      @Param("subBusinessUnitId") Integer subBusinessUnitId);

    @Query("SELECT b FROM Booking b " +
            "WHERE b.bookingNumber = :bookingNumber " +
            "AND b.vesselProgrammingDetail.id = :vesselProgrammingDetailId " +
            "AND b.active = true " +
            "ORDER BY b.bookingIssueDate DESC")
    Optional<Booking> findTop1ByBookingNumberAndVesselProgrammingDetailId(
            @Param("bookingNumber") String bookingNumber,
            @Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId
    );

    @Query("SELECT b FROM Booking b " +
            "WHERE b.bookingNumber = :bookingNumber " +
            "AND b.subBusinessUnit.id = :subBusinessUnitId " +
            "AND b.active = true " +
            "ORDER BY b.bookingIssueDate DESC")
    Optional<Booking> findTop1ByBookingNumberAndSubBusinessUnitId(
            @Param("bookingNumber") String bookingNumber,
            @Param("subBusinessUnitId") Integer subBusinessUnitId
    );

    @Query("SELECT b FROM Booking b WHERE b.bookingNumber = :bookingNumber AND b.subBusinessUnit.id = :subBusinessUnitId " +
            "AND b.catBookingStatus.id <> :excludedStatusId AND b.active = true ORDER BY b.bookingIssueDate DESC")
    Optional<Booking> findTopBookingByFilters(
            @Param("bookingNumber") String bookingNumber,
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("excludedStatusId") Integer excludedStatusId
    );

    @Modifying
    @Query("UPDATE Booking b SET b.vesselProgrammingDetail.id = :vesselProgrammingDetailId, " +
            "b.modificationUser.id = :userModificationId, b.modificationDate = CURRENT_TIMESTAMP, " +
            "b.traceBooking = 'EDI-Replace.Vessel', b.bookingEdiReference.id = :ediCoparnReferenciaId " +
            "WHERE b.id = :bookingId " +
            "AND b.vesselProgrammingDetail.id <> :vesselProgrammingDetailId")
    int updateBookingDetails(@Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId,
                             @Param("userModificationId") Integer userModificationId,
                             @Param("ediCoparnReferenciaId") Integer ediCoparnReferenciaId,
                             @Param("bookingId") Integer bookingId);

    @Modifying
    @Query("UPDATE Booking b SET b.destinationPort.id = :portDestinationId, " +
            "b.modificationUser.id = :userRegistrationId, b.modificationDate = CURRENT_TIMESTAMP, " +
            "b.traceBooking = 'EDI-Replace.PD1', b.bookingEdiReference.id = :ediCoparnId " +
            "WHERE b.id = :bookingId AND b.destinationPort.id <> :portDestinationId")
    int updateBooking(@Param("portDestinationId") Integer portDestinationId,
                      @Param("userRegistrationId") Integer userRegistrationId,
                      @Param("ediCoparnId") Integer ediCoparnId,
                      @Param("bookingId") Integer bookingId);

    @Modifying
    @Query("UPDATE Booking b SET b.dischargePort.id = :dischargePortId, " +
            "b.modificationUser.id = :userRegistrationId, b.modificationDate = CURRENT_TIMESTAMP, " +
            "b.traceBooking = 'EDI-Replace.PD2', b.bookingEdiReference.id = :ediCoparnId " +
            "WHERE b.id = :bookingId AND b.dischargePort.id <> :dischargePortId")
    int updateBookingDischargeDetails(@Param("dischargePortId") Integer dischargePortId,
                                      @Param("userRegistrationId") Integer userRegistrationId,
                                      @Param("ediCoparnId") Integer ediCoparnId,
                                      @Param("bookingId") Integer bookingId);

    @Modifying
    @Query("UPDATE Booking b " +
            "SET b.clientCompany.id = :clientId, " +
            "    b.shipperCompany.id = :customerId, " +
            "    b.modificationUser.id = :modifiedById, " +
            "    b.modificationDate = CURRENT_TIMESTAMP, " +
            "    b.traceBooking = 'EDI-Replace.CUS', " +
            "    b.bookingEdiReference.id = :ediCoparnId " +
            "WHERE b.id = :bookingId AND b.clientCompany.id <> :clientId")
    int updateBookingByCustIdAndBookingId(@Param("clientId") Integer clientId,
                                          @Param("modifiedById") Integer modifiedById,
                                          @Param("ediCoparnId") Integer ediCoparnId,
                                          @Param("bookingId") Integer bookingId);

    @Modifying
    @Query("UPDATE Booking b " +
            "SET b.commodity = TRIM(:groupProductDescription), " +
            "    b.product.id = CASE WHEN :productId IS NULL OR :productId = 0 THEN b.product.id ELSE :productId END, " +
            "    b.modificationUser.id = :userRegistrationId, " +
            "    b.modificationDate = CURRENT_TIMESTAMP, " +
            "    b.traceBooking = 'EDI-Replace.MER', " +
            "    b.bookingEdiReference.id = :ediCoparnId " +
            "WHERE b.id = :bookingId")
    int updateBooking(@Param("groupProductDescription") String groupProductDescription,
                      @Param("productId") Integer productId,
                      @Param("userRegistrationId") Integer userRegistrationId,
                      @Param("ediCoparnId") Integer ediCoparnId,
                      @Param("bookingId") Integer bookingId);

    @Modifying
    @Query("UPDATE Booking b " +
            "SET b.imo.id = :imoId, " +
            "    b.modificationUser.id = :userRegistrationId, " +
            "    b.modificationDate = CURRENT_TIMESTAMP, " +
            "    b.traceBooking = 'EDI-Replace.IMO', " +
            "    b.bookingEdiReference.id = :ediCoparnId " +
            "WHERE b.id = :bookingId")
    int updateBookingImoDetails(@Param("imoId") Integer imoId,
                                @Param("userRegistrationId") Integer userRegistrationId,
                                @Param("ediCoparnId") Integer ediCoparnId,
                                @Param("bookingId") Integer bookingId);

    @Modifying
    @Query("UPDATE Booking b SET b.temperatureC = TRIM(:temperature), b.modificationUser.id = :userRegistrationId, b.modificationDate = CURRENT_TIMESTAMP, " +
            "b.traceBooking = 'EDI-Replace.TMP', b.bookingEdiReference.id = :ediCoparnId " +
            "WHERE b.id = :bookingId AND TRIM(COALESCE(b.temperatureC, '')) <> TRIM(COALESCE(:temperature, ''))")
    int updateBookingTemperature(
            @Param("temperature") String temperature,
            @Param("userRegistrationId") Integer userRegistrationId,
            @Param("ediCoparnId") Integer ediCoparnId,
            @Param("bookingId") Integer bookingId);

    @Modifying
    @Query("UPDATE Booking b " +
            "SET b.isColdtreatment = COALESCE(:coldTreatment, false), b.modificationUser.id = :userId, " +
            "b.modificationDate = CURRENT_TIMESTAMP, b.traceBooking = :traceBooking, " +
            "b.bookingEdiReference.id = :ediCoparnId " +
            "WHERE b.id = :bookingId AND COALESCE(b.isColdtreatment, false) <> COALESCE(:coldTreatment, false)")
    int updateBookingColdTreatment(
            @Param("coldTreatment") Boolean coldTreatment,
            @Param("userId") Integer userId,
            @Param("ediCoparnId") Integer ediCoparnId,
            @Param("bookingId") Integer bookingId,
            @Param("traceBooking") String traceBooking
    );

    @Modifying
    @Query("UPDATE Booking b " +
            "SET b.remarkRulesName = :ediRemarkRulesName, " +
            "    b.modificationUser.id = :userId, " +
            "    b.modificationDate = CURRENT_TIMESTAMP, " +
            "    b.traceBooking = :traceBooking, " +
            "    b.commodity = CASE " +
            "        WHEN :isConvertToFlex = true THEN CONCAT(COALESCE(b.commodity, ''), ' | OK TO FLEX') " +
            "        ELSE REPLACE(COALESCE(b.commodity, ''), ' | OK TO FLEX', '') " +
            "    END " +
            "WHERE b.id = :bookingId")
    int updateBookingRemarkRulesWithCommodityLogic(
            @Param("ediRemarkRulesName") String ediRemarkRulesName,
            @Param("userId") Integer userId,
            @Param("traceBooking") String traceBooking,
            @Param("isConvertToFlex") boolean isConvertToFlex,
            @Param("bookingId") Integer bookingId
    );

    @Query("""
        SELECT new com.maersk.sd1.sds.dto.BookingDataDTO(
            b.imo.id,
            b.commodity,
            CASE
                WHEN v.catOperation.id IN (42994, 42995, 47734, 47735) THEN 43007
                ELSE 43004
            END
        )
        FROM Booking b
        JOIN b.vesselProgrammingDetail v
        WHERE b.id = :bookingId
    """)
    BookingDataDTO findBookingDetailsByBookingId(@Param("bookingId") Integer bookingId);

    @Modifying
    @Query("UPDATE Booking b SET b.catBkEdiReference.id = :isBookingTypeUpdate, " +
            "b.bkEdiReferenceDate = CURRENT_TIMESTAMP, " +
            "b.bookingEdiReference.id = :ediCoparnId, " +
            "b.modificationUser.id = :usuarioRegistroId, " +
            "b.modificationDate = CURRENT_TIMESTAMP, " +
            "b.traceBooking = CASE WHEN :updateEmissionDate = true THEN 'edi-update-updemi' ELSE 'edi-update' END, " +
            "b.bookingIssueDate = CASE WHEN :updateEmissionDate = true THEN CURRENT_TIMESTAMP ELSE b.bookingIssueDate END " +
            "WHERE b.id = :bookingId")
    int updateBooking(@Param("bookingId") Integer bookingId,
                      @Param("isBookingTypeUpdate") Integer isBookingTypeUpdate,
                      @Param("ediCoparnId") Integer ediCoparnId,
                      @Param("usuarioRegistroId") Integer usuarioRegistroId,
                      @Param("updateEmissionDate") boolean updateEmissionDate);

    @Query("SELECT b FROM Booking b WHERE b.id = :bookingId AND b.active = :active")
    Optional<Booking> findByBookingIdAndActive(Integer bookingId, boolean active);
    boolean existsByVesselProgrammingDetailIdAndBookingNumberAndActive(Integer vesselProgrammingDetailId, String bookingNumber, Boolean active);

    @Query("SELECT b.id FROM Booking b " +
            "JOIN b.vesselProgrammingDetail vpd " +
            "JOIN vpd.vesselProgramming vp " +
            "WHERE b.bookingNumber = :booking " +
            "AND vp.subBusinessUnit.id = :subBusinessUnitId " +
            "AND b.catBookingStatus.id = :isStateBkCancel " +
            "AND b.active = true " +
            "AND vp.active = true " +
            "AND vpd.active = true")
    List<Integer> findCancelledBookingIds(@Param("booking") String booking,
                                          @Param("subBusinessUnitId") Integer subBusinessUnitId,
                                          @Param("isStateBkCancel") Integer isStateBkCancel);

    @Modifying
    @Query("UPDATE Booking b " +
            "SET b.catBookingStatus.id = :isStateBkActive, " +
            "b.remarkRulesName = :remarkRulesName, " +
            "b.modificationUser.id = :userRegistrationId, " +
            "b.modificationDate = :modificationDate, " +
            "b.traceBooking = 'Released by EDI', " +
            "b.bookingEdiReference.id = :ediCoparnId " +
            "WHERE b.id = :bookingId " +
            "AND b.catBookingStatus.id <> :isStateBkActive")
    void updateBookingStatus(@Param("bookingId") Integer bookingId,
                             @Param("isStateBkActive") Integer isStateBkActive,
                             @Param("remarkRulesName") String remarkRulesName,
                             @Param("userRegistrationId") Integer userRegistrationId,
                             @Param("ediCoparnId") Integer ediCoparnId,
                             @Param("modificationDate") LocalDateTime modificationDate);

    @Query("""
    SELECT new com.maersk.sd1.sds.dto.BookingDetailsService9DTO(
        b.id,
        CONCAT(n.name, '/', vp.voyage),
        b.vesselProgrammingDetail.id,
        pDest.port,
        pDesc.port,
        cli.document,
        b.commodity,
        imo.imoCode,
        b.temperatureC,
        b.isColdtreatment,
        b.isControlledAtmosphere
    )
    FROM Booking b
    JOIN b.vesselProgrammingDetail vpd
    JOIN vpd.vesselProgramming vp
    JOIN vp.vessel n
    JOIN b.destinationPort pDest
    JOIN b.dischargePort pDesc
    LEFT JOIN b.clientCompany cli
    LEFT JOIN b.imo imo
    WHERE b.bookingNumber = :bookingNumber
    AND b.subBusinessUnit.id = :subBusinessUnitId
    AND vpd.catOperation.id = :catOperationId
    AND b.active = true
    ORDER BY b.bookingIssueDate DESC
    """)
    List<BookingDetailsService9DTO> findTopBookingDetails(@Param("bookingNumber") String bookingNumber,
                                                          @Param("subBusinessUnitId") Integer subBusinessUnitId,
                                                          @Param("catOperationId") Integer catOperationId,
                                                          Pageable pageable);

    boolean existsByBookingNumberAndVesselProgrammingDetailIdAndCatBookingStatusIdAndActive(String bookingNumber, Integer vesselProgrammingDetailId, Integer catBookingStatus, Boolean active);

    Booking findTopByBookingNumberAndVesselProgrammingDetailIdAndCatBookingStatusIdAndActiveOrderByBookingIssueDateDesc(String bookingNumber, Integer vesselProgrammingDetailId, Integer catBookingStatus, Boolean active);


    @Query("SELECT b " +
            "FROM Booking b " +
            "JOIN VesselProgrammingDetail vp ON b.vesselProgrammingDetail.id = vp.id " +
            "WHERE b.id = :bookingId")
    Booking findBookingDetailsAndVesselDetailByBookingId(@Param("bookingId") Integer bookingId);

    @Query("SELECT b AS booking, c1.legalName AS companyName1, c2.legalName AS companyName2 " +
            "FROM Booking b " +
            "LEFT JOIN Company c1 ON b.clientCompany.id = c1.id " +
            "LEFT JOIN Company c2 ON b.shipperCompany.id = c2.id " +
            "WHERE b.id = :bookingId")
    BookingAndCompanyNameProjection getBookingAndCompanyName(@Param("bookingId") Integer bookingId);

    @Query("SELECT new com.maersk.sd1.sds.dto.BookingProductDTO(b.id, b.product.id) FROM Booking b WHERE b.id IN :bookingIds")
    List<BookingProductDTO> findProductIdsByBookingIds(@Param("bookingIds") List<Integer> bookingIds);

    @Modifying
    @Query("UPDATE Booking b SET b.approvedBooking = true, b.approvedBookingDate = CURRENT_TIMESTAMP, b.approvedUser.id = :registeredUserId WHERE b.id = :bookingId AND b.approvedBooking = false")
    void updateBookingApproval(@Param("registeredUserId") Integer registeredUserId, @Param("bookingId") Integer bookingId);

    @Query("""
            SELECT b
            FROM Booking b
            JOIN b.shippingLine l
            JOIN b.vesselProgrammingDetail d
            JOIN d.vesselProgramming p
            JOIN p.vessel v
            JOIN b.clientCompany c
            JOIN b.loadingPort pe
            JOIN b.dischargePort pd
            JOIN b.product prod
            JOIN d.catOperation op
            JOIN b.catBookingStatus est
            JOIN b.catOriginBookingCreation org
            LEFT JOIN b.shipperCompany emb
            LEFT JOIN b.imo imo
            LEFT JOIN b.catBkEdiReference ref
            JOIN b.registrationUser usua
            LEFT JOIN b.modificationUser usub
            WHERE b.active = true
              AND b.approvedBooking = true
              AND (
              b.bookingIssueDate BETWEEN :#{#dto.startDate} AND :#{#dto.endDate}
          )
          AND (COALESCE(:#{#dto.bookingNumber},'') = '' OR b.bookingNumber LIKE CONCAT(:#{#dto.bookingNumber}, '%'))
          AND (:#{#dto.vesselProgrammingDetailId} IS NULL OR d.id = :#{#dto.vesselProgrammingDetailId})
          AND (:#{#dto.bookingId} IS NULL OR b.id = :#{#dto.bookingId})
          AND b.subBusinessUnit.id = :#{#dto.subBusinessUnitId}
          AND (:#{#dto.operation} IS NULL OR op.id = :#{#dto.operation})
          AND (:#{#dto.clientCompanyId} IS NULL OR c.id = :#{#dto.clientCompanyId})
          AND (:#{#dto.shippingLineId} IS NULL OR l.id = :#{#dto.shippingLineId})
        ORDER BY b.id DESC
        """)
    Page<Booking> findBookings(
            @Param("dto") PreAssignmentListProcessDTO dto,
            Pageable pageable
    );

    @Query("SELECT b FROM Booking b " +
            "WHERE b.subBusinessUnit.id = :subBizUId " +
            "AND b.catBookingStatus.id = :isDocumentActive " +
            "AND b.approvedBooking = false " +
            "AND ((:bookingNumber IS NOT NULL AND b.bookingNumber = :bookingNumber) " +
            "OR (:bookingId IS NOT NULL AND b.id = :bookingId)) " +
            "ORDER BY b.bookingIssueDate DESC")
    Optional<Booking> findBookingToApprove(
            @Param("subBizUId") Integer subBusinessUnitId,
            @Param("isDocumentActive") Integer activeCatalogId,
            @Param("bookingNumber") String bookingNumber,
            @Param("bookingId") Integer bookingId
    );

    Optional<Booking> findByIdAndActiveTrue(Integer id);

    @Query("SELECT b FROM Booking b WHERE b.id = :bookingId AND b.active = true")
    Optional<Booking> findActiveBookingById(@Param("bookingId") Integer bookingId);

    @Query("SELECT COUNT(b.id) FROM Booking b " +
            "WHERE b.active = true " +
            "AND b.vesselProgrammingDetail.vesselProgramming.id = :vesselProgrammingId " +
            "AND (b.loadingPort.id = :portId OR b.dischargePort.id = :portId OR b.destinationPort.id = :portId)")
    long countByVesselProgrammingIdAndPortId(
            @Param("vesselProgrammingId") Integer vesselProgrammingId,
            @Param("portId") Integer portId
    );

    @Query("SELECT b FROM Booking b WHERE b.bookingNumber = :bookingNumber AND b.vesselProgrammingDetail.id = :vesselProgrammingDetailId AND b.active = true ORDER BY b.bookingIssueDate DESC")
    Optional<Booking> findTopByBookingNumberAndVesselProgrammingDetailIdOrderByBookingIssueDateDesc(@Param("bookingNumber") String bookingNumber, @Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId);

    // Using @Procedure to call the stored procedure
    @Procedure(name = "sds.aprobar_booking")
    void approveBooking(@Param("booking_id") Integer bookingId,
                        @Param("usuario_registro_id") Integer userRegistrationId,
                        @Param("cat_origen_aprobacion") Integer creationSourceForEdiCancel);

    @Query("SELECT COUNT(1) FROM Booking b "
            + "WHERE b.active = true "
            + "AND b.vesselProgrammingDetail.vesselProgramming.id = :programacionNaveId")
    int countActiveBookingsByProgramacionNaveId(@Param("programacionNaveId") Integer programacionNaveId);

    int countByVesselProgrammingDetailAndActiveTrue(VesselProgrammingDetail detail);

    @Query("SELECT COALESCE(b.remarkRulesName, '') FROM Booking b WHERE b.id = :bookingId")
    String findBookingRemarkRuleName(@Param("bookingId") Integer bookingId);

    Optional<Booking> findByBookingNumber(String bookingNumber);
}