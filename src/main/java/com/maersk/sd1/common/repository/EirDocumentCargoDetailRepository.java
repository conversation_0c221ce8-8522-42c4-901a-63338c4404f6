package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.EirDocumentCargoDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface EirDocumentCargoDetailRepository extends JpaRepository<EirDocumentCargoDetail, Integer> {
    @Query("select e from EirDocumentCargoDetail e where e.eir.id = :id and e.active = true")
    EirDocumentCargoDetail findByEirAndActiveTrue(@Param("id") Integer id);

    @Query("select e from EirDocumentCargoDetail e where e.eir.id = :eirId")
    EirDocumentCargoDetail findOneByEirId(@Param("eirId") Integer eirId);

    List<EirDocumentCargoDetail> findByEirId(Integer eirId);

    List<EirDocumentCargoDetail> findByEirIdAndActiveIsTrue(Integer eirId);

    @Query("SELECT e FROM EirDocumentCargoDetail e WHERE e.eir.id = :eirId ORDER BY e.id DESC")
    EirDocumentCargoDetail findTopByEirIdOrderByIdDesc(@Param("eirId") Integer eirId);

    boolean existsByCargoDocumentDetailIdAndActiveTrue(Integer cargoDocumentDetailId);

    @Query("SELECT e FROM EirDocumentCargoDetail e WHERE e.cargoDocumentDetail.id = :cargoDocumentDetail AND e.active = true")
    List<EirDocumentCargoDetail> findByDocumentoCargaDetalleIdAndActivo(@Param("cargoDocumentDetail") Integer cargoDocumentDetail);

    @Query("select E from EirDocumentCargoDetail E " +
            "where E.eir.id IN :eirId")
    List<EirDocumentCargoDetail> findManyByEirId(List<Integer> eirId);


    @Query("""
    SELECT COUNT(1)
    FROM EirDocumentCargoDetail edodx
    JOIN edodx.cargoDocumentDetail dodx
    JOIN edodx.eir eirx
    WHERE dodx.cargoDocument.id = :documentoCargaId
      AND eirx.catEmptyFull.id = :isFull
      AND eirx.catMovement.id = :isGateOut
      AND edodx.active = true
      AND dodx.active = true
      AND eirx.active = true
    """)
    Integer countAssignedContainers(@Param("documentoCargaId") Integer documentoCargaId,
                                    @Param("isFull") Integer isFull,
                                    @Param("isGateOut") Integer isGateOut);

    @Query("SELECT e FROM EirDocumentCargoDetail e " +
            "INNER JOIN e.eir eirx " +
            "WHERE e.eir.id = eirx.id " +
            "AND eirx.catMovement.id = :isGateOut " +
            "AND eirx.catEmptyFull.id = :isEmpty " +
            "AND e.active = true " +
            "AND eirx.active = true")
    List<EirDocumentCargoDetail> findActiveEirDocumentCargoDetails(@Param("isGateOut") Integer isGateOut,
                                                                   @Param("isEmpty") Integer isEmpty);



    @Query("SELECT edcd FROM EirDocumentCargoDetail edcd "
            + "JOIN edcd.eir e "
            + "WHERE edcd.active = true "
            + "  AND e.active = true "
            + "  AND e.container.id = :containerId "
            + "  AND e.subBusinessUnit.id = :subBusinessUnitId "
            + "  AND e.catMovement.alias = '43081' " // gate out alias
            + "  AND e.catEmptyFull.alias = '43083' ") // empty alias
    List<EirDocumentCargoDetail> findActiveGateOutEmpty(@Param("containerId") Integer containerId,
                                                        @Param("subBusinessUnitId") Long subBusinessUnitId);

    // Additional logic for verifying if there's a doc cargo detail for that container in gate-out with no closure.
    // The stored procedure's approach uses eir_documento_carga_detalle to see if there's an existing doc cargo detail.

    @Query("SELECT edcd FROM EirDocumentCargoDetail edcd "
            + "JOIN edcd.eir e "
            + "JOIN edcd.cargoDocumentDetail cdd "
            + "WHERE edcd.active = true "
            + "  AND e.active = true "
            + "  AND e.container.id = :containerId "
            + "  AND e.subBusinessUnit.id = :subBusinessUnitId "
            + "  AND cdd.id = edcd.cargoDocumentDetail.id ")
    List<EirDocumentCargoDetail> findAllValidDocuments(@Param("containerId") Integer containerId,
                                                       @Param("subBusinessUnitId") Long subBusinessUnitId);

    @Query("SELECT DISTINCT cd.cargoDocument.cargoDocument " +
            "FROM EirDocumentCargoDetail ed " +
            "JOIN ed.cargoDocumentDetail cd " +
            "JOIN cd.cargoDocument cdoc " +
            "WHERE ed.eir.id = :eirId " +
            "AND ed.active = true " +
            "AND cdoc.active = true")
    List<String> findCargoDocumentsByEirId(@Param("eirId") Integer eirId);


    @Query("SELECT e.cargoDocumentDetail.id FROM EirDocumentCargoDetail e WHERE e.cargoDocumentDetail.id IN :cargoDocumentDetailIds AND e.active = true")
    List<Integer> findActiveEirCargoDocumentDetailByIds(@Param("cargoDocumentDetailIds") List<Integer> detailIds);

    @Query("SELECT e FROM EirDocumentCargoDetail e WHERE e.eir.id = :eirId AND e.active = :active")
    List<EirDocumentCargoDetail> findByEirIdAndActive(@Param("eirId") Integer eirId, @Param("active") Boolean active);

    @Query("SELECT e FROM EirDocumentCargoDetail e WHERE e.cargoDocumentDetail.id IN :documentoCargaDetalleIds AND e.active = true")
    List<EirDocumentCargoDetail> findByDocumentoCargaDetalleIdsAndActivo(@Param("documentoCargaDetalleIds") List<Integer> documentoCargaDetalleIds);

    @Query(value = """
            SELECT new com.maersk.sd1.sdy.dto.ContainerFullInitialData(
                cnt.id,
                eir.id,
                eir.truckArrivalDate,
                cnt.containerNumber
            )
            FROM CargoDocument dcg
            JOIN CargoDocumentDetail dcd ON dcd.cargoDocument.id = dcg.id
            JOIN EirDocumentCargoDetail edcd ON edcd.cargoDocumentDetail.id = dcd.id
            JOIN VesselProgrammingDetail pnd ON pnd.id = dcg.vesselProgrammingDetail.id
            JOIN VesselProgramming pn ON pn.id = pnd.vesselProgramming.id
            JOIN Container cnt ON cnt.id = edcd.eir.container.id
            JOIN StockFull sfll ON sfll.container.id = cnt.id
            JOIN Eir eir ON eir.id = edcd.eir.id
            WHERE dcg.id = :documentoCargoId
              AND sfll.inStock = true
              AND sfll.active = true
              AND pn.active = true
              AND pnd.active = true
            """)
    List<com.maersk.sd1.sdy.dto.ContainerFullInitialData> findInitialContainerDataForFull(
            @Param("documentoCargoId") Integer documentoCargoId);
    @Query(value = "SELECT sdy.fn_get_container_location(:container_id)", nativeQuery = true)
    String getContainerLocation(@Param("container_id") Integer containerId);

    @Query(value = "SELECT ges.fn_CatalogTranslationDescLong(sdg.fn_GetEquipmentConditionID(:eir_id, :is_container, 'S', 'CUR'), :language_id)")
    String getEquipmentCondition(@Param("eir_id")Integer eir_id,@Param("is_container") Integer isContainer, @Param("language_id") Integer languageId);
}