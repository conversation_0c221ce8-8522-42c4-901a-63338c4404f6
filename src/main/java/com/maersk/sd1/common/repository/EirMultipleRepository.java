package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.EirMultiple;
import com.maersk.sd1.common.model.EirMultipleId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface EirMultipleRepository extends JpaRepository<EirMultiple, EirMultipleId> {
    List<EirMultiple> findByEirIdAndActiveIsTrue(Integer eirId);

    @Query("""
    SELECT em.id.eirMultipleId
    FROM EirMultiple em 
    JOIN em.eir e
    WHERE em.eir.id = :eirId
      AND em.active = true
      AND (e.active = true OR (e.active = false AND COALESCE(e.controlRevision, 0) = 2))
""")
    Integer findActiveEirMultipleIdWithConditions(@Param("eirId") Integer eirId);

    @Query("""
    SELECT a.id 
    FROM EirMultiple b 
    JOIN b.eir a 
    WHERE b.id.eirMultipleId = :eirMultipleId 
      AND b.active = true 
      AND (
        a.active = true OR 
        (a.active = false AND COALESCE(a.controlRevision, 0) = 2)
      )
    """)
    List<Integer> findEirIdsByEirMultipleId(@Param("eirMultipleId") Integer eirMultipleId);

    @Query("SELECT e.id.eirMultipleId FROM EirMultiple e WHERE e.id.eirId = :eirId AND e.active = :active")
    Integer findEirMultipleIdByEirIdAndActive(@Param("eirId") Integer eirId, @Param("active") boolean active);

    @Query("SELECT e.id.eirId FROM EirMultiple e WHERE e.id.eirMultipleId = :eirMultipleId AND e.active = :active")
    List<Integer> findEirIdsByEirMultipleIdAndActive(@Param("eirMultipleId") Integer eirMultipleId, @Param("active") boolean active);

}