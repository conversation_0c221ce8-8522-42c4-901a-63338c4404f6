package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.ChassisBookingDocument;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ChassisBookingDocumentRepository extends JpaRepository<ChassisBookingDocument, Integer> {

    @Modifying
    @Query("UPDATE ChassisBookingDocument d SET d.attendedQuantity = :quantityAttended, d.modificationUser.id = :userModificationId, d.modificationDate = CURRENT_TIMESTAMP WHERE d.id = :bookingChassisId")
    void updateQuantityAttendedAndOtherFields(@Param("bookingChassisId") Integer bookingChassisId, @Param("quantityAttended") int quantityAttended, @Param("userModificationId") Integer userModificationId);

    @Query("SELECT b.id FROM ChassisBookingDocument b WHERE b.chassisDocument.id = :docId AND b.catChassisType.id = :typeId AND b.active = true")
    List<Integer> findBookingIds(@Param("docId") Integer documentChassisId, @Param("typeId") Integer chassisTypeId);

    @Modifying
    @Query("UPDATE ChassisBookingDocument b SET b.quanty = b.quanty + 1 WHERE b.id = :bookingId")
    void updateBookingQuantity(@Param("bookingId") Integer bookingId);

    @Query(value="select cbd from ChassisBookingDocument cbd where cbd.chassisDocument.id in :ids and cbd.active = true")
    List<ChassisBookingDocument> findByChassisDocumentIds(@Param("ids")  List<Integer> ids);
}