package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.ChassisDocumentDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface ChassisDocumentDetailRepository extends JpaRepository<ChassisDocumentDetail, Integer> {
    @Query("""
            select count(c) from ChassisDocumentDetail c
            where c.chassisBookingDocument.id = :chassisBookingDocumentId and c.chassis.id is not null and c.active = true""")
    Integer countFindByChassisBookingDocumentIdAndChassisIdNotNullAndActiveTrue(@Param("chassisBookingDocumentId") Integer chassisBookingDocumentId);

    @Query(value = "SELECT C.chassisBookingDocument.id FROM ChassisDocumentDetail C " +
            "WHERE C.id = :documentChassisDetailId")
    Integer findDocumentChassisBookingIdByDocumentChassisDetailId(@Param("documentChassisDetailId") Integer documentChassisDetailId);

    @Query(value="SELECT COUNT(C.id) FROM ChassisDocumentDetail C " +
            "WHERE C.chassisBookingDocument = : bookingChassisId " +
            "AND C.chassis IS NOT NULL AND C.active = TRUE")
    Integer countDocumentChassisDetailByBookingChassisId(Integer bookingChassisId);

    @Query(value="SELECT C FROM ChassisDocumentDetail C " +
            "WHERE C.chassisBookingDocument = : bookingChassisId " +
            "AND C.chassis IS NOT NULL AND C.active = TRUE")
    ChassisDocumentDetail findDocumentChassisDetailByBookingChassisId(Integer bookingChassisId);
    @Query("SELECT COUNT(d) FROM ChassisDocumentDetail d WHERE d.chassisBookingDocument.id = :bookingChassisId AND d.chassis.id IS NOT NULL AND d.active = true")
    int countActiveDetailsByBookingChassisId(@Param("bookingChassisId") Integer bookingChassisId);


    @Modifying
    @Query("UPDATE ChassisDocumentDetail d SET d.chassis.id = :chassisId, d.catStatusChassis.id = :statusInProcess, " +
            "d.modificationUser.id = :userModificationId, d.modificationDate = CURRENT_TIMESTAMP, " +
            "d.traceChassisDocDetail = 'asig_gral_gout2' WHERE d.id = :documentChassisDetailId")
    void updateDocumentChassisDetail(@Param("documentChassisDetailId") Integer documentChassisDetailId,
                                     @Param("chassisId") Integer chassisId, @Param("statusInProcess") Integer statusInProcess,
                                     @Param("userModificationId") Integer userModificationId);

    @Query("SELECT dcd.chassisDocument.id " +
            "FROM ChassisDocumentDetail dcd " +
            "WHERE dcd.id = :detailId AND dcd.active = true")
    Integer findDocumentChassisIdByDetailId(@Param("detailId") Integer detailId);
}