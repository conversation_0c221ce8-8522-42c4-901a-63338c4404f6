package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.GateTransmissionSetting;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "edi_codeco_correlativo", schema = "sde")
public class GateTransmissionCorrelative {
    @Id
    @Column(name = "edi_codeco_correlativo_id", nullable = false)
    private Integer id;

    @Size(max = 10)
    @NotNull
    @Column(name = "identificador_emisor", nullable = false, length = 10)
    private String identifierEmitter;

    @NotNull
    @Column(name = "correlativo_numero_referencia", nullable = false)
    private Integer correlativeNumberReference;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "seteo_edi_codeco_id", nullable = false)
    private GateTransmissionSetting gateTransmissionSetting;

    @NotNull
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

}