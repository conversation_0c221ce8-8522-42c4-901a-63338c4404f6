package com.maersk.sd1.common.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Nationalized;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "email_enviado", schema = "ges", indexes = {
        @Index(name = "idx_msg_id", columnList = "msg_id")
})
public class SentEmail {
    @Id
    @Column(name = "email_enviado_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "email_plantilla_id", nullable = false)
    private EmailTemplate emailTemplate;

    @NotNull
    @Nationalized
    @Lob
    @Column(name = "remitente", nullable = false)
    private String sender;

    @NotNull
    @Nationalized
    @Lob
    @Column(name = "destinatario", nullable = false)
    private String recipient;

    @Nationalized
    @Lob
    @Column(name = "copia")
    private String copy;

    @Nationalized
    @Lob
    @Column(name = "copia_oculta")
    private String hiddenCopy;

    @NotNull
    @Nationalized
    @Lob
    @Column(name = "titulo", nullable = false)
    private String title;

    @NotNull
    @Nationalized
    @Lob
    @Column(name = "mensaje", nullable = false)
    private String message;

    @Size(max = 100)
    @Column(name = "msg_id", length = 100)
    private String messageId;

    @NotNull
    @ColumnDefault("'0'")
    @Column(name = "estado", nullable = false)
    private Character status;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

}