package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.CargoTemplateRoleId;
import com.maersk.sd1.common.model.Role;
import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "carga_plantilla_rol", schema = "ges")
public class LoadTemplateRole {
    @EmbeddedId
    private CargoTemplateRoleId id;

    @MapsId("roleId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "rol_id", nullable = false)
    private Role role;

    @MapsId("cargoTemplateId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "carga_plantilla_id", nullable = false)
    private LoadTemplate loadTemplate;

}