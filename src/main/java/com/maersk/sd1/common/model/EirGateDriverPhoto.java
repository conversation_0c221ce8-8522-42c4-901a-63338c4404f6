package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "eir_gate_driver_photo", schema = "sde", indexes = {
        @Index(name = "EIRGATEDRIV_PHOTO", columnList = "eir_id")
})
public class EirGateDriverPhoto {

    @Id
    @Column(name = "eir_gate_driver_photo_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "eir_id", nullable = false)
    private Eir eir;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "attached_id", nullable = false)
    private Attachment attachment;

    @NotNull
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @NotNull
    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_registration_id", nullable = false)
    private User registrationUser;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_modification_id")
    private User modificationUser;

}