package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "mensaje_idioma", schema = "ges", indexes = {
        @Index(name = "MESLANX_MESSAGE_LANGUAGE_ID", columnList = "tipo, codigo, idioma_id, activo")
})
public class MessageLanguage {
    @Id
    @Column(name = "mensaje_idioma_id", nullable = false)
    private Integer id;

    @Size(max = 200)
    @NotNull
    @Column(name = "tipo", nullable = false, length = 200)
    private String type;

    @NotNull
    @Column(name = "codigo", nullable = false)
    private Integer code;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "idioma_id", nullable = false)
    private Language language;

    @Size(max = 300)
    @Column(name = "mensaje", length = 300)
    private String message;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

}