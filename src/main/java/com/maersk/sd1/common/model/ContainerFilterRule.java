package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.IsoCode;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "rule_container_filter", schema = "sdy")
public class ContainerFilterRule {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "rule_container_filter_id", nullable = false)
    private Integer id;

    @Size(max = 100)
    @Column(name = "code", length = 100)
    private String code;

    @Size(max = 100)
    @Column(name = "name", length = 100)
    private String name;

    @Size(max = 100)
    @Column(name = "description", length = 100)
    private String description;

    @Size(max = 100)
    @Column(name = "container_number", length = 100)
    private String containerNumber;

    @Column(name = "container_tare")
    private Integer containerTare;

    @Column(name = "container_maximum_load")
    private Integer containerMaximumLoad;

    @Column(name = "container_manufacture_date")
    private LocalDateTime containerManufactureDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_container_family_id")
    private Catalog catContainerFamily;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_container_size_id")
    private Catalog catContainerSize;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_container_type_id")
    private Catalog catContainerType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_container_class_id")
    private Catalog catContainerClass;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_container_technology_id")
    private Catalog catContainerTechnology;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_engine_brand_id")
    private Catalog catEngineBrand;

    @Column(name = "flag_electrical_connection")
    private Boolean flagElectricalConnection;

    @Column(name = "flag_dangerous_cargo")
    private Boolean flagDangerousCharge;

    @NotNull
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "iso_code_id")
    private IsoCode isoCode;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "business_unit_id", nullable = false)
    private BusinessUnit businessUnit;

    @NotNull
    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "registration_user_id", nullable = false)
    private User registrationUser;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "modification_user_id")
    private User modificationUser;

}