package com.maersk.sd1.common.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "regla_planificacion_patio", schema = "sdy")
public class YardPlanningRule {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "regla_planificacion_patio_id", nullable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "linea_naviera_id")
    private ShippingLine shippingLine;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cliente_id")
    private Company customer;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_status_id")
    private Catalog catStatus;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_movimiento_id")
    private Catalog catMovement;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_actividad_contenedor_id")
    private Catalog catContainerActivity;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "familia_contenedor_id")
    private Catalog catContainerFamily;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tamano_contenedor_id")
    private Catalog catContainerSize;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tipo_contenedor_id")
    private Catalog catContainerType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "clase_contenedor_id")
    private Catalog catContainerGrade;

    @Column(name = "tipo_categoria_id")
    private Integer typeCategoryId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "condicion_reefer_id")
    private Catalog catReeferCondition;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "condicion_caja_id")
    private Catalog catConditionBox;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "condicion_ca_id")
    private Catalog catConditionCA;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "nave_id")
    private Vessel vessel;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "viaje_id")
    private Voyage voyage;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "puerto_descarga_id")
    private Port dischargePort;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "codigo_grupo_id")
    private GroupCode groupCode;

    @Size(max = 10)
    @NotNull
    @Column(name = "codigo", nullable = false, length = 10)
    private String code;

    @Size(max = 100)
    @Column(name = "descripcion", length = 100)
    private String description;

    @Column(name = "prioridad")
    private Integer priority;

    @Size(max = 10)
    @Column(name = "numero_documento_referencia", length = 10)
    private String documentNumberReference;

    @Column(name = "indice_ordenamiento")
    private Integer orderingIndex;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "patio_id")
    private Yard yard;

    @Column(name = "requiere_conexion_electrica")
    private Boolean requiredElectricConnection;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_tecnologia_id")
    private Catalog catTechnology;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_lavado_id")
    private Catalog catWashed;

    @Column(name = "carga_imo")
    private Boolean imoCargo;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_imo_id")
    private Catalog catImo;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_empty_full_id")
    private Catalog catEmptyFull;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_structure_condition_id")
    private Catalog catStructureCondition;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_machine_condition_id")
    private Catalog catMachineCondition;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_type_activity_id")
    private Catalog catActivityType;

}