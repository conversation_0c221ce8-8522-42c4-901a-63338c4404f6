package com.maersk.sd1.common.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class LanguageSystemId implements Serializable {
    private static final long serialVersionUID = 6782087114233839645L;
    @NotNull
    @Column(name = "sistema_id", nullable = false)
    private Integer systemId;

    @NotNull
    @Column(name = "idioma_id", nullable = false)
    private Integer languageId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        LanguageSystemId entity = (LanguageSystemId) o;
        return Objects.equals(this.systemId, entity.systemId) &&
                Objects.equals(this.languageId, entity.languageId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(systemId, languageId);
    }

}