package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Imo;
import com.maersk.sd1.common.model.RuleContainerFilterImoId;
import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "rule_container_filter_imo", schema = "sdy")
public class ContainerFilterRuleImo {
    @EmbeddedId
    private RuleContainerFilterImoId id;

    @MapsId("ruleContainerFilterId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "rule_container_filter_id", nullable = false)
    private ContainerFilterRule containerFilterRule;

    @MapsId("imoId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "imo_id", nullable = false)
    private Imo imo;

}