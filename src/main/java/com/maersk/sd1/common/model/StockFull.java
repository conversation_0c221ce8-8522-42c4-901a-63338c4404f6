package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "stock_full", schema = "sdf", indexes = {
        @Index(name = "STFULLX_CNT_ID", columnList = "container_id"),
        @Index(name = "STFULLX_EIR_GI", columnList = "eir_gatein_id"),
        @Index(name = "STFULLX_EIR_GO", columnList = "eir_gateout_id")
})
public class StockFull {
    @Id
    @Column(name = "stock_full_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "sub_business_unit_id", nullable = false)
    private BusinessUnit subBusinessUnit;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "container_id", nullable = false)
    private Container container;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "eir_gatein_id")
    private Eir gateInEir;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "eir_gateout_id")
    private Eir gateOutEir;

    @NotNull
    @Column(name = "in_stock", nullable = false)
    private Boolean inStock = false;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_registration_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_modification_id")
    private User modificationUser;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

    @Size(max = 50)
    @Column(name = "stock_trace", length = 50)
    private String traceStock;

}