package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.ChassisEdiSetting;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "chassis_edi_setting_parameter", schema = "sdh")
public class ChassisEdiSettingParameter {
    @Id
    @Column(name = "chassis_edi_setting_parameter_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "chassis_edi_setting_id", nullable = false)
    private ChassisEdiSetting chassisEdiSetting;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_chassis_edi_parameter_type_id", nullable = false)
    private Catalog catChassisEdiParameterType;

    @Size(max = 100)
    @NotNull
    @Column(name = "chassis_edi_setting_parameter_value", nullable = false, length = 100)
    private String chassisEdiSettingParameterValue;

    @NotNull
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_registration_id", nullable = false)
    private User registrationUser;

    @NotNull
    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_modification_id")
    private User modificationUser;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

}