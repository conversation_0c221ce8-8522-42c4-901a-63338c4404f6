package com.maersk.sd1.common.service;

import com.maersk.sd1.common.model.Container;
import com.maersk.sd1.common.repository.ContainerRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

@Service
public class ContainerService {

    private final ContainerRepository containerRepository;

    @Autowired
    public ContainerService(ContainerRepository containerRepository)
    {
        this.containerRepository = containerRepository;
    }

    public HashMap<String, Integer> findIdsByNumbers(List<String> containerNumbers) {
        HashMap<String, Integer> result = new HashMap<>();
        List<Object[]> rawResult = containerRepository.findIdsByNumbers(containerNumbers);
        rawResult.stream().forEach(rr -> {
            result.put((String) rr[0], (Integer) rr[1]);
        });
        return result;
    }

    public Container findByContainerNumber(String containerNumber) {
        return containerRepository.findByContainerNumber(containerNumber);
    }
}
