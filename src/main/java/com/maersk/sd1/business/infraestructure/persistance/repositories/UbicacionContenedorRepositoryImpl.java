package com.maersk.sd1.business.infraestructure.persistance.repositories;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Repository;

import com.maersk.sd1.business.core.planing.domain.Bloque;
import com.maersk.sd1.business.core.planing.domain.Celda;
import com.maersk.sd1.business.core.planing.domain.Contenedor;
import com.maersk.sd1.business.core.planing.domain.Nivel;
import com.maersk.sd1.business.core.planing.domain.Tipo;
import com.maersk.sd1.business.core.planing.domain.UbicacionContenedor;
import com.maersk.sd1.business.core.planing.domain.planificacion.UbicacionContenedorValor;
import com.maersk.sd1.business.core.planing.domain.planificacion.VisitaContenedor;
import com.maersk.sd1.business.core.planing.domain.planificacion.update_quantity_removed.UpdateQuantityRemovedCommand;
import com.maersk.sd1.business.core.planing.domain.planificacion_cantidadremovidos.ActualizarCantidadRemovidosCommand;
import com.maersk.sd1.business.core.planing.domain.planificacion_cantidadremovidos.ActualizarCantidadRemovidosDataBloque;
import com.maersk.sd1.business.core.planing.domain.planificacion_cantidadremovidos.ActualizarCantidadRemovidosDataCommand;
import com.maersk.sd1.business.core.planing.domain.planificacion_cantidadremovidos.ActualizarCantidadRemovidosDataResponse;
import com.maersk.sd1.business.core.planing.domain.planificacion_cantidadremovidos.ActualizarCantidadRemovidosDataUbicacionContenedor;
import com.maersk.sd1.business.core.planing.domain.planificacion_cantidadremovidos.ActualizarCantidadRemovidosResponse;
import com.maersk.sd1.business.core.planing.domain.planificacion_cantidadremovidos.UpdateAmountRemovedResponse;
import com.maersk.sd1.business.core.planing.port.UbicacionContenedorRepository;
import com.maersk.sd1.business.infraestructure.dto.operacion.ContenedorARemoverDto;
import com.maersk.sd1.business.infraestructure.shared.datamodel.ubicacion_entity;
import com.maersk.sd1.business.infraestructure.shared.utils.GsonUtil;
import com.google.gson.Gson;

import lombok.RequiredArgsConstructor;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.Procedure;

@Repository
@RequiredArgsConstructor
public class UbicacionContenedorRepositoryImpl implements UbicacionContenedorRepository {

	@Autowired
	private ApplicationContext context;

	@Override
	public Collection<UbicacionContenedor> TraerUbicaciones(int patio_id,
			Collection<UbicacionContenedorValor> contenedores) throws SQLException {
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.ubicacion_contenedor_obtener");
		Collection<UbicacionContenedor> respuestaBusqueda = null;

		var ubicaciones = contenedores.stream().map(c -> c.getBloque_codigo().trim() + "." + c.getFila().trim() + "."
				+ c.getColumna().trim() + "." + String.valueOf(c.getNivel())).collect(Collectors.joining("-"));

		try {
			pResult.input("patio_id", String.valueOf(patio_id), Jpo.INTEGER);
			pResult.input("ubicaciones", ubicaciones, Jpo.STRING);

			List<Object> resultado = (List<Object>) pResult.execute();
			resultado.removeIf(item -> item == null);
			if (!resultado.isEmpty()) {
				if (resultado.get(0) instanceof ArrayList<?>) {
					resultado = (List<Object>) resultado.get(0);
				}
			}

			Collection<ubicacion_entity> result = GsonUtil.GetList(resultado, ubicacion_entity.class);
			respuestaBusqueda = MapTo(result);

			conn.commit();

		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return respuestaBusqueda;
	}

	private Collection<UbicacionContenedor> MapTo(Collection<ubicacion_entity> ubicaciones) {
		var items = new ArrayList<UbicacionContenedor>();

		ubicaciones.forEach(u -> {
			var base = new UbicacionContenedor(
					new Bloque(u.getBloque_id(), u.getPatio_id(), u.getCat_bloque_id(), u.getUnidad_negocio_id(),
							u.getCodigo(), u.getNombre(), u.getFilas(), u.getColumnas(), u.getNiveles(), "",
							u.isActivo(), u.getBloque_cantidad_contenedores(), u.getBloque_tipo_codigo()),
					new Celda(u.getCelda_id(), u.getBloque_id(), u.getFila(), u.getColumna(), u.getIndice_fila(),
							u.getIndice_columna(), u.isBloqueado()),
					new Nivel(u.getNivel_id(), u.getCelda_id(), u.getIndice()));
			base.setActivo(true);
			base.setBloque_id(u.getBloque_id());
			base.setCelda_id(u.getCelda_id());
			base.setCelda_name(u.getFila() + "-" + u.getColumna());
			base.setColumna(u.getColumna());
			base.setFila(u.getFila());
			base.setIndice_columna(u.getIndice_columna());
			base.setIndice_fila(u.getIndice_fila());
			base.setNivel_id(u.getNivel_id());
			base.setPatio_id(u.getPatio_id());
			base.setUbicacion_contenedor_id(u.getUbicacion_contenedor_id());

			if (u.getContenedor_id() != null && u.getNumero_contenedor() != null && u.getCat_familia_id() != null
					&& u.getCat_tamano_id() != null && u.getCat_tipo_contenedor_id() != null
					&& u.getLinea_naviera_id() != null && u.getTara() != null && u.getCarga_maxima() != null) {
				var new_contenedor = new Contenedor(u.getContenedor_id(), u.getNumero_contenedor(),
						new Tipo(u.getCat_tamano_id(), u.getCatalogo_padre_id(), u.getCat_tamano_codigo(),
								u.getDescripcion(), u.isActivo()));
				base.setContenedor(new_contenedor);
			}

			items.add(base);
		});

		return items;
	}

	@Override
	public Collection<Celda> TraerUbicacionesPorBloque(int patio_id, int bloque_id) throws SQLException {
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.ubicacion_contenedor_obtener_bloque");
		Collection<Celda> respuestaBusqueda = null;

		try {
			pResult.input("patio_id", String.valueOf(patio_id), Jpo.INTEGER);
			pResult.input("bloque_id", String.valueOf(bloque_id), Jpo.INTEGER);

			List<Object> resultado = (List<Object>) pResult.execute();
			resultado.removeIf(item -> item == null);
			if (!resultado.isEmpty()) {
				if (resultado.get(0) instanceof ArrayList<?>) {
					resultado = (List<Object>) resultado.get(0);
				}
			}

			Collection<ubicacion_entity> result = GsonUtil.GetList(resultado, ubicacion_entity.class);
			respuestaBusqueda = MapToCelda(result);

			conn.commit();

		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return respuestaBusqueda;
	}

	@Override
	public Collection<Celda> TraerUbicacionesPorBloque(int patio_id, Collection<Integer> bloque_id_list)
			throws SQLException {
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.ubicacion_contenedor_obtener_bloque_lista");
		Collection<Celda> respuestaBusqueda = null;

		try {
			var bloque_ids_json = "["
					+ bloque_id_list.stream().map(id -> "{" + '"' + "bloque_id" + '"' + ":" + String.valueOf(id) + "}")
							.collect(Collectors.joining(","))
					+ "]";
			pResult.input("patio_id", String.valueOf(patio_id), Jpo.INTEGER);
			pResult.input("bloque_id_list", bloque_ids_json, Jpo.STRING);

			List<Object> resultado = (List<Object>) pResult.execute();

			resultado.removeIf(item -> item == null);
			if (!resultado.isEmpty()) {
				if (resultado.get(0) instanceof ArrayList<?>) {
					resultado = (List<Object>) resultado.get(0);
				}
			}

			Collection<ubicacion_entity> result = GsonUtil.GetList(resultado, ubicacion_entity.class);
			respuestaBusqueda = MapToCelda(result);

		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return respuestaBusqueda;
	}

	private Collection<Celda> MapToCelda(Collection<ubicacion_entity> ubicaciones) {
		var items = new ArrayList<Celda>();
		int celda_id = 0;
		int nivel_id = 0;
		Celda[] new_celda = { null };
		Nivel[] new_nivel = { null };

		ubicaciones.forEach(u -> {
			if (u.getCelda_id() != celda_id) {
				new_celda[0] = new Celda();
				new_celda[0].setActivo(u.isActivo());
				// new_celda[0].setBloque(null);
				new_celda[0].setBloqueado(u.isBloqueado());
				new_celda[0].setCantidad_contenedores(u.getCelda_cantidad_contenedores());
				new_celda[0].setCelda_id(u.getCelda_id());
				new_celda[0].setColumna(u.getColumna());
				new_celda[0].setFila(u.getColumna());
				new_celda[0].setIndice_columna(u.getIndice_columna());
				new_celda[0].setIndice_fila(u.getIndice_fila());
				new_celda[0].setName(u.getFila() + "-" + u.getColumna());
				new_celda[0].setNiveles(new ArrayList<Nivel>());
			}

			if (u.getNivel_id() != nivel_id) {
				new_nivel[0] = new Nivel();
				new_nivel[0].setActivo(u.isActivo());
				new_nivel[0].setCelda_id(u.getCelda_id());
				new_nivel[0].setIndice_columna(u.getIndice_columna());
				new_nivel[0].setIndice(u.getIndice());
				new_nivel[0].setNivel_id(u.getNivel_id());
				new_nivel[0].setIndice_fila(u.getIndice_fila());
				new_nivel[0].setContenedores(new ArrayList<UbicacionContenedor>());
			}

			var new_ubi_con = new UbicacionContenedor();
			new_ubi_con.setActivo(u.isActivo());
			new_ubi_con.setContenedor_id(u.getContenedor_id());
			new_ubi_con.setUbicacion_contenedor_id(u.getUbicacion_contenedor_id());
			new_ubi_con.setPatio_id(u.getPatio_id());

			new_nivel[0].getContenedores().add(new_ubi_con);
			new_celda[0].getNiveles().add(new_nivel[0]);

			items.add(new_celda[0]);
		});

		return items;
	}

	@Override
	public UbicacionContenedor TraerUbicacion(int patio_id, String bloque_codigo, Integer indice_fila,
			Integer indice_columna, Integer indice_nivel) throws SQLException {
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planificacion_ubicacion_traer");
		UbicacionContenedor respuestaBusqueda = null;

		try {
			pResult.input("patio_id", String.valueOf(patio_id), Jpo.INTEGER);
			pResult.input("bloque_codigo", bloque_codigo, Jpo.STRING);
			pResult.input("indice_fila", String.valueOf(indice_fila), Jpo.INTEGER);
			pResult.input("indice_columna", String.valueOf(indice_columna), Jpo.INTEGER);
			pResult.input("indice_nivel", String.valueOf(indice_nivel), Jpo.INTEGER);

			List<Object> resultado = (List<Object>) pResult.execute();

			resultado.removeIf(item -> item == null);
			if (!resultado.isEmpty()) {
				if (resultado.get(0) instanceof ArrayList<?>) {
					resultado = (List<Object>) resultado.get(0);
				}
			}

			ubicacion_entity response = GsonUtil.GetModel(resultado, ubicacion_entity.class);
			respuestaBusqueda = MapToUbicacionContenedor(response);
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return respuestaBusqueda;
	}

	private UbicacionContenedor MapToUbicacionContenedor(ubicacion_entity ubicacion) {
		var base = new UbicacionContenedor(
				new Bloque(ubicacion.getBloque_id(), ubicacion.getPatio_id(), ubicacion.getCat_bloque_id(),
						ubicacion.getUnidad_negocio_id(), ubicacion.getCodigo(), ubicacion.getNombre(),
						ubicacion.getFilas(), ubicacion.getColumnas(), ubicacion.getNiveles(), "", ubicacion.isActivo(),
						ubicacion.getBloque_cantidad_contenedores(), ubicacion.getBloque_tipo_codigo()),
				new Celda(ubicacion.getCelda_id(), ubicacion.getBloque_id(), ubicacion.getFila(),
						ubicacion.getColumna(), ubicacion.getIndice_fila(), ubicacion.getIndice_columna(),
						ubicacion.isBloqueado()),
				new Nivel(ubicacion.getNivel_id(), ubicacion.getCelda_id(), ubicacion.getIndice()));
		base.setActivo(true);
		base.setBloque_id(ubicacion.getBloque_id());
		base.setCelda_id(ubicacion.getCelda_id());
		base.setCelda_name(ubicacion.getFila() + "-" + ubicacion.getColumna());
		base.setColumna(ubicacion.getColumna());
		base.setFila(ubicacion.getFila());
		base.setIndice_columna(ubicacion.getIndice_columna());
		base.setIndice_fila(ubicacion.getIndice_fila());
		base.setNivel_id(ubicacion.getNivel_id());
		base.setPatio_id(ubicacion.getPatio_id());
		base.setUbicacion_contenedor_id(ubicacion.getUbicacion_contenedor_id());
		base.setContenedor_id(ubicacion.getContenedor_id());

		return base;
	}

	@Override
	public Collection<UbicacionContenedor> TraerUbicacionesPorCelda(int patio_id, Collection<Integer> celdas_id_list)
			throws SQLException {
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planificacion_obtener_ubicacion_celda");
		Collection<UbicacionContenedor> respuestaBusqueda = null;

		var celdas = "["
				+ celdas_id_list.stream().map(c -> "{" + '"' + "celda_id" + '"' + ": " + String.valueOf(c) + " }")
						.collect(Collectors.joining(","))
				+ "]";

		try {
			pResult.input("patio_id", String.valueOf(patio_id), Jpo.INTEGER);
			pResult.input("celdas", celdas, Jpo.STRING);

			List<Object> resultado = (List<Object>) pResult.execute();

			resultado.removeIf(item -> item == null);
			if (!resultado.isEmpty()) {
				if (resultado.get(0) instanceof ArrayList<?>) {
					resultado = (List<Object>) resultado.get(0);
				}
			}

			Collection<ubicacion_entity> result = GsonUtil.GetList(resultado, ubicacion_entity.class);
			respuestaBusqueda = MapTo(result);

			conn.commit();

		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return respuestaBusqueda;
	}

	@Override
	public Collection<VisitaContenedor> TraerContenedorARemover(String patio_codigo, String bloque_codigo, String fila,
			String columna, Integer nivel) throws SQLException {
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planificacion_removidos");
		Collection<VisitaContenedor> respuestaBusqueda = new ArrayList<VisitaContenedor>();

		try {
			pResult.input("patio_codigo", String.valueOf(patio_codigo), Jpo.STRING);
			pResult.input("bloque_codigo", String.valueOf(bloque_codigo), Jpo.STRING);
			pResult.input("fila", String.valueOf(fila), Jpo.STRING);
			pResult.input("columna", String.valueOf(columna), Jpo.STRING);
			pResult.input("nivel", String.valueOf(nivel), Jpo.INTEGER);
			pResult.input("page", String.valueOf(1), Jpo.INTEGER);
			pResult.input("size", String.valueOf(1), Jpo.INTEGER);

			List<Object> resultado = (List<Object>) pResult.execute();

			Collection<ContenedorARemoverDto> result = GsonUtil.GetList(resultado, ContenedorARemoverDto.class);

			result.stream().forEach(r -> {
				respuestaBusqueda.add(new VisitaContenedor(r.getNumero_contenedor(), r.getContenedor_id()));
			});

			conn.commit();
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return respuestaBusqueda;
	}

	@Override
	public Collection<UbicacionContenedor> TraerUbicaciones(int patio_id, List<UbicacionContenedorValor> contenedores)
			throws SQLException {
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.ubicacion_contenedor_obtener");
		Collection<UbicacionContenedor> respuestaBusqueda = null;

		var ubicaciones = contenedores.stream().map(c -> c.getBloque_codigo().trim() + "." + c.getFila().trim() + "."
				+ c.getColumna().trim() + "." + String.valueOf(c.getNivel())).collect(Collectors.joining("-"));

		try {
			pResult.input("patio_id", String.valueOf(patio_id), Jpo.INTEGER);
			pResult.input("ubicaciones", ubicaciones, Jpo.STRING);

			List<Object> resultado = (List<Object>) pResult.execute();
			resultado.removeIf(item -> item == null);
			if (!resultado.isEmpty()) {
				if (resultado.get(0) instanceof ArrayList<?>) {
					resultado = (List<Object>) resultado.get(0);
				}
			}

			Collection<ubicacion_entity> result = GsonUtil.GetList(resultado, ubicacion_entity.class);
			respuestaBusqueda = MapTo(result);

			conn.commit();

		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return respuestaBusqueda;
	}
	
	@Override
	public HashMap<String, Object> GetLocationsForHeap(int yard_id, int block_id, String containers_json)
			throws SQLException {
		HashMap<String, Object> result = new HashMap<>();
		var conn = context.getBean(Jpo.class);
		try {
			Procedure pResult = conn.procedure("sdy.get_heap_available_locations");
			pResult.input("yard_id", String.valueOf(yard_id), Jpo.INTEGER);
			pResult.input("block_id", String.valueOf(block_id), Jpo.INTEGER);
			pResult.input("containers_json", containers_json, Jpo.STRING);			
			
			ArrayList<ArrayList<Object>> resultado = (ArrayList<ArrayList<Object>>) pResult.execute();
			HashMap<String, Object> result_status = (HashMap<String, Object>) resultado.get(0).get(0);
			HashMap<String, Object> result_message = (HashMap<String, Object>) resultado.get(1).get(0);
			result.put("result_state", resultado.get(0) != null ? Integer.parseInt(result_status.get("result_status").toString()) : null);
			result.put("result_message", resultado.get(1) != null ? result_message.get("result_message").toString() : null);
			
			if (result.get("result_state") != null && (Integer) result.get("result_state") == 1 && resultado.get(2) instanceof ArrayList<?>) {				
				Collection<ubicacion_entity> locations = GsonUtil.GetList(resultado.get(2), ubicacion_entity.class);
				result.put("locations", MapTo(locations));
			}
			conn.commit();
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		
		return result;
	}
	
	
	@Override
	public ActualizarCantidadRemovidosDataResponse ObtenerDataActualizacionCantidadRemovidos(
			ActualizarCantidadRemovidosDataCommand command) throws SQLException {
		Gson gson = new Gson();
		ActualizarCantidadRemovidosDataResponse result = new ActualizarCantidadRemovidosDataResponse();
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planificacion_ubicacioncontenedor_cantidadremovidos_listar");
		try {
			pResult.input("bloques_filas", gson.toJson(command.getBloques_filas()), Jpo.STRING);
			List<Object> resultado = (List<Object>) pResult.execute();

			List<ActualizarCantidadRemovidosDataBloque> bloques = GsonUtil.GetList(resultado.get(0),
					ActualizarCantidadRemovidosDataBloque.class);
			List<ActualizarCantidadRemovidosDataUbicacionContenedor> ubicaciones_contenedor = GsonUtil
					.GetList(resultado.get(1), ActualizarCantidadRemovidosDataUbicacionContenedor.class);

			result.setBloques(bloques);
			result.setUbicaciones_contenedor(ubicaciones_contenedor);

			conn.commit();
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return result;
	}

	@Override
	public ActualizarCantidadRemovidosResponse ActualizarCantidadRemovidosUbicacionContenedor(
			ArrayList<ActualizarCantidadRemovidosCommand> ubicaciones_actualizables, int usuario_id)
			throws SQLException {

		Gson gson = new Gson();
		ActualizarCantidadRemovidosResponse result = new ActualizarCantidadRemovidosResponse();
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planificacion_ubicacioncontenedor_cantidadremovidos_actualizar");
		try {
			pResult.input("ubicaciones_actualizadas", gson.toJson(ubicaciones_actualizables), Jpo.STRING);
			pResult.input("usuario_id", String.valueOf(usuario_id), Jpo.INTEGER);
			List<Object> resultado = (List<Object>) pResult.execute();
			result = GsonUtil.GetModel(resultado, ActualizarCantidadRemovidosResponse.class);
			conn.commit();
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return result;
	}
	

	@Override
	public UpdateAmountRemovedResponse UpdateAmountRemovedLocationContainer(
			ArrayList<ActualizarCantidadRemovidosCommand> locations_updatable, String user_alias)
			throws SQLException {

		Gson gson = new Gson();
		UpdateAmountRemovedResponse result = new UpdateAmountRemovedResponse();
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planification_location_container_update_amount_removed");
		try {
			pResult.input("locations_updated", gson.toJson(locations_updatable), Jpo.STRING);
			pResult.input("user_alias", user_alias, Jpo.STRING);
			List<Object> resultado = (List<Object>) pResult.execute();
			result = GsonUtil.GetModel(resultado, UpdateAmountRemovedResponse.class);
			conn.commit();
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return result;
	}
	
	
	
	@Override
	public ActualizarCantidadRemovidosDataResponse GetDataForUpdateQuantityOfRemoved(
			UpdateQuantityRemovedCommand command) throws SQLException {
		Gson gson = new Gson();
		ActualizarCantidadRemovidosDataResponse result = new ActualizarCantidadRemovidosDataResponse();
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planificacion_ubicacioncontenedor_cantidadremovidos_listar");
		try {
			pResult.input("bloques_filas", gson.toJson(command.getBlock_tiers()), Jpo.STRING);
			List<Object> resultado = (List<Object>) pResult.execute();

			List<ActualizarCantidadRemovidosDataBloque> bloques = GsonUtil.GetList(resultado.get(0),
					ActualizarCantidadRemovidosDataBloque.class);
			List<ActualizarCantidadRemovidosDataUbicacionContenedor> ubicaciones_contenedor = GsonUtil
					.GetList(resultado.get(1), ActualizarCantidadRemovidosDataUbicacionContenedor.class);

			result.setBloques(bloques);
			result.setUbicaciones_contenedor(ubicaciones_contenedor);

			conn.commit();
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return result;
	}

	@Override	
	public ActualizarCantidadRemovidosResponse UpdateQuantityOfRemovedToEquipmentLocation(
			ArrayList<ActualizarCantidadRemovidosCommand> updatable_locations, String user_alias)  
				throws SQLException 
	{

		Gson gson = new Gson();
		ActualizarCantidadRemovidosResponse result = new ActualizarCantidadRemovidosResponse();
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planification_ubication_container_quantity_removed_update");
		try {
			pResult.input("updatable_locations_json", gson.toJson(updatable_locations), Jpo.STRING);
			pResult.input("user_alias", String.valueOf(user_alias), Jpo.STRING);
			List<Object> resultado = (List<Object>) pResult.execute();
			result = GsonUtil.GetModel(resultado, ActualizarCantidadRemovidosResponse.class);
			conn.commit();
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return result;
	}	
}
