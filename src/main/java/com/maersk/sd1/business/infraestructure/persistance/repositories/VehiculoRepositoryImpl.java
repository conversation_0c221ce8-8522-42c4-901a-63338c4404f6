package com.maersk.sd1.business.infraestructure.persistance.repositories;

import java.sql.SQLException;
import java.util.Collection;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Repository;

import com.maersk.sd1.business.core.planing.domain.Vehiculo;
import com.maersk.sd1.business.core.planing.port.VehiculoRepository;
import com.maersk.sd1.business.infraestructure.shared.utils.GsonUtil;

import lombok.RequiredArgsConstructor;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.Procedure;

@Repository
@RequiredArgsConstructor
public class VehiculoRepositoryImpl implements VehiculoRepository {

	@Autowired
	private ApplicationContext context;
	
	@Override
	public Collection<Vehiculo> TraerPorLista(Collection<String> placas) throws SQLException {
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planificacion_vehiculo_obtener_placas");
		Collection<Vehiculo> respuestaBusqueda = null;

		try {
			var lista_placas = "[" + placas.stream().map(placa -> "{ " + '"' + "placa" + '"' + ":" + '"' + placa + '"' + " }").collect(Collectors.joining(",")) + "]";
			pResult.input("placas", lista_placas, Jpo.STRING);

			var resultado = pResult.execute();
			respuestaBusqueda = GsonUtil.GetList(resultado, Vehiculo.class);

			conn.commit();

		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return respuestaBusqueda;
	}
}
