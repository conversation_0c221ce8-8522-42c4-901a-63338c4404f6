package com.maersk.sd1.business.infraestructure.dto.planificacion;

import com.maersk.sd1.business.core.planing.domain.planificacion.DelimitedBlockRange;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class DelimitedBlockRuleRangeDto {
	private int bloque_id;
	private DelimitedBlockRange rango;
	
	@Override
	public boolean equals(Object obj) {
		if (obj == this)
			return true;
		if(!(obj instanceof DelimitedBlockRuleRangeDto))
			return false;
		DelimitedBlockRuleRangeDto u = (DelimitedBlockRuleRangeDto)obj;
		return  u.bloque_id == this.bloque_id &&
				u.rango.equals(this.rango);
	}
}
