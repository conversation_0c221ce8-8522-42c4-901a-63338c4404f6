package com.maersk.sd1.business.infraestructure.configuration;

import java.sql.SQLException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.context.annotation.RequestScope;

import com.maersk.sd1.business.core.planing.port.BloqueRepository;
import com.maersk.sd1.business.core.planing.port.CatalogoRepository;
import com.maersk.sd1.business.core.planing.port.ColaTrabajoRepository;
import com.maersk.sd1.business.core.planing.port.ContenedorRepository;
import com.maersk.sd1.business.core.planing.port.InstruccionMovimientoRepository;
import com.maersk.sd1.business.core.planing.port.OperacionRepository;
import com.maersk.sd1.business.core.planing.port.PatioRepository;
import com.maersk.sd1.business.core.planing.port.ReglasUbicacionRepository;
import com.maersk.sd1.business.core.planing.port.UbicacionContenedorRepository;
import com.maersk.sd1.business.core.planing.port.UsuarioRepository;
import com.maersk.sd1.business.core.planing.port.VehiculoRepository;
import com.maersk.sd1.business.core.planing.port.WorkOrderRepository;
import com.maersk.sd1.business.core.planing.usecase.InstruccionMovimientoBuilderUseCase;
import com.maersk.sd1.business.core.planing.usecase.InstruccionMovimientoBuilderUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.operacion.ProcesoActualizacionUbicacionUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.operacion.ProcesoConfirmarMovimientoUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.operacion.ProcesoNuevaUbicacionUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.operacion.ProcesoRemovidoUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.operacion.ProcesoUbicacionAsignadaUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.operacion.movilizacion.PlanificacionConfirmacionMovimientoUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.operacion.movilizacion.PlanificacionNuevaUbicacionUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.operacion.movilizacion.PlanificacionRehandlingUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.operacion.movilizacion.PlanificacionUbicacionAsignadaUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.planificacion.ActualizarCantidadRemovidosUseCase;
import com.maersk.sd1.business.core.planing.usecase.planificacion.ActualizarCantidadRemovidosUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.planificacion.ActualizarSecuenciaAColaTrabajoUseCase;
import com.maersk.sd1.business.core.planing.usecase.planificacion.ActualizarSecuenciaAColaTrabajoUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.planificacion.BuscarContenedoresParaSalidaUseCase;
import com.maersk.sd1.business.core.planing.usecase.planificacion.BuscarContenedoresParaSalidaUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.planificacion.ColasTrabajoUseCase;
import com.maersk.sd1.business.core.planing.usecase.planificacion.ColasTrabajoUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.planificacion.ConfirmarInstruccionMovimientoUseCase;
import com.maersk.sd1.business.core.planing.usecase.planificacion.ConfirmarInstruccionMovimientoUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.planificacion.DeterminarUbicacionOrigenUseCase;
import com.maersk.sd1.business.core.planing.usecase.planificacion.DeterminarUbicacionOrigenUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.planificacion.EstrategiaPlanificacion;
import com.maersk.sd1.business.core.planing.usecase.planificacion.PlanificacionMovimientoCamionUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.planificacion.SeleccionUbicacionDisponible;
import com.maersk.sd1.business.core.planing.usecase.planificacion.SeleccionUbicacionDisponibleImpl;
import com.maersk.sd1.business.core.planing.usecase.planificacion.WorkOrderUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.planificacion.asignacionubicacion.UbicacionAsignadaDespacho;
import com.maersk.sd1.business.core.planing.usecase.planificacion.asignacionubicacion.UbicacionAsignadaPorBloque;
import com.maersk.sd1.business.core.planing.usecase.planificacion.asignacionubicacion.UbicacionAsignadaPorRegla;
import com.maersk.sd1.business.core.planing.usecase.planificacion.asignacionubicacion.UbicacionAsignadaPreseleccionada;
import com.maersk.sd1.business.core.planing.usecase.planificacion.desentierro.PlanificacionDesentierroUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.planificacion.ingreso.PlanificacionIngresoUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.planificacion.movilizacion.PlanificacionMovilizacionUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.planificacion.reglaubicacion.BuscarReglaCorrespondienteUseCase;
import com.maersk.sd1.business.core.planing.usecase.planificacion.reglaubicacion.BuscarReglaCorrespondienteUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.planificacion.reglaubicacion.ManejadorReglasPlanificacionUseCase;
import com.maersk.sd1.business.core.planing.usecase.planificacion.reglaubicacion.ManejadorReglasPlanificacionUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.planificacion.reglaubicacion.ObtenerReglasPlanificacionUseCase;
import com.maersk.sd1.business.core.planing.usecase.planificacion.reglaubicacion.ObtenerReglasPlanificacionUseCaseImpl;
import com.maersk.sd1.business.core.planing.usecase.planificacion.salida.PlanificacionSalidaUseCaseImpl;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import ohSolutions.ohJpo.dao.Jpo;

@Configuration
public class PlaningConfig {

	@Bean
	public ObjectMapper objectMapper() {
		ObjectMapper mapper = new ObjectMapper();
		mapper.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);
		return mapper;
	}

	@Bean
	@RequestScope
	public Jpo JPOInstance() throws SQLException {
		Jpo jpo = null;
		try {
			jpo = new Jpo();
		} catch (Exception e) {
			jpo.finalizar();
		}
		return jpo;
	}

	@Autowired
	private BloqueRepository bloqueRepository;
	@Autowired
	private OperacionRepository operacionRepository;
	@Autowired
	private UbicacionContenedorRepository ubicacionContenedoRepository;
	@Autowired
	private ContenedorRepository contenedorRepository;
	@Autowired
	private InstruccionMovimientoRepository instruccionMovimientoRepository;
	@Autowired
	private CatalogoRepository catalogoRepository;
	@Autowired
	private ReglasUbicacionRepository reglasUbicacionRepository;
	@Autowired
	private UbicacionContenedorRepository ubicacionContenedorRepository;
	@Autowired
	private UsuarioRepository usuarioRepository;
	@Autowired
	private ColaTrabajoRepository colaTrabajoRepository;
	@Autowired
	private VehiculoRepository vehiculoRepository;
	@Autowired
	private PatioRepository patioRepository;
	@Autowired
	private WorkOrderRepository workOrderRepository;
	@Autowired
	private com.maersk.sd1.sdy.service.FindMostAvailableFullContainerGateOutService findMostAvailableFullContainerGateOutService;
	@Autowired
	private com.maersk.sd1.sdy.service.FindMostAvailableEmptyContainerGateOutService findMostAvailableEmptyContainerGateOutService;

	@Bean
	public InstruccionMovimientoBuilderUseCase createInstruccionMovimientoBuilderUseCase() {
		return new InstruccionMovimientoBuilderUseCaseImpl(catalogoRepository);
	}

	@Bean
	public SeleccionUbicacionDisponible createSeleccionUbicacionDisponible() {
		return new SeleccionUbicacionDisponibleImpl();
	}

	@Bean
	public BuscarReglaCorrespondienteUseCase createBuscarReglaPlanificacionUseCase() {
		return new BuscarReglaCorrespondienteUseCaseImpl();
	}

	@Bean
	public DeterminarUbicacionOrigenUseCase createDeterminarUbicacionOrigenUseCase() {
		return new DeterminarUbicacionOrigenUseCaseImpl(ubicacionContenedoRepository);
	}

	@Bean
	public ManejadorReglasPlanificacionUseCase createManejadorReglasPlanificacionUseCase() {
		return new ManejadorReglasPlanificacionUseCaseImpl(reglasUbicacionRepository,
				createBuscarReglaPlanificacionUseCase());
	}

	@Bean
	public ObtenerReglasPlanificacionUseCase createObtenerReglasPlanificacionUseCase() {
		return new ObtenerReglasPlanificacionUseCaseImpl(createManejadorReglasPlanificacionUseCase());
	}

	@Bean
	public ColasTrabajoUseCase createColasTrabajoUseCase() {
		return new ColasTrabajoUseCaseImpl(colaTrabajoRepository);
	}

	@Bean
	public ActualizarCantidadRemovidosUseCase CreateActualizarCantidadRemovidosUseCase() {
		return new ActualizarCantidadRemovidosUseCaseImpl(ubicacionContenedoRepository);
	}

	@Bean
	public ConfirmarInstruccionMovimientoUseCase createConfirmarInstruccionMovimientoUseCase() {
		return new ConfirmarInstruccionMovimientoUseCaseImpl(instruccionMovimientoRepository,
				CreateActualizarCantidadRemovidosUseCase());
	}

	@Bean
	public ActualizarSecuenciaAColaTrabajoUseCase CreateReordenamientoSecuenciaColaTrabajoUseCase() {
		return new ActualizarSecuenciaAColaTrabajoUseCaseImpl(instruccionMovimientoRepository);
	}

	@Bean
	public EstrategiaPlanificacion createEstrategiaPlanificacionPorBloque() {
		return new UbicacionAsignadaPorBloque(bloqueRepository, reglasUbicacionRepository,
				instruccionMovimientoRepository, ubicacionContenedorRepository, createSeleccionUbicacionDisponible());
	}

	@Bean
	public EstrategiaPlanificacion createEstrategiaPlanificacionPorRegla() {
		return new UbicacionAsignadaPorRegla(bloqueRepository, reglasUbicacionRepository,
				instruccionMovimientoRepository, ubicacionContenedorRepository, createSeleccionUbicacionDisponible());
	}

	@Bean
	public EstrategiaPlanificacion createEstrategiaPlanificacionPorUbicacionSeleccionada() {
		return new UbicacionAsignadaPreseleccionada(ubicacionContenedorRepository);
	}

	@Bean
	public EstrategiaPlanificacion createEstrategiaPlanificacionPorDespacho() {
		return new UbicacionAsignadaDespacho(bloqueRepository);
	}

	@Bean
	public ObtenerReglasPlanificacionUseCase createValidacionReglaBloque() {
		return new ObtenerReglasPlanificacionUseCaseImpl(createManejadorReglasPlanificacionUseCase());
	}

	@Bean
	public BuscarContenedoresParaSalidaUseCase CreatePlanificarContenedorSalidaBusquedaUseCase() {
		return new BuscarContenedoresParaSalidaUseCaseImpl(contenedorRepository);
	}

	@Bean
	@Primary
	public PlanificacionMovilizacionUseCaseImpl createPlanificacionMovilizacionUseCase() {
		return new PlanificacionMovilizacionUseCaseImpl(bloqueRepository, ubicacionContenedoRepository,
				contenedorRepository, instruccionMovimientoRepository, operacionRepository, usuarioRepository,
				reglasUbicacionRepository, createColasTrabajoUseCase(), createSeleccionUbicacionDisponible(),
				createInstruccionMovimientoBuilderUseCase(), createObtenerReglasPlanificacionUseCase(),
				createEstrategiaPlanificacionPorBloque(), createEstrategiaPlanificacionPorUbicacionSeleccionada(),
				createEstrategiaPlanificacionPorRegla(), createColasTrabajoUseCase(),
				CreateReordenamientoSecuenciaColaTrabajoUseCase(), createDeterminarUbicacionOrigenUseCase());
	}

	@Bean
	public PlanificacionIngresoUseCaseImpl createPlanificacionIngresoUseCaseImpl() {
		return new PlanificacionIngresoUseCaseImpl(bloqueRepository, instruccionMovimientoRepository,
				ubicacionContenedoRepository, usuarioRepository, contenedorRepository, operacionRepository,
				catalogoRepository, createInstruccionMovimientoBuilderUseCase(),
				createObtenerReglasPlanificacionUseCase(), createEstrategiaPlanificacionPorRegla(),
				createColasTrabajoUseCase(), CreateReordenamientoSecuenciaColaTrabajoUseCase(),
				createDeterminarUbicacionOrigenUseCase(), patioRepository);
	}

	@Bean
	public PlanificacionSalidaUseCaseImpl createPlanificacionSalidaUseCaseImpl() {
		return new PlanificacionSalidaUseCaseImpl(bloqueRepository, instruccionMovimientoRepository,
				ubicacionContenedoRepository, usuarioRepository, contenedorRepository, operacionRepository,
				catalogoRepository, createInstruccionMovimientoBuilderUseCase(),
				createObtenerReglasPlanificacionUseCase(), createEstrategiaPlanificacionPorDespacho(),
				createColasTrabajoUseCase(), CreateReordenamientoSecuenciaColaTrabajoUseCase(),
				createDeterminarUbicacionOrigenUseCase(), patioRepository,
				findMostAvailableFullContainerGateOutService, findMostAvailableEmptyContainerGateOutService);
	}
	
	@Bean
	public PlanificacionDesentierroUseCaseImpl createPlanificacionDesentierroUseCaseImpl() {
		return new PlanificacionDesentierroUseCaseImpl(bloqueRepository, instruccionMovimientoRepository,
				ubicacionContenedoRepository, usuarioRepository, contenedorRepository, operacionRepository,
				catalogoRepository, createInstruccionMovimientoBuilderUseCase(),
				createObtenerReglasPlanificacionUseCase(), createEstrategiaPlanificacionPorRegla(),
				createColasTrabajoUseCase(), CreateReordenamientoSecuenciaColaTrabajoUseCase(),
				createDeterminarUbicacionOrigenUseCase(), patioRepository);
	}

	@Bean
	public PlanificacionMovimientoCamionUseCaseImpl createPlanificacionMovimientoCamionUseCaseImpl() {
		return new PlanificacionMovimientoCamionUseCaseImpl(usuarioRepository, contenedorRepository,
				operacionRepository, vehiculoRepository, ubicacionContenedoRepository, bloqueRepository,
				instruccionMovimientoRepository, catalogoRepository, createInstruccionMovimientoBuilderUseCase(),
				createColasTrabajoUseCase(), CreateReordenamientoSecuenciaColaTrabajoUseCase());
	}

	@Bean
	public PlanificacionRehandlingUseCaseImpl createPlanificacionRehandlingUseCaseImpl() {
		return new PlanificacionRehandlingUseCaseImpl(bloqueRepository, ubicacionContenedoRepository,
				contenedorRepository, instruccionMovimientoRepository, operacionRepository, usuarioRepository,
				reglasUbicacionRepository, createColasTrabajoUseCase(), createSeleccionUbicacionDisponible(),
				createInstruccionMovimientoBuilderUseCase(), createObtenerReglasPlanificacionUseCase(),
				createEstrategiaPlanificacionPorBloque(), createEstrategiaPlanificacionPorUbicacionSeleccionada(),
				createEstrategiaPlanificacionPorRegla(), createColasTrabajoUseCase(),
				CreateReordenamientoSecuenciaColaTrabajoUseCase(), createDeterminarUbicacionOrigenUseCase());
	}

	@Bean
	public PlanificacionNuevaUbicacionUseCaseImpl createPlanificacionNuevaUbicacionUseCaseImpl() {
		return new PlanificacionNuevaUbicacionUseCaseImpl(bloqueRepository, ubicacionContenedoRepository,
				contenedorRepository, instruccionMovimientoRepository, operacionRepository, usuarioRepository,
				reglasUbicacionRepository, createColasTrabajoUseCase(), createSeleccionUbicacionDisponible(),
				createInstruccionMovimientoBuilderUseCase(), createObtenerReglasPlanificacionUseCase(),
				createEstrategiaPlanificacionPorBloque(), createEstrategiaPlanificacionPorUbicacionSeleccionada(),
				createEstrategiaPlanificacionPorRegla(), createColasTrabajoUseCase(),
				CreateReordenamientoSecuenciaColaTrabajoUseCase(), createDeterminarUbicacionOrigenUseCase());
	}

	@Bean
	public PlanificacionUbicacionAsignadaUseCaseImpl createPlanificacionUbicacionAsignadaUseCaseImpl() {
		return new PlanificacionUbicacionAsignadaUseCaseImpl(bloqueRepository, ubicacionContenedoRepository,
				contenedorRepository, instruccionMovimientoRepository, operacionRepository, usuarioRepository,
				reglasUbicacionRepository, createColasTrabajoUseCase(), createSeleccionUbicacionDisponible(),
				createInstruccionMovimientoBuilderUseCase(), createObtenerReglasPlanificacionUseCase(),
				createEstrategiaPlanificacionPorBloque(), createEstrategiaPlanificacionPorUbicacionSeleccionada(),
				createEstrategiaPlanificacionPorRegla(), createColasTrabajoUseCase(),
				CreateReordenamientoSecuenciaColaTrabajoUseCase(), createDeterminarUbicacionOrigenUseCase());
	}

	@Bean
	public PlanificacionConfirmacionMovimientoUseCaseImpl createPlanificacionConfirmacionMovimientoUseCaseImpl() {
		return new PlanificacionConfirmacionMovimientoUseCaseImpl(bloqueRepository, ubicacionContenedoRepository,
				contenedorRepository, instruccionMovimientoRepository, operacionRepository, usuarioRepository,
				reglasUbicacionRepository, createColasTrabajoUseCase(), createSeleccionUbicacionDisponible(),
				createInstruccionMovimientoBuilderUseCase(), createObtenerReglasPlanificacionUseCase(),
				createEstrategiaPlanificacionPorBloque(), createEstrategiaPlanificacionPorUbicacionSeleccionada(),
				createEstrategiaPlanificacionPorRegla(), createColasTrabajoUseCase(),
				CreateReordenamientoSecuenciaColaTrabajoUseCase(), createDeterminarUbicacionOrigenUseCase(),
				colaTrabajoRepository, vehiculoRepository);
	}
	
	@Bean
	public WorkOrderUseCaseImpl createWorkOrderUseCaseImpl() {
		return new WorkOrderUseCaseImpl(workOrderRepository);
	}

	@Bean
	public ProcesoRemovidoUseCaseImpl createProcesoRemovidoUseCaseImpl() {
		return new ProcesoRemovidoUseCaseImpl(instruccionMovimientoRepository,
				createPlanificacionRehandlingUseCaseImpl());
	}

	@Bean
	public ProcesoNuevaUbicacionUseCaseImpl createProcesoNuevaUbicacionUseCaseImpl() {
		return new ProcesoNuevaUbicacionUseCaseImpl(createPlanificacionNuevaUbicacionUseCaseImpl());
	}

	@Bean
	public ProcesoUbicacionAsignadaUseCaseImpl createProcesoUbicacionAsignadaUseCaseImpl() {
		return new ProcesoUbicacionAsignadaUseCaseImpl(createPlanificacionUbicacionAsignadaUseCaseImpl(),
				instruccionMovimientoRepository, createConfirmarInstruccionMovimientoUseCase());
	}

	@Bean
	public ProcesoActualizacionUbicacionUseCaseImpl createProcesoActualizacionUbicacionUseCaseImpl() {
		return new ProcesoActualizacionUbicacionUseCaseImpl(instruccionMovimientoRepository,
				createConfirmarInstruccionMovimientoUseCase());
	}

	@Bean
	public ProcesoConfirmarMovimientoUseCaseImpl createProcesoConfirmarEqControlUseCaseImpl() {
		return new ProcesoConfirmarMovimientoUseCaseImpl(instruccionMovimientoRepository,
				createConfirmarInstruccionMovimientoUseCase(), createPlanificacionConfirmacionMovimientoUseCaseImpl(),
				ubicacionContenedorRepository, operacionRepository);
	}
}
