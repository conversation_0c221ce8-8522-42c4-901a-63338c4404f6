package com.maersk.sd1.business.infraestructure.persistance.repositories;

import java.sql.SQLException;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Repository;

import com.maersk.sd1.business.core.planing.domain.Bloque;
import com.maersk.sd1.business.core.planing.domain.RangoReglaPlanificacionPatio;
import com.maersk.sd1.business.core.planing.domain.ReglaPlanificacionPatio;
import com.maersk.sd1.business.core.planing.port.ReglasUbicacionRepository;
import com.maersk.sd1.business.infraestructure.shared.utils.GsonUtil;

import lombok.RequiredArgsConstructor;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.Procedure;

@Repository
@RequiredArgsConstructor
public class ReglasUbicacionRepositoryImpl implements ReglasUbicacionRepository {
	@Autowired
	private ApplicationContext context;
	
	@Override
	public Collection<ReglaPlanificacionPatio> TraerReglasDelPatio(int patio_id) throws SQLException {
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.buscar_reglas_patio");
		Collection<ReglaPlanificacionPatio> respuestaBusqueda = null;

		try {
			pResult.input("bloque_id", String.valueOf(patio_id), Jpo.INTEGER);

			List<Object> resultado = (List<Object>) pResult.execute();

			respuestaBusqueda = GsonUtil.GetList(resultado, ReglaPlanificacionPatio.class);

		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return respuestaBusqueda;
	}
	
	

	@Override
	public Collection<ReglaPlanificacionPatio> TraerReglasDelBloque(int bloque_id) throws SQLException {
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.regla_planificacion_buscar_bloque");
		Collection<ReglaPlanificacionPatio> respuestaBusqueda = null;

		try {
			pResult.input("bloque_id", String.valueOf(bloque_id), Jpo.INTEGER);

			List<Object> resultado = (List<Object>) pResult.execute();

			respuestaBusqueda = GsonUtil.GetList(resultado, ReglaPlanificacionPatio.class);

			conn.commit();

		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return respuestaBusqueda;
	}

	@Override
	public Collection<RangoReglaPlanificacionPatio> TraerRangoCeldasPorBloqueReglas(
			int unidad_negocio_id,
			int bloque_id, Collection<Integer> regla_id_list) throws SQLException {
		
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.buscar_reglas_patio_bloque_reglas");
		
		Collection<RangoReglaPlanificacionPatio> respuestaBusqueda = null;
		var id_list = regla_id_list.stream().map(c -> String.valueOf(c)).collect(Collectors.joining("-"));

		try {
			pResult.input("bloque_id", String.valueOf(bloque_id), Jpo.INTEGER);
			pResult.input("regla_id_list", String.valueOf(id_list), Jpo.STRING);

			List<Object> resultado = (List<Object>) pResult.execute();

			respuestaBusqueda = GsonUtil.GetList(resultado, RangoReglaPlanificacionPatio.class);
			respuestaBusqueda.forEach(b -> {
				b.setBloque(new Bloque(b.getBloque_id(), b.getPatio_id(), b.getCat_bloque_id(), unidad_negocio_id, 
						b.getBloque_codigo(), b.getBloque_nombre(), b.filas, b.columnas, b.niveles, 
						b.configuracion, b.isBloque_activo(), b.cantidad_contenedores, b.getCat_bloque_codigo()));
				b.getBloque().setCat_bloque_codigo(b.getCat_bloque_codigo());
			});
			
			conn.commit();

		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		} 
		return respuestaBusqueda;
	}

	@Override
	public Collection<RangoReglaPlanificacionPatio> TraerRangoCeldasPorReglas(Collection<Integer> regla_id_list) throws SQLException {
		
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.buscar_reglas_patio_id_reglas");
		
		Collection<RangoReglaPlanificacionPatio> respuestaBusqueda = null;
		var id_list = "[" + regla_id_list.stream().map(c -> "{" + '"' + "regla_planificacion_patio_id" + '"' + ": " + String.valueOf(c) + "}").collect(Collectors.joining(",")) + "]"; 

		try {
			pResult.input("regla_id_list", String.valueOf(id_list), Jpo.STRING);
			
			pResult.input("skip_block_id", null, Jpo.INTEGER);
			pResult.input("row_skip_from", null, Jpo.INTEGER);
			pResult.input("row_skip_to", null, Jpo.INTEGER);

			List<Object> resultado = (List<Object>) pResult.execute();

			respuestaBusqueda = GsonUtil.GetList(resultado, RangoReglaPlanificacionPatio.class);
			respuestaBusqueda.forEach(b -> {
				b.setBloque(new Bloque(b.getBloque_id(), b.getPatio_id(), b.getCat_bloque_id(), null, 
						b.getBloque_codigo(), b.getBloque_nombre(), b.filas, b.columnas, b.niveles, 
						b.configuracion, b.isBloque_activo(), b.cantidad_contenedores, b.getCat_bloque_codigo()));
			});
			
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return respuestaBusqueda;
	}
	
	@Override
	public Collection<RangoReglaPlanificacionPatio> TraerRangoCeldasPorReglas(Collection<Integer> regla_id_list, Collection<Integer> skip_range, Integer skip_block_id) throws SQLException {
		
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.buscar_reglas_patio_id_reglas");
		
		Collection<RangoReglaPlanificacionPatio> respuestaBusqueda = null;
		var id_list = "[" + regla_id_list.stream().map(c -> "{" + '"' + "regla_planificacion_patio_id" + '"' + ": " + String.valueOf(c) + "}").collect(Collectors.joining(",")) + "]"; 

		try {
			pResult.input("regla_id_list", String.valueOf(id_list), Jpo.STRING);
			if (skip_range != null && !skip_range.isEmpty() && skip_range.toArray()[0] != null && skip_range.toArray()[1] != null && skip_block_id != null)
			{
				pResult.input("skip_block_id", String.valueOf(skip_block_id), Jpo.INTEGER);
				pResult.input("row_skip_from", String.valueOf(skip_range.toArray()[0]), Jpo.INTEGER);
				pResult.input("row_skip_to", String.valueOf(skip_range.toArray()[1]), Jpo.INTEGER);
			}

			List<Object> resultado = (List<Object>) pResult.execute();

			respuestaBusqueda = GsonUtil.GetList(resultado, RangoReglaPlanificacionPatio.class);
			respuestaBusqueda.forEach(b -> {
				b.setBloque(new Bloque(b.getBloque_id(), b.getPatio_id(), b.getCat_bloque_id(), null, 
						b.getBloque_codigo(), b.getBloque_nombre(), b.filas, b.columnas, b.niveles, 
						b.configuracion, b.isBloque_activo(), b.cantidad_contenedores, b.getCat_bloque_codigo()));
			});
			
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return respuestaBusqueda;
	}
	
	@Override
	public Collection<ReglaPlanificacionPatio> FindRulesByCriteriaInBlock(String rulesCriteria_json, Integer blockId) throws SQLException {
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.regla_planificacion_patio_get_rules_by_criteria_in_block");
		Collection<ReglaPlanificacionPatio> response = null;
		try {
			pResult.input("block_id", String.valueOf(blockId), Jpo.INTEGER);
			pResult.input("rules_criteria_json", rulesCriteria_json, Jpo.STRING);
			pResult.input("skip_block_id", null, Jpo.INTEGER);
			pResult.input("row_skip_from", null, Jpo.INTEGER);
			pResult.input("row_skip_to", null, Jpo.INTEGER);
			List<Object> resultado = (List<Object>) pResult.execute();
			response = GsonUtil.GetList(resultado, ReglaPlanificacionPatio.class);
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return response;
	}
	
	@Override
	public Collection<ReglaPlanificacionPatio> FindRulesByCriteriaInYard(String rulesCriteria_json, Integer yardId) throws SQLException {
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.regla_planificacion_patio_get_rules_by_criteria_in_yard");
		Collection<ReglaPlanificacionPatio> response = null;
		try {
			pResult.input("yard_id", String.valueOf(yardId), Jpo.INTEGER);
			pResult.input("rules_criteria_json", rulesCriteria_json, Jpo.STRING);
			pResult.input("skip_block_id", null, Jpo.INTEGER);
			pResult.input("row_skip_from", null, Jpo.INTEGER);
			pResult.input("row_skip_to", null, Jpo.INTEGER);
			List<Object> resultado = (List<Object>) pResult.execute();
			response = GsonUtil.GetList(resultado, ReglaPlanificacionPatio.class);
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return response;
	}



	@Override
	public Collection<ReglaPlanificacionPatio> FindRulesByCriteriaInBlock(String rulesCriteria_json, Integer blockId,
			Collection<Integer> skipRows, Integer skipBlockId) throws SQLException {
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.regla_planificacion_patio_get_rules_by_criteria_in_block");
		Collection<ReglaPlanificacionPatio> response = null;
		try {
			pResult.input("block_id", String.valueOf(blockId), Jpo.INTEGER);
			pResult.input("rules_criteria_json", rulesCriteria_json, Jpo.STRING);
			pResult.input("skip_block_id", String.valueOf(skipBlockId), Jpo.INTEGER);
			pResult.input("row_skip_from", String.valueOf(skipRows.toArray()[0]), Jpo.INTEGER);
			pResult.input("row_skip_to", String.valueOf(skipRows.toArray()[1]), Jpo.INTEGER);			
			List<Object> resultado = (List<Object>) pResult.execute();
			response = GsonUtil.GetList(resultado, ReglaPlanificacionPatio.class);
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return response;
	}



	@Override
	public Collection<ReglaPlanificacionPatio> FindRulesByCriteriaInYard(String rulesCriteria_json, Integer yardId,
			Collection<Integer> skipRows, Integer skipBlockId) throws SQLException {
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.regla_planificacion_patio_get_rules_by_criteria_in_yard");
		Collection<ReglaPlanificacionPatio> response = null;
		try {
			pResult.input("yard_id", String.valueOf(yardId), Jpo.INTEGER);
			pResult.input("rules_criteria_json", rulesCriteria_json, Jpo.STRING);
			pResult.input("skip_block_id", String.valueOf(skipBlockId), Jpo.INTEGER);
			pResult.input("row_skip_from", String.valueOf(skipRows.toArray()[0]), Jpo.INTEGER);
			pResult.input("row_skip_to", String.valueOf(skipRows.toArray()[1]), Jpo.INTEGER);
			List<Object> resultado = (List<Object>) pResult.execute();
			response = GsonUtil.GetList(resultado, ReglaPlanificacionPatio.class);
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return response;
	}
}
