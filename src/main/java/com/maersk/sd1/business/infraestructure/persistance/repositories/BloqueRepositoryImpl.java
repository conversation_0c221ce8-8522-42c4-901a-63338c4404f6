package com.maersk.sd1.business.infraestructure.persistance.repositories;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Repository;

import com.maersk.sd1.business.core.planing.domain.Bloque;
import com.maersk.sd1.business.core.planing.domain.Celda;
import com.maersk.sd1.business.core.planing.domain.Contenedor;
import com.maersk.sd1.business.core.planing.domain.Nivel;
import com.maersk.sd1.business.core.planing.domain.RangoReglaPlanificacionPatio;
import com.maersk.sd1.business.core.planing.domain.Tipo;
import com.maersk.sd1.business.core.planing.domain.UbicacionContenedor;
import com.maersk.sd1.business.core.planing.domain.planificacion.DelimitedBlockRangeRequest;
import com.maersk.sd1.business.core.planing.port.BloqueRepository;
import com.maersk.sd1.business.infraestructure.shared.datamodel.rango_regla_planificacion_patio_entity;
import com.maersk.sd1.business.infraestructure.shared.datamodel.ubicacion_entity;
import com.maersk.sd1.business.infraestructure.shared.utils.GsonUtil;

import lombok.RequiredArgsConstructor;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.Procedure;

@Repository
@RequiredArgsConstructor
public class BloqueRepositoryImpl implements BloqueRepository {

	@Autowired
	private ApplicationContext context;

	public Bloque TraerPorId(int bloque_id) throws SQLException {
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.bloque_obtener");
		Bloque respuestaBusqueda = null;

		try {
			pResult.input("bloque_id", String.valueOf(bloque_id), Jpo.INTEGER);

			var resultado = pResult.execute();
			respuestaBusqueda = GsonUtil.GetModel(resultado, Bloque.class);
			
			conn.commit();
			
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		} 
		return respuestaBusqueda;
	}

	@Override
	public Bloque ObtenerCeldasNivelesPorLimites(int bloque_id, int row_index_from, int col_index_from,
			int row_index_to, int col_index_to) throws SQLException {

		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.buscar_rango_limites");
		Bloque respuestaBusqueda = null;

		try {
			pResult.input("bloque_id", String.valueOf(bloque_id), Jpo.INTEGER);
			pResult.input("row_index_from", String.valueOf(row_index_from), Jpo.INTEGER);
			pResult.input("col_index_from", String.valueOf(col_index_from), Jpo.INTEGER);
			pResult.input("row_index_to", String.valueOf(row_index_to), Jpo.INTEGER);
			pResult.input("col_index_to", String.valueOf(col_index_to), Jpo.INTEGER);

			var resultado = pResult.execute();
			Collection<rango_regla_planificacion_patio_entity> result = GsonUtil.GetList(resultado,
					rango_regla_planificacion_patio_entity.class);

			respuestaBusqueda = MapTo(result);
			
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return respuestaBusqueda;

	}

	// TODO: corregir para que se pueda retornar una lista de Celdas que podrian
	// pertenecer a varios bloques
	private Bloque MapTo(Collection<rango_regla_planificacion_patio_entity> contenedores) {
		int[] bloque_id = { 0 };
		int[] celda_id = { 0 };
		int[] nivel_id = { 0 };

		Bloque[] new_bloque = { new Bloque() };
		Celda[] new_celda = { new Celda() };
		Nivel[] new_nivel = { new Nivel() };

		contenedores.forEach(c -> {
			if (c.getBloque_id() != bloque_id[0]) {
				new_bloque[0] = new Bloque();
				new_bloque[0].setActivo(c.isActivo());
				new_bloque[0].setBloque_id(c.getBloque_id());
				new_bloque[0].setCat_bloque_codigo(c.getCat_bloque_codigo());
				new_bloque[0].setCat_bloque_id(c.getCat_bloque_id());
				new_bloque[0].setCodigo(c.getBloque_codigo());
				new_bloque[0].setColumnas(c.getColumnas());
				new_bloque[0].setConfiguracion("");
				new_bloque[0].setFilas(c.getFilas());
				new_bloque[0].setNiveles(c.getNiveles());
				new_bloque[0].setNombre(c.getNombre());
				new_bloque[0].setPatio_id(c.getPatio_id());
				new_bloque[0].setUnidad_negocio_id(c.getUnidad_negocio_id());
				new_bloque[0].setCeldas(new ArrayList<Celda>());

				bloque_id[0] = new_bloque[0].getBloque_id();
			}

			if (c.getCelda_id() != celda_id[0]) {
				new_celda[0] = new Celda();
				new_celda[0].setActivo(c.isActivo());

				new_celda[0].setBloque(new_bloque[0]);
				new_celda[0].setBloque_id(c.getBloque_id());
				new_celda[0].setIndice_columna(c.getIndice_columna());
				new_celda[0].setCantidad_contenedores(c.getCantidad_contenedores());
				new_celda[0].setBloqueado(c.isBloqueado());
				new_celda[0].setMax_dynamic_tier(5);
				new_celda[0].setIndice_fila(c.getIndice_fila());
				new_celda[0].setColumna(c.getColumna());
				new_celda[0].setFila(c.getFila());
				new_celda[0].setCelda_id(c.getCelda_id());
				new_celda[0].setName(c.getFila() + "-" + c.getColumna());
				new_celda[0].setNiveles(new ArrayList<Nivel>());

				new_bloque[0].getCeldas().add(new_celda[0]);

				// new_slot[0].Cost = 0;
				// new_slot[0].HasPickupInstruction = false;
				// new_slot[0].HasReeferPlug = false;
				// new_bloque[0].getSlots().add(new_slot[0]);

				celda_id[0] = new_celda[0].getCelda_id();
			}

			if (c.getNivel_id() != nivel_id[0]) {
				new_nivel[0] = new Nivel();
				new_nivel[0].setActivo(c.isActivo());
				new_nivel[0].setCelda_id(c.getCelda_id());
				new_nivel[0].setIndice_columna(c.getIndice_columna());
				new_nivel[0].setIndice(c.getIndice());
				new_nivel[0].setMax_dinamic_tier(5);
				new_nivel[0].setNivel_id(c.getNivel_id());
				new_nivel[0].setIndice_fila(c.getIndice_fila());

				new_nivel[0].setBloque_codigo(c.getBloque_codigo());
				new_nivel[0].setBloque_id(c.getBloque_id());
				new_nivel[0].setBloque_nombre(c.getNombre());
				// new_nivel[0].Color = "";
				new_nivel[0].setColumna(c.getColumna());
				new_nivel[0].setCelda_nombre(c.getFila() + "-" + c.getColumna());
				new_nivel[0].setFila(c.getFila());
				new_nivel[0].setPatio_code("");
				new_nivel[0].setPatio_id(c.getPatio_id());
				new_nivel[0].setPatio_name("");
				new_nivel[0].setContenedores(new ArrayList<UbicacionContenedor>());

				new_celda[0].getNiveles().add(new_nivel[0]);

				nivel_id[0] = new_nivel[0].getNivel_id();
			}

			if (c.getUbicacion_contenedor_id() != null) {
				var new_ubica = new UbicacionContenedor();
				new_ubica.setActivo(c.isActivo());
				new_ubica.setBloque(new Bloque());
				new_ubica.setCelda(null);

				new_ubica.setFecha_modificacion(c.getFecha_modificacion());
				new_ubica.setFecha_registro(c.getFecha_registro());
				new_ubica.setUbicacion_contenedor_id(c.getUbicacion_contenedor_id());
				new_ubica.setNivel(null);
				new_ubica.setPatio_id(c.getPatio_id());
				// new_ubica.setUsuario_modificacion_id(c.getUsuario_modificacion_id().doubleValue());
				new_ubica.setUsuario_registro_id(c.getUsuario_registro_id());

				if (c.getContenedor_id() != null) {
					var new_contenedor = new Contenedor(c.getContenedor());
					new_contenedor.setContenedor_id(c.getContenedor_id());
					new_contenedor.setCat_tamano_id(c.getCat_tamano_id());
					new_contenedor.setTamanio_contenedor(
							new Tipo(c.getCatalogo_id(), null, c.getDescripcion(), c.getDescricion_larga(), true));
					new_contenedor.setNumero_contenedor(c.getContenedor());
					new_contenedor.setCarga_maxima(c.getCarga_maxima());
					new_contenedor.setCat_clase_id(c.getCat_clase_id());
					new_contenedor.setCat_familia_id(c.getCat_familia_id());
					new_contenedor.setCat_marca_motor_id(c.getCat_marca_motor_id());
					new_contenedor.setCat_tipo_contenedor_id(c.getCat_tipo_contenedor_id());
					new_contenedor.setCat_tipo_reefer_id(c.getCat_tipo_reefer_id());
					new_contenedor.setCodigo_iso_id(c.getCodigo_iso_id());
					new_contenedor.setContenedor(c.getContenedor());
					new_contenedor.setFecha_fabricacion(c.getFecha_fabricacion());
					new_contenedor.setLinea_naviera_id(c.getLinea_naviera_id());
					new_contenedor.setShipper_own(c.isShipper_own());
					new_contenedor.setTara(c.getTara());

					new_ubica.setContenedor(new_contenedor);
					new_ubica.setContenedor_id(c.getContenedor_id());
					new_ubica.setContenedor_tamano(
							Integer.valueOf(new_contenedor.getTamanio_contenedor().getDescripcion()));
				}

				new_nivel[0].getContenedores().add(new_ubica);
			}
		});
		return new_bloque[0];
	}

	@Override
	public Bloque TraerPorCodigo(int patio_id, String bloque_codigo) throws SQLException {

		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.bloque_obtener_codigo");
		Bloque respuestaBusqueda = null;

		try {
			pResult.input("patio_id", String.valueOf(patio_id), Jpo.INTEGER);
			pResult.input("bloque_codigo", bloque_codigo, Jpo.STRING);

			var resultado = pResult.execute();
			respuestaBusqueda = GsonUtil.GetModel(resultado, Bloque.class);

			conn.commit();

		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		} 
		return respuestaBusqueda;
	}

	@Override
	public UbicacionContenedor ObtenerUbicacionVirtual(String bloque_codigo, int patio_id) throws SQLException {

		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.bloque_obtener_virtual");
		UbicacionContenedor respuestaBusqueda = null;

		try {
			pResult.input("codigo", bloque_codigo, Jpo.STRING);
			pResult.input("patio_id", String.valueOf(patio_id), Jpo.INTEGER);

			var resultado = pResult.execute();
			ubicacion_entity result = GsonUtil.GetModel(resultado, ubicacion_entity.class);
			respuestaBusqueda = MapToUbicacionContenedor(result);
			conn.commit();

		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return respuestaBusqueda;
	}

	private UbicacionContenedor MapToUbicacionContenedor(ubicacion_entity ubicacion) {
		var base = new UbicacionContenedor(
				new Bloque(ubicacion.getBloque_id(), ubicacion.getPatio_id(), ubicacion.getCat_bloque_id(),
						ubicacion.getUnidad_negocio_id(), ubicacion.getCodigo(), ubicacion.getNombre(),
						ubicacion.getFilas(), ubicacion.getColumnas(), ubicacion.getNiveles(), "", ubicacion.isActivo(),
						ubicacion.getBloque_cantidad_contenedores(), ubicacion.getBloque_tipo_codigo()),
				new Celda(ubicacion.getCelda_id(), ubicacion.getBloque_id(), ubicacion.getFila(),
						ubicacion.getColumna(), ubicacion.getIndice_fila(), ubicacion.getIndice_columna(),
						ubicacion.isBloqueado()),
				new Nivel(ubicacion.getNivel_id(), ubicacion.getCelda_id(), ubicacion.getIndice()));
		base.setActivo(true);
		base.setBloque_id(ubicacion.getBloque_id());
		base.setCelda_id(ubicacion.getCelda_id());
		base.setCelda_name(ubicacion.getFila() + "-" + ubicacion.getColumna());
		base.setColumna(ubicacion.getColumna());
		base.setFila(ubicacion.getFila());
		base.setIndice_columna(ubicacion.getIndice_columna());
		base.setIndice_fila(ubicacion.getIndice_fila());
		base.setNivel_id(ubicacion.getNivel_id());
		base.setPatio_id(ubicacion.getPatio_id());
		base.setUbicacion_contenedor_id(ubicacion.getUbicacion_contenedor_id());
		base.setContenedor_id(ubicacion.getContenedor_id());

		return base;
	}

	
	
	
	@Override
	public List<RangoReglaPlanificacionPatio> getPositionsByRangeInABlock(List<DelimitedBlockRangeRequest> listDelimitedBlockRange) throws SQLException {

		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.ubicacion_contenedor_list_positions_by_bange_in_a_block");
		List<RangoReglaPlanificacionPatio> response = new ArrayList<RangoReglaPlanificacionPatio>();

		try {
			String listDelimitedBlockRangeJson = GsonUtil.ConvertToJSON(listDelimitedBlockRange);
			pResult.input("list_delimited_block_range_json", listDelimitedBlockRangeJson, Jpo.STRING);			
			var resultado = pResult.execute();
			Collection<rango_regla_planificacion_patio_entity> result = GsonUtil.GetList(resultado, rango_regla_planificacion_patio_entity.class);

			response = MapToList(result, listDelimitedBlockRange);

		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return response;

	}
	
private List<RangoReglaPlanificacionPatio> MapToList(Collection<rango_regla_planificacion_patio_entity> ranges_bloque_celdas_nivel, List<DelimitedBlockRangeRequest> delimitedBlockRangeRequest) {
		
		List<RangoReglaPlanificacionPatio> response = new ArrayList<RangoReglaPlanificacionPatio>();
		
		delimitedBlockRangeRequest.forEach(delimitedBlockRange -> {
			
			List<rango_regla_planificacion_patio_entity> listBlockCellLevelByRange = ranges_bloque_celdas_nivel.stream()
					.filter(item -> item.getRango_regla_planificacion_patio_id() == delimitedBlockRange.getRango_regla_planificacion_patio_id())
					.collect(Collectors.toList());
			
			if(listBlockCellLevelByRange.size() > 0) {
				
				var firstBloqueCeldaNivelByRango = listBlockCellLevelByRange.stream().findFirst().get(); 
				var block = new Bloque();
				block.setActivo(firstBloqueCeldaNivelByRango.isActivo());
				block.setBloque_id(firstBloqueCeldaNivelByRango.getBloque_id());
				block.setCat_bloque_codigo(firstBloqueCeldaNivelByRango.getCat_bloque_codigo());
				block.setCat_bloque_id(firstBloqueCeldaNivelByRango.getCat_bloque_id());
				block.setCodigo(firstBloqueCeldaNivelByRango.getBloque_codigo());
				block.setColumnas(firstBloqueCeldaNivelByRango.getColumnas());
				block.setConfiguracion("");
				block.setFilas(firstBloqueCeldaNivelByRango.getFilas());
				block.setNiveles(firstBloqueCeldaNivelByRango.getNiveles());
				block.setNombre(firstBloqueCeldaNivelByRango.getNombre());
				block.setPatio_id(firstBloqueCeldaNivelByRango.getPatio_id());
				block.setUnidad_negocio_id(firstBloqueCeldaNivelByRango.getUnidad_negocio_id());
				block.setCeldas(new ArrayList<Celda>());

				var celdas = listBlockCellLevelByRange.stream().filter(distinctByKeys(rango_regla_planificacion_patio_entity::getCelda_id)).collect(Collectors.toList());					
				celdas.forEach(rangoBloqueCeldaNivel -> {

					var cell = new Celda();
					cell.setCelda_id(rangoBloqueCeldaNivel.getCelda_id());
					cell.setActivo(rangoBloqueCeldaNivel.isActivo());
					cell.setBloque(block);
					cell.setBloque_id(rangoBloqueCeldaNivel.getBloque_id());
					cell.setIndice_columna(rangoBloqueCeldaNivel.getIndice_columna());
					cell.setCantidad_contenedores(rangoBloqueCeldaNivel.getCantidad_contenedores());
					cell.setBloqueado(rangoBloqueCeldaNivel.isBloqueado());
					cell.setMax_dynamic_tier(5);
					cell.setIndice_fila(rangoBloqueCeldaNivel.getIndice_fila());
					cell.setColumna(rangoBloqueCeldaNivel.getColumna());
					cell.setFila(rangoBloqueCeldaNivel.getFila());
					cell.setName(rangoBloqueCeldaNivel.getFila() + "-" + rangoBloqueCeldaNivel.getColumna());
					cell.setNiveles(new ArrayList<Nivel>());
					block.getCeldas().add(cell);

					
					List<rango_regla_planificacion_patio_entity> levelsByCell = ranges_bloque_celdas_nivel.stream()
							.filter(item -> item.getCelda_id() == cell.getCelda_id())
							.collect(Collectors.toList());
					
					levelsByCell.forEach(levelByCell -> {
						var level = new Nivel();
						level.setActivo(levelByCell.isActivo());
						level.setCelda_id(levelByCell.getCelda_id());
						level.setIndice_columna(levelByCell.getIndice_columna());
						level.setIndice(levelByCell.getIndice());
						level.setMax_dinamic_tier(5);
						level.setNivel_id(levelByCell.getNivel_id());
						level.setIndice_fila(levelByCell.getIndice_fila());
						level.setBloque_codigo(levelByCell.getBloque_codigo());
						level.setBloque_id(levelByCell.getBloque_id());
						level.setBloque_nombre(levelByCell.getNombre());
						level.setColumna(levelByCell.getColumna());
						level.setCelda_nombre(levelByCell.getFila() + "-" + levelByCell.getColumna());
						level.setFila(levelByCell.getFila());
						level.setPatio_code("");
						level.setPatio_id(levelByCell.getPatio_id());
						level.setPatio_name("");
						level.setContenedores(new ArrayList<UbicacionContenedor>());
						cell.getNiveles().add(level);
						

						if (levelByCell.getUbicacion_contenedor_id() != null) {
							var new_ubica = new UbicacionContenedor();
							new_ubica.setUbicacion_contenedor_id(levelByCell.getUbicacion_contenedor_id());
							new_ubica.setActivo(levelByCell.isActivo());
							new_ubica.setFecha_modificacion(levelByCell.getFecha_modificacion());
							new_ubica.setFecha_registro(levelByCell.getFecha_registro());							
							new_ubica.setNivel(null);
							new_ubica.setPatio_id(levelByCell.getPatio_id());
							new_ubica.setUsuario_registro_id(levelByCell.getUsuario_registro_id());
							new_ubica.setBloque(new Bloque());
							new_ubica.setCelda(null);
							
							if (levelByCell.getContenedor_id() != null) {
								var new_contenedor = new Contenedor(levelByCell.getContenedor());
								new_contenedor.setContenedor_id(levelByCell.getContenedor_id());
								new_contenedor.setCat_tamano_id(levelByCell.getCat_tamano_id());
								
								new_contenedor.setTamanio_contenedor(new Tipo(levelByCell.getCatalogo_id(), null, levelByCell.getDescripcion(), levelByCell.getDescricion_larga(), true));
								
								new_contenedor.setNumero_contenedor(levelByCell.getContenedor());
								new_contenedor.setCarga_maxima(levelByCell.getCarga_maxima());
								new_contenedor.setCat_clase_id(levelByCell.getCat_clase_id());
								new_contenedor.setCat_familia_id(levelByCell.getCat_familia_id());
								new_contenedor.setCat_marca_motor_id(levelByCell.getCat_marca_motor_id());
								new_contenedor.setCat_tipo_contenedor_id(levelByCell.getCat_tipo_contenedor_id());
								new_contenedor.setCat_tipo_reefer_id(levelByCell.getCat_tipo_reefer_id());
								new_contenedor.setCodigo_iso_id(levelByCell.getCodigo_iso_id());
								new_contenedor.setContenedor(levelByCell.getContenedor());
								new_contenedor.setFecha_fabricacion(levelByCell.getFecha_fabricacion());
								new_contenedor.setLinea_naviera_id(levelByCell.getLinea_naviera_id());
								new_contenedor.setShipper_own(levelByCell.isShipper_own());
								new_contenedor.setTara(levelByCell.getTara());
								
								new_ubica.setContenedor(new_contenedor);								
								new_ubica.setContenedor_id(levelByCell.getContenedor_id());
								new_ubica.setContenedor_tamano(Integer.valueOf(new_contenedor.getTamanio_contenedor().getDescripcion()));
							}

							level.getContenedores().add(new_ubica);
							
						}
					});
				});	
				var newRangoRegle = new RangoReglaPlanificacionPatio();
				newRangoRegle.setRango_regla_planificacion_patio_id(delimitedBlockRange.getRango_regla_planificacion_patio_id());
				newRangoRegle.setBloque(block);
				response.add(newRangoRegle);
			}			
		});
		
		return response;
	}
	
	@SafeVarargs
	private static <T> Predicate<T> distinctByKeys(Function<? super T, ?>... keyExtractors) 
	{
	    final Map<List<?>, Boolean> seen = new ConcurrentHashMap<>();	     
	    return t -> 
	    {
	      final List<?> keys = Arrays.stream(keyExtractors)
	                  .map(ke -> ke.apply(t))
	                  .collect(Collectors.toList());
	                  //.collect(Collectors.toList());
	       
	      return seen.putIfAbsent(keys, Boolean.TRUE) == null;
	    };
	}
	
}
