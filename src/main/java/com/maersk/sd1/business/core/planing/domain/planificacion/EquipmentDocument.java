package com.maersk.sd1.business.core.planing.domain.planificacion;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class EquipmentDocument 
{
	@ApiModelProperty(notes = "Document", required = true) 
	private PlanningDocument document;
	
	@ApiModelProperty(notes = "Equipment", required = true) 
	private PlanningEquipment equipment;
}
