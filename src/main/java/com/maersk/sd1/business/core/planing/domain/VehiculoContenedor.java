package com.maersk.sd1.business.core.planing.domain;

import java.util.Collection;

import com.maersk.sd1.business.core.planing.domain.planificacion.VisitaContenedor;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor

public class VehiculoContenedor {
	private String placa_vehiculo;
	private Collection<VisitaContenedor> contenedores;
	
	//private Collection<Contenedor> contenedores;
	private Vehiculo vehiculo;
}
