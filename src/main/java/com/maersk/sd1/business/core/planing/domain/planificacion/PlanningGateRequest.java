package com.maersk.sd1.business.core.planing.domain.planificacion;

import java.util.Collection;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@Builder
@NoArgsConstructor
public class PlanningGateRequest {
	@ApiModelProperty(notes = "Yard Code", required = true) 
	private String yard_code;
	
	@ApiModelProperty(notes = "User Alias", required = true) 	
	private String user_alias;

	@ApiModelProperty(notes = "Sub Business Unit Alias", required = true) 
	private String sub_business_unit_alias;
	
	@ApiModelProperty(notes = "Operation Type", required = true) 
	private String operation_type;

	@ApiModelProperty(notes = "Process realized", required = true) 
	private String process_realized;	

	@ApiModelProperty(notes = "Additional Data", required = true) 
	private Collection<EquipmentDocument> aditional_data;
}
