package com.maersk.sd1.business.core.planing.domain.planificacion;

import java.util.Collection;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@Builder
@NoArgsConstructor
public class PlanningGateInRequest {
	@ApiModelProperty(notes = "Yard Code", required = true)
	private String patio_codigo;

	@ApiModelProperty(notes = "User ID", required = false)
	private int usuario_id;
	@ApiModelProperty(notes = "Business Unit ID", required = false)
	private int unidad_negocio_id;
	@ApiModelProperty(notes = "Business Unit Father ID", required = false)
	private int unidad_negocio_padre_id;

	@ApiModelProperty(notes = "User Alias", required = true)
	private String usuario_alias;
	@ApiModelProperty(notes = "Sub Business Unit Alias", required = true)
	private String sub_unidad_negocio_alias;
	@ApiModelProperty(notes = "Operation Type", required = true)
	private String tipo_operacion;
	@ApiModelProperty(notes = "Process realized", required = true)
	private String proceso_realizado;

	@ApiModelProperty(notes = "Containers", required = false)
	private Collection<VisitaContenedorIngresoRequest> contenedores;
	@ApiModelProperty(notes = "Additional Data", required = true)
	private Collection<EquipmentDocument> aditional_data;
}
