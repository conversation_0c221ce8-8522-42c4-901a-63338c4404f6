package com.maersk.sd1.business.core.planing.usecase.operacion;

import java.util.List;

import com.maersk.sd1.business.core.planing.domain.planificacion.PlanificacionRequestCommand;
import com.maersk.sd1.business.core.planing.domain.planificacion.PlanificationRelocationManualResponse;

public interface ProcesoActualizacionUbicacionUseCase {
	PlanificacionRequestCommand execute(PlanificacionRequestCommand request) throws Exception;
	
	List<PlanificationRelocationManualResponse> executePlanning(PlanificacionRequestCommand request) throws Exception;

}
