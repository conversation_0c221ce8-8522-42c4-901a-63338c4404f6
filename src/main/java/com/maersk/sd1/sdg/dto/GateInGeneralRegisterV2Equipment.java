package com.maersk.sd1.sdg.dto;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.ArrayList;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GateInGeneralRegisterV2Equipment {

    @SerializedName("programacion_nave_detalle_id")
    private Integer programacionNaveDetalleId;

    @SerializedName("documento_carga_detalle_id")
    private Integer documentoCargaDetalleId;

    @SerializedName("planning_detail_id")
    private Integer planningDetailId;

    @SerializedName("equipment_id")
    private Integer equipmentId;

    @SerializedName("equipment_number")
    private String equipmentNumber;

    @SerializedName("iso_code_id")
    private Integer isoCodeId;

    @SerializedName("manifest_weight")
    private BigDecimal manifestWeight;

    @SerializedName("manifest_weight_unit_id")
    private Integer manifestWeightUnitId;

    @SerializedName("temperature")
    private String temperature;

    @SerializedName("temperature_unit_id")
    private Integer temperatureUnitId;

    @SerializedName("seal_1")
    private String seal1;

    @SerializedName("seal_2")
    private String seal2;

    @SerializedName("seal_3")
    private String seal3;

    @SerializedName("seal_4")
    private String seal4;

    @SerializedName("reefer_connect")
    private Boolean reeferConnect;

    @SerializedName("reefer_dangerous")
    private Boolean reeferDangerous;

    @SerializedName("description_dangerous_substance")
    private String descriptionDangerousSubstance;

    @SerializedName("eir_id")
    private Integer eirId;

    @SerializedName("equipment_message")
    private String equipmentMessage;

    @SerializedName("pictures")
    private Object adjuntos;

    @SerializedName("aps_id")
    private Integer apsId;

    @SerializedName("eir_chassis_id")
    private Integer eirChassisId;

    @SerializedName("chassis_number")
    private String chassisNumber;

    @JsonAdapter(StringToBooleanDeserializer.class)
    @SerializedName("no_maersk")
    private Boolean noMaersk;

    @SerializedName("imos")
    private ArrayList<GateInGeneralRegisterV2Imo> imos;
}

class StringToBooleanDeserializer implements JsonDeserializer<Boolean> {
    @Override
    public Boolean deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        String value = json.getAsString();
        return value.equals("1");
    }
}
