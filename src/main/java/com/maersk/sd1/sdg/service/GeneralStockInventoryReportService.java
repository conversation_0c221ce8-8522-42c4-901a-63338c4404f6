package com.maersk.sd1.sdg.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.service.BusinessUnitConfigService;
import com.maersk.sd1.sdg.controller.dto.GeneralStockInventoryInput;
import com.maersk.sd1.sdg.controller.dto.StockInventoryReportOutputDTO;
import com.maersk.sd1.sdg.dto.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.maersk.sd1.common.Parameter.*;

@Service
@RequiredArgsConstructor
public class GeneralStockInventoryReportService {

    private final  BusinessUnitRepository businessUnitRepository;

    private  final BusinessUnitConfigService businessUnitConfigService;

    private final  CatalogRepository catalogRepository;

    private final  ContainerRepository containerRepository;

    private final  FgisInspectionRepository fgisInspectionRepository;

    private final  StockFullRepository stockFullRepository;

    private final  EmrInspectionRepository emrInspectionRepository;

    private final  StockEmptyRepository stockEmptyRepository;

    private final  EstimateEmrRepository estimateEmrRepository;

    private final  EirActivityZoneRepository eirActivityZoneRepository;

    private final  ContainerPreassignmentRepository containerPreassignmentRepository;

    private final  CargoDocumentDetailRepository cargoDocumentDetailRepository;

    private final  BookingDetailRepository bookingDetailRepository;

    private final  BookingRepository bookingRepository;

    private final  EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;

    private final  ContainerRestrictionDetailRepository containerRestrictionDetailRepository;

    private final  ContainerRestrictionRepository containerRestrictionRepository;

    private final  VesselProgrammingContainerImoRepository vesselProgrammingContainerImoRepository;

    private final  VesselProgrammingContainerRepository vesselProgrammingContainerRepository;

    private final  StockChassisRepository stockChassisRepository;

    private final  EirChassisZoneActivityRepository eirChassisZoneActivityRepository;

    private final  ChassisEstimateRepository chassisEstimateRepository;

    private final  ChassisRestrictionRepository chassisRestrictionRepository;

    private final  CompanyRepository companyRepository;

    private final  CatalogLanguageRepository catalogLanguageRepository;

    private final  IsoCodeRepository isoCodeRepository;

    private final  ShippingLineRepository shippingLineRepository;

    private final  ProductRepository productRepository;

    private final ContainerLocationRepository containerLocationRepository;

    @Transactional
    public StockInventoryReportOutputDTO getGeneralStockInventoryReport(GeneralStockInventoryInput.Root inputRoot) {

        Integer localSubBusinessUnitId = inputRoot.getInput().getSubBusinessUnitId();
        Integer languageId = inputRoot.getInput().getLanguageId();
        String equipmentNumber = inputRoot.getInput().getEquipmentNumber();
        Integer equipmentCategory = inputRoot.getInput().getEquipmentCategory();
        Integer emptyFullId = inputRoot.getInput().getEmptyFullId();
        Integer containerSizeId = inputRoot.getInput().getContainerSizeId();
        Integer containerTypeId = inputRoot.getInput().getContainerTypeId();
        Integer containerGradeId = inputRoot.getInput().getContainerGradeId();
        Integer containerShippingLineId = inputRoot.getInput().getContainerShippingLineId();
        Integer chassisOwnerId = inputRoot.getInput().getChassisOwnerId();
        Integer chassisTypeId = inputRoot.getInput().getChassisTypeId();
        Integer shipperName = inputRoot.getInput().getShipperName();
        String nonMaerskFlag = inputRoot.getInput().getNonMaerskFlag();
        Integer page = inputRoot.getInput().getPage();
        Integer recordCount = inputRoot.getInput().getRecordCount();

        Integer subBusinessUnitId = businessUnitRepository.findParentBusinessUnitId(localSubBusinessUnitId);
        Integer businessUnitId = businessUnitRepository.findParentBusinessUnitId(subBusinessUnitId);
        String dateTimeFormat = businessUnitConfigService.getDateTimeFormat(businessUnitId);
        List<String> aliases = getCatalogAliases();
        Map<String, Integer> catalogMap = catalogRepository.findIdsByAliases(aliases).stream()
                .collect(Collectors.toMap(
                        obj -> (String) obj[0],
                        obj -> (Integer) obj[1]
                ));
        Integer isGateOut = catalogMap.get(CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS);
        Integer isFull = catalogMap.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS);
        Integer isEmpty = catalogMap.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS);
        Integer isChassis = catalogMap.get(EQUIPMENT_CATEGORY_CHASSIS);
        Integer isContainer = catalogMap.get(EQUIPMENT_CATEGORY_CONTAINER);
        Integer isMtyActivityInsp = catalogMap.get(CATALOG_BOX_INSPECTION_ALIAS);
        Integer isChassisActivityInsp = catalogMap.get(CATALOG_CHASSIS_ZONE_ACTIVITY_INSPECTION_ALIAS);
        Integer isInsepectionPending = catalogMap.get(INSPECTION_GRAL_STATUS_PENDING);
        Integer isInsepectionStarted = catalogMap.get(INSPECTION_GRAL_STATUS_STARTED);
        Integer isInsepectionFinished = catalogMap.get(INSPECTION_GRAL_STATUS_FINISHED);
        Integer isEstimateTypeStructure = catalogMap.get(CATALOG_ESTIMATE_BOX_ALIAS);
        Integer isEstimateTypeMachinery = catalogMap.get(CATALOG_ESTIMATE_TYPE_MACHINERY);

        if (equipmentCategory == null &&
                (emptyFullId != null || containerGradeId != null || containerShippingLineId != null ||
                        containerSizeId != null || containerTypeId != null)) {
            equipmentCategory = isContainer;
        }

        if (equipmentCategory == null &&
                (chassisOwnerId != null || chassisTypeId != null)) {
            equipmentCategory = isChassis;
        }

        if ((equipmentCategory == null ? 0 : equipmentCategory) != isContainer &&
                (emptyFullId != null || containerGradeId != null || containerShippingLineId != null ||
                        containerSizeId != null || containerTypeId != null)) {
            equipmentCategory = -1;
        }

        if ((equipmentCategory == null ? 0 : equipmentCategory) != isChassis &&
                (equipmentCategory == null ? 0 : equipmentCategory) != -1 &&
                (chassisOwnerId != null || chassisTypeId != null)) {
            equipmentCategory = -1;
        }

        if (equipmentCategory == null &&
                (emptyFullId != null || containerGradeId != null || containerShippingLineId != null ||
                        chassisOwnerId != null || containerSizeId != null || containerTypeId != null ||
                        chassisTypeId != null)) {
            equipmentCategory = -1;
        }

        Integer equipmentPendingId = containerRepository.findIdByContainerNumber(CONTAINER_NO_CNT);
        Integer equipmentNotApplicableId = containerRepository.findIdByContainerNumber(CONTAINER_NOT_APPLICA);
        List<StockDataDTO> stockDataDtosImpl = new ArrayList<>();
        fgisInspectionRepository.updateFgisInspectionWithStockAndEir(86, 293972);

        if (equipmentCategory == null || Objects.equals(equipmentCategory, isContainer)) {
            AtomicInteger atomicId = new AtomicInteger(1);
            stockDataDtosImpl = extractFullContainerStockData(subBusinessUnitId, emptyFullId, containerSizeId, containerTypeId, containerGradeId, containerShippingLineId, nonMaerskFlag, equipmentNumber, equipmentPendingId, equipmentNotApplicableId, isContainer);
            if (!CollectionUtils.isEmpty(stockDataDtosImpl)) {
                updateStockDataStatus(stockDataDtosImpl, isFull, isInsepectionFinished, isContainer);
            }
            List<StockDataDTO> stockEmptyContainerDataDTOS = extractEmptyContainerStockData(subBusinessUnitId, emptyFullId, containerSizeId, containerTypeId, containerGradeId, containerShippingLineId, nonMaerskFlag, equipmentNumber, equipmentPendingId, equipmentNotApplicableId, isContainer);
            if (!CollectionUtils.isEmpty(stockEmptyContainerDataDTOS)) {
                stockDataDtosImpl.addAll(stockEmptyContainerDataDTOS);
            }
            for (StockDataDTO stockDataDTO : stockDataDtosImpl) {
                stockDataDTO.setId(atomicId.getAndIncrement());
            }
            updateEmptyContainerStockDataDtos(stockDataDtosImpl, isEstimateTypeStructure, isEmpty, isContainer, Boolean.FALSE);
            updateEmptyContainerStockDataDtos(stockDataDtosImpl, isEstimateTypeMachinery, isEmpty, isContainer, Boolean.TRUE);
            updateInspectionDetailsInStockData(stockDataDtosImpl, isMtyActivityInsp, isInsepectionFinished,
                    isInsepectionStarted, isInsepectionPending, isEmpty, isContainer);
            List<FgisDataDTO> fgisData = generateFgisData(stockDataDtosImpl, isEmpty, isContainer);
            updateUsdaApproval(stockDataDtosImpl, fgisData);
            List<PreallocationDTO> preallocationDTOS = generatePreallocation(stockDataDtosImpl, isEmpty, isContainer);
            if (!CollectionUtils.isEmpty(preallocationDTOS)) {
                List<PreallocationDTO> updatedPreallocationDtos = processPreallocation(preallocationDTOS, isGateOut, isEmpty);
                if (!CollectionUtils.isEmpty(updatedPreallocationDtos)) {
                    updateBookingPreAllocation(stockDataDtosImpl, updatedPreallocationDtos);
                }
            }
        }
        updateShipperData(stockDataDtosImpl, shipperName);
        List<RestrictionDTO> restrictionDTOS = processRestrictions(stockDataDtosImpl);
        List<IMODetailsDTO> imoDetailsDTOS = processIMOData(stockDataDtosImpl);
        List<ChassisRestrictionDTO> chassisRestrictionDTOS = new ArrayList<>();
        if (Objects.equals(equipmentCategory, isChassis) || equipmentCategory == null) {
            List<StockDataDTO> stockDataList = processStockData(subBusinessUnitId, chassisOwnerId, chassisTypeId,
                    equipmentNumber, shipperName, nonMaerskFlag, isChassis);
            stockDataDtosImpl.addAll(stockDataList);
            updateStatusInspection(stockDataDtosImpl, isInsepectionFinished, isInsepectionStarted, isInsepectionPending,
                    isChassisActivityInsp, isChassis);
            updateChassisEstimateStockDataDtos(stockDataDtosImpl, isChassis);
            chassisRestrictionDTOS = processChassisRestrictions(stockDataDtosImpl, isChassis);
        }
        processShipperFilter(stockDataDtosImpl, shipperName);
        int totalRecord = CollectionUtils.isEmpty(stockDataDtosImpl) ? 0 : stockDataDtosImpl.size();
        List<StockInventoryReportOutputDTO.StockInventory> stockInventories = processReport(stockDataDtosImpl, isContainer, languageId, isChassis, subBusinessUnitId,
                dateTimeFormat, isEmpty, isFull, restrictionDTOS, chassisRestrictionDTOS, imoDetailsDTOS);
        List<StockInventoryReportOutputDTO.StockInventory> outputData = getPaginatedResult(stockInventories, page, recordCount, totalRecord);
        return StockInventoryReportOutputDTO.builder()
                .totalCount(List.of(Map.of("totalCount", totalRecord)))
                .dataList(outputData)
                .build();
    }

    private List<String> getCatalogAliases() {
        return List.of(CATALOG_TYPE_GATE_IS_GATEIN_ALIAS, CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS, CATALOG_TYPE_PROCESS_IS_FULL_ALIAS,
                CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS, EQUIPMENT_CATEGORY_CHASSIS, EQUIPMENT_CATEGORY_CONTAINER, EQUIPMENT_CATEGORY_GENSET,
                EQUIPMENT_CATEGORY_UNDERSLUNG, CATALOG_MEASURE_WEIGHT_KG_ALIAS, CATALOG_BOX_INSPECTION_ALIAS,
                CATALOG_CHASSIS_ZONE_ACTIVITY_INSPECTION_ALIAS, INSPECTION_GRAL_STATUS_PENDING, INSPECTION_GRAL_STATUS_STARTED,
                INSPECTION_GRAL_STATUS_FINISHED, CATALOG_ESTIMATE_BOX_ALIAS, CATALOG_ESTIMATE_TYPE_MACHINERY);
    }

    private List<StockDataDTO> extractFullContainerStockData(Integer subBusinessUnitId, Integer emptyFullId, Integer containerSizeId,
                                                             Integer containerTypeId, Integer containerGradeId,
                                                             Integer containerShippingLineId, String nonMaerskFlag,
                                                             String equipmentNumber, Integer equipmentPendingId,
                                                             Integer equipmentNotApplicableId, Integer isContainer) {
        List<Object[]> results = stockFullRepository.fetchFullContainerStockData(subBusinessUnitId, emptyFullId,
                containerSizeId, containerTypeId, containerGradeId, containerShippingLineId, nonMaerskFlag, equipmentNumber,
                equipmentPendingId, equipmentNotApplicableId, isContainer);
        return results.stream()
                .map(this::buildStockDataDTO)
                .collect(Collectors.toCollection(ArrayList::new));
    }

    private void updateStockDataStatus(List<StockDataDTO> stockDataDTOS, Integer isFull, Integer isInspectionFinished, Integer isContainer) {
        List<Integer> eirIds = stockDataDTOS.stream()
                .map(StockDataDTO::getEirId)
                .toList();

        List<EmrInspection> inspections = emrInspectionRepository.findActiveInspectionsByEirIds(eirIds);

        Map<Integer, Integer> inspectionStatusMap = new HashMap<>();
        for (EmrInspection inspection : inspections) {
            inspectionStatusMap.put(inspection.getEir().getId(), isInspectionFinished);
        }


        stockDataDTOS.stream()
                .filter(data -> data.getCatEmptyFullId() != null && data.getCatEmptyFullId().equals(isFull))
                .filter(data -> data.getEquipmentCategory() != null && data.getEquipmentCategory().equals(isContainer))
                .forEach(data -> {
                    if (inspectionStatusMap.containsKey(data.getEirId())) {
                        data.setInspectionStatus(inspectionStatusMap.get(data.getEirId()));
                    }
                });
    }

    private List<StockDataDTO> extractEmptyContainerStockData(Integer subBusinessUnitId, Integer emptyFullId,
                                                              Integer containerSizeId, Integer containerTypeId,
                                                              Integer containerGradeId, Integer containerShippingLineId,
                                                              String nonMaerskFlag, String equipmentNumber,
                                                              Integer equipmentPendingId, Integer equipmentNotApplicableId,
                                                              Integer isContainer) {
        List<Object[]> results = stockEmptyRepository.fetchEmptyContainerStockData(subBusinessUnitId, emptyFullId,
                containerSizeId, containerTypeId, containerGradeId, containerShippingLineId, nonMaerskFlag, equipmentNumber,
                equipmentPendingId, equipmentNotApplicableId, isContainer);
        return results.stream()
                .map(this::buildStockDataDTO)
                .toList();
    }

    private void updateEmptyContainerStockDataDtos(
            List<StockDataDTO> stockDataDTOS,
            Integer estimateTypeId,
            Integer isEmpty,
            Integer isContainer,
            boolean isMachinery) {
        if (stockDataDTOS == null || stockDataDTOS.isEmpty()) {
            return;
        }

        List<StockDataDTO> filteredDtos = stockDataDTOS.stream()
                .filter(dto -> Objects.equals(dto.getCatEmptyFullId(), isEmpty) && Objects.equals(dto.getEquipmentCategory(), isContainer))
                .toList();

        if (filteredDtos.isEmpty()) {
            return;
        }

        Set<Integer> eirIds = filteredDtos.stream()
                .map(StockDataDTO::getEirId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (eirIds.isEmpty()) {
            return;
        }

        List<EstimateEmr> estimates = estimateEmrRepository.findLatestEstimatesForEirIds(eirIds, estimateTypeId);

        Map<Integer, EstimateEmr> estimateMap = estimates.stream()
                .collect(Collectors.toMap(e -> e.getEir().getId(), estimate -> estimate));

        filteredDtos.forEach(dto -> {
            EstimateEmr estimate = estimateMap.get(dto.getEirId());
            if (estimate != null) {
                if (isMachinery) {
                    dto.setEstimateMachineNumber(estimate.getId());
                    dto.setEstimateMachineStatus(estimate.getCatEstimateStatus().getId());
                } else {
                    dto.setEstimateStructureNumber(estimate.getId());
                    dto.setEstimateStructureStatus(estimate.getCatEstimateStatus().getId());
                }
            }
        });
    }

    private void updateInspectionDetailsInStockData(
            List<StockDataDTO> stockDataDTOS,
            Integer activityZoneId,
            Integer isInspectionFinished,
            Integer isInspectionStarted,
            Integer isInspectionPending,
            Integer isEmpty,
            Integer isContainer) {
        if (stockDataDTOS == null || stockDataDTOS.isEmpty()) {
            return;
        }

        List<StockDataDTO> filteredDtos = stockDataDTOS.stream()
                .filter(dto -> Objects.equals(dto.getCatEmptyFullId(), isEmpty) && Objects.equals(dto.getEquipmentCategory(), isContainer))
                .toList();

        if (filteredDtos.isEmpty()) {
            return;
        }

        Set<Integer> eirIds = filteredDtos.stream()
                .map(StockDataDTO::getEirId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (eirIds.isEmpty()) {
            return;
        }

        List<EirInspectionDataDTO> inspectionData = eirActivityZoneRepository.findInspectionDataForEirs(
                eirIds,
                activityZoneId,
                isInspectionFinished,
                isInspectionStarted,
                isInspectionPending
        );

        Map<Integer, EirInspectionDataDTO> inspectionDataMap = inspectionData.stream()
                .collect(Collectors.toMap(EirInspectionDataDTO::getEirId, dto -> dto));

        filteredDtos.forEach(dto -> {
            EirInspectionDataDTO inspection = inspectionDataMap.get(dto.getEirId());
            if (inspection != null) {
                dto.setInspectorComment(inspection.getInspectorComment());
                dto.setPotentialFoodAid(inspection.getPotentialFoodAid());
                dto.setInspectionStatus(inspection.getStatusInspection());
            }
        });
    }

    private List<FgisDataDTO> generateFgisData(List<StockDataDTO> stockDataDTOS, Integer isEmpty, Integer isContainer) {
        List<FgisInspection> allInspections = fgisInspectionRepository.findApprovedAndActiveInspections();

        List<FgisDataDTO> fgisDataDTOList = stockDataDTOS.stream()
                .flatMap(stock -> allInspections.stream()
                        .filter(fgis -> Objects.equals(fgis.getGateInEir().getId(), stock.getEirId()) &&
                                Objects.equals(stock.getCatEmptyFullId(), isEmpty) &&
                                Objects.equals(stock.getEquipmentCategory(), isContainer))
                        .map(fgis -> new FgisDataDTO(
                                fgis.getFgisDate(),
                                fgis.getGateInEir().getId(),
                                fgis.getContainer().getId(),
                                calculateUsdaApproved(fgis.getUsdaApproved())
                        ))
                )
                .toList();

        Map<List<Integer>, FgisDataDTO> groupedData = fgisDataDTOList.stream()
                .collect(Collectors.toMap(
                        fgis -> List.of(fgis.getGateInEirId(), fgis.getContainerId()),
                        fgis -> fgis,
                        (existing, replacement) ->
                                existing.getFgisDate().isAfter(replacement.getFgisDate()) ? existing : replacement
                ));

        return new ArrayList<>(groupedData.values());
    }

    private void updateUsdaApproval(List<StockDataDTO> stockDataDTOS, List<FgisDataDTO> fgisDataDTOS) {
        if (stockDataDTOS == null || stockDataDTOS.isEmpty() || fgisDataDTOS == null || fgisDataDTOS.isEmpty()) {
            return;
        }

        Map<Integer, FgisDataDTO> fgisMap = fgisDataDTOS.stream()
                .collect(Collectors.toMap(FgisDataDTO::getContainerId, fgisDTO -> fgisDTO));

        for (StockDataDTO dto : stockDataDTOS) {
            if (dto.getContainerId() == null) {
                continue;
            }

            FgisDataDTO fgisDTO = fgisMap.get(dto.getContainerId());

            if (fgisDTO != null) {
                Integer usdaApproved = fgisDTO.getUsdaApproved();
                dto.setUsdaApproved(usdaApproved);
            }
        }
    }

    private Integer calculateUsdaApproved(Boolean usdaApproved) {
        if (usdaApproved == null) {
            return 0;
        } else if (usdaApproved) {
            return 1;
        } else {
            return 2;
        }
    }

    private List<PreallocationDTO> generatePreallocation(List<StockDataDTO> stockDataDTOS, Integer isEmpty, Integer isContainer) {

        List<StockDataDTO> filteredData = stockDataDTOS.stream()
                .filter(data -> data.getCatEmptyFullId().equals(isEmpty) && data.getEquipmentCategory().equals(isContainer))
                .toList();

        List<Integer> containerIds = filteredData.stream()
                .map(StockDataDTO::getContainerId)
                .distinct()
                .toList();

        List<ContainerPreassignment> containerPreassignments = containerPreassignmentRepository.findByContainerIdsAndActive(containerIds);
        Map<Integer, List<ContainerPreassignment>> containerPreassignmentsMap = containerPreassignments.stream()
                .collect(Collectors.groupingBy(preassignment -> preassignment.getContainer().getId()));

        List<Integer> bookingDetailIds = containerPreassignments.stream()
                .map(preassignment -> preassignment.getBookingDetail().getId())
                .distinct()
                .toList();
        List<CargoDocumentDetail> cargoDocumentDetails = cargoDocumentDetailRepository.findByBookingDetailIdsAndContainorIdsAndActiveTrue(bookingDetailIds, containerIds);
        Map<String, CargoDocumentDetail> cargoDocumentDetailsMap = cargoDocumentDetails.stream()
                .collect(Collectors.toMap(
                        doc -> doc.getBookingDetail().getId() + "-" + doc.getContainer().getId(),
                        doc -> doc
                ));
        List<BookingDetail> bookingDetails = bookingDetailRepository.findByBookingIdsAndActive(bookingDetailIds);
        Map<Integer, BookingDetail> bookingDetailsMap = bookingDetails.stream()
                .collect(Collectors.toMap(bd -> bd.getBooking().getId(), bd -> bd));

        List<Integer> bookingIds = bookingDetails.stream()
                .map(BookingDetail::getBooking)
                .map(Booking::getId)
                .distinct()
                .toList();

        List<Booking> bookings = bookingRepository.findByBookingIdsInAndSubBusinessUnitIds(bookingIds, filteredData.stream()
                .map(StockDataDTO::getSubBusinessUnitId)
                .distinct()
                .toList());
        Map<Integer, Booking> bookingsMap = bookings.stream()
                .collect(Collectors.toMap(Booking::getId, booking -> booking));

        List<PreallocationDTO> preallocationList = new ArrayList<>();
        for (StockDataDTO data : filteredData) {
            List<ContainerPreassignment> relatedPreassignments = containerPreassignmentsMap.get(data.getContainerId());
            if (relatedPreassignments != null) {
                for (ContainerPreassignment preassignment : relatedPreassignments) {
                    CargoDocumentDetail cargoDocumentDetail = cargoDocumentDetailsMap.get(preassignment.getBookingDetail().getId() + "-" + preassignment.getContainer().getId());
                    if (cargoDocumentDetail != null) {
                        BookingDetail bookingDetail = bookingDetailsMap.get(preassignment.getBookingDetail().getId());
                        if (bookingDetail != null) {
                            Booking booking = bookingsMap.get(bookingDetail.getBooking().getId());
                            if (booking != null) {
                                PreallocationDTO preallocationDTO = new PreallocationDTO();
                                preallocationDTO.setId(data.getId());
                                preallocationDTO.setContainerId(data.getContainerId());
                                preallocationDTO.setDocumentLoadDetailId(cargoDocumentDetail.getId());
                                preallocationDTO.setDispatched(false);
                                preallocationDTO.setBookingNumber(booking.getBookingNumber());
                                preallocationList.add(preallocationDTO);
                            }
                        }
                    }
                }
            }
        }

        return preallocationList;
    }

    private List<PreallocationDTO> processPreallocation(List<PreallocationDTO> preallocationDTOS, Integer isGateOut, Integer isEmpty) {
        List<EirDocumentCargoDetail> eirDocumentCargoDetails = eirDocumentCargoDetailRepository.findActiveEirDocumentCargoDetails(isGateOut, isEmpty);
        return removeDispatchedPreallocations(preallocationDTOS, eirDocumentCargoDetails);
    }

    private List<PreallocationDTO> removeDispatchedPreallocations(List<PreallocationDTO> preallocationDTOS, List<EirDocumentCargoDetail> eirDocumentCargoDetails) {
        Set<Integer> matchingDocumentoIds = eirDocumentCargoDetails.stream()
                .map(detail -> detail.getCargoDocumentDetail().getId())
                .collect(Collectors.toSet());
        return preallocationDTOS.stream()
                .filter(preallocationDTO -> !matchingDocumentoIds.contains(preallocationDTO.getDocumentLoadDetailId()))
                .toList();
    }

    private void updateBookingPreAllocation(List<StockDataDTO> stockDataDTOS, List<PreallocationDTO> preallocationDTOS) {
        for (StockDataDTO stockDataDTO : stockDataDTOS) {
            Optional<PreallocationDTO> matchingPreallocation = preallocationDTOS.stream()
                    .filter(preallocation -> preallocation.getId().equals(stockDataDTO.getId()))
                    .findFirst();
            if (matchingPreallocation.isPresent()) {
                PreallocationDTO preallocation = matchingPreallocation.get();
                stockDataDTO.setBookingPreAllocation(preallocation.getBookingNumber());
            }
        }
    }

    private void updateShipperData(List<StockDataDTO> stockDataList, Integer shipperName) {
        List<StockDataUpdateDTO> shipperDetail = cargoDocumentDetailRepository.fetchShipperDetail(shipperName);
        if (!CollectionUtils.isEmpty(stockDataList)) {
            stockDataList.forEach(stock -> shipperDetail.stream()
                    .filter(update -> update.getEirId().equals(stock.getEirId()))
                    .findFirst()
                    .ifPresent(update -> {
                        stock.setShipperNumber(update.getShipperNumber());
                        stock.setShipperName(update.getShipperName());
                        stock.setProductId(update.getProductId());
                    }));
        }
    }

    private List<RestrictionDTO> processRestrictions(List<StockDataDTO> stockDataDTOS) {
        List<Integer> catEmptyFullIds = stockDataDTOS.stream()
                .map(StockDataDTO::getCatEmptyFullId)
                .distinct()
                .toList();
        List<Integer> containerIds = stockDataDTOS.stream()
                .map(StockDataDTO::getContainerId)
                .distinct()
                .toList();
        List<Integer> subBusinessUnitIds = stockDataDTOS.stream()
                .map(StockDataDTO::getSubBusinessUnitId)
                .distinct()
                .toList();

        List<ContainerRestriction> restrictionContainers = containerRestrictionRepository.findActiveRestrictions(
                catEmptyFullIds, containerIds, subBusinessUnitIds);

        List<RestrictionDTO> restrictionDTOs = new ArrayList<>();
        for (ContainerRestriction container : restrictionContainers) {
            List<String> reasons = containerRestrictionDetailRepository.findActiveRestrictionReasons(container.getId());
            String concatenatedReasons = reasons.stream()
                    .sorted()
                    .collect(Collectors.joining(", "));

            RestrictionDTO dto = RestrictionDTO.builder()
                    .emptyFullCategoryId(container.getCatEmptyFull().getId())
                    .containerId(container.getContainer().getId())
                    .restrictionRemark(container.getRestrictionAnnotation() != null ? container.getRestrictionAnnotation() : "")
                    .restrictionRemark(concatenatedReasons)
                    .build();
            restrictionDTOs.add(dto);
        }

        return restrictionDTOs;
    }

    private List<IMODetailsDTO> processIMOData(List<StockDataDTO> stockDataDTOS) {
        List<Integer> programmingShipDetailIds = stockDataDTOS.stream()
                .map(StockDataDTO::getProgrammingShipDetailId)
                .distinct()
                .toList();
        List<Integer> containerIds = stockDataDTOS.stream()
                .map(StockDataDTO::getContainerId)
                .distinct()
                .toList();

        List<VesselProgrammingContainer> vesselContainers = vesselProgrammingContainerRepository
                .findActiveVesselContainers(containerIds, programmingShipDetailIds);

        List<IMODetailsDTO> imoDataDTOs = new ArrayList<>();
        for (StockDataDTO stockData : stockDataDTOS) {
            VesselProgrammingContainer container = vesselContainers.stream()
                    .filter(vc -> vc.getContainer().getId().equals(stockData.getContainerId()) &&
                            vc.getVesselProgrammingDetail().getId().equals(stockData.getProgrammingShipDetailId()))
                    .findFirst()
                    .orElse(null);

            if (container != null) {
                List<String> imoCodes = vesselProgrammingContainerImoRepository.findActiveIMOs(container.getId());
                String concatenatedIMO = String.join(", ", imoCodes);
                imoDataDTOs.add(IMODetailsDTO.builder()
                        .eirId(stockData.getEirId())
                        .containerId(container.getContainer().getId())
                        .schedulingDetailId(container.getVesselProgrammingDetail().getId())
                        .imoValue(concatenatedIMO)
                        .build());
            }
        }

        return imoDataDTOs;
    }

    private List<StockDataDTO> processStockData(Integer subBusinessUnitId, Integer chassisOwnerId,
                                                Integer chassisTypeId, String equipmentNumber,
                                                Integer shipperName, String nonMaerskFlag,
                                                Integer isChassis) {
        List<Object[]> results = stockChassisRepository.fetchStockData(subBusinessUnitId, chassisOwnerId,
                chassisTypeId, equipmentNumber, shipperName, nonMaerskFlag, isChassis);
        return results.stream()
                .map(this::buildStockDataDTO)
                .toList();
    }

    private void updateStatusInspection(List<StockDataDTO> stockDataDTOS,
                                        Integer isInspectionFinished, Integer isInspectionStarted, Integer isInspectionPending,
                                        Integer isChassisActivityInsp, Integer isChassis) {

        for (StockDataDTO stockData : stockDataDTOS) {
            if (stockData.getEquipmentCategory().equals(isChassis)) {
                List<EirChassisZoneActivity> chassisActivities =
                        eirChassisZoneActivityRepository.fetchActiveChassisActivitiesByZoneActivityId(isChassisActivityInsp, stockData.getEirChassisId());

                if (!chassisActivities.isEmpty()) {
                    EirChassisZoneActivity matchingChassisActivity = chassisActivities.getFirst();

                    Integer statusInspection = determineStatusInspection(matchingChassisActivity,
                            isInspectionFinished, isInspectionStarted, isInspectionPending);

                    stockData.setInspectionStatus(statusInspection);
                }
            }
        }
    }

    private Integer determineStatusInspection(EirChassisZoneActivity chassisActivity, Integer isInspectionFinished,
                                              Integer isInspectionStarted, Integer isInspectionPending) {

        if (chassisActivity.getCompleted() != null && chassisActivity.getCompleted().equals(Boolean.TRUE)) {
            return isInspectionFinished;
        } else if (chassisActivity.getIsPartialInspection() != null && chassisActivity.getIsPartialInspection().equals(Boolean.TRUE)) {
            return isInspectionStarted;
        } else {
            return isInspectionPending;
        }
    }

    private void updateChassisEstimateStockDataDtos(List<StockDataDTO> stockDataDTOS, Integer isChassis) {
        if (stockDataDTOS == null || stockDataDTOS.isEmpty()) {
            return;
        }

        List<StockDataDTO> filteredDtos = stockDataDTOS.stream()
                .filter(dto -> Objects.equals(dto.getEquipmentCategory(), isChassis))
                .toList();

        if (filteredDtos.isEmpty()) {
            return;
        }
        Set<Integer> eirChassisIds = filteredDtos.stream()
                .map(StockDataDTO::getEirChassisId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (eirChassisIds.isEmpty()) {
            return;
        }
        List<ChassisEstimate> chassisEstimates = chassisEstimateRepository.findLatestChassisEstimates(eirChassisIds);
        Map<Integer, ChassisEstimate> estimateMap = chassisEstimates.stream()
                .collect(Collectors.toMap(chassisEstimate -> chassisEstimate.getEirChassis().getId(), estimate -> estimate));
        filteredDtos.forEach(dto -> {
            ChassisEstimate estimate = estimateMap.get(dto.getEirChassisId());
            if (estimate != null) {
                dto.setEstimateStructureNumber(estimate.getId());
                dto.setEstimateStructureStatus(estimate.getCatChaestimStatus().getId());
            }
        });
    }

    private List<ChassisRestrictionDTO> processChassisRestrictions(
            List<StockDataDTO> stockDataDTOS,
            Integer isChassis) {

        if (stockDataDTOS == null || stockDataDTOS.isEmpty()) {
            return Collections.emptyList();
        }

        Set<Integer> chassisIds = stockDataDTOS.stream()
                .filter(dto -> Objects.equals(dto.getEquipmentCategory(), isChassis))
                .map(StockDataDTO::getChassisId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Set<Integer> subBusinessUnitIds = stockDataDTOS.stream()
                .filter(dto -> Objects.equals(dto.getEquipmentCategory(), isChassis))
                .map(StockDataDTO::getSubBusinessUnitId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (chassisIds.isEmpty() || subBusinessUnitIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<Object[]> rawData = chassisRestrictionRepository.findChassisRestrictionsRawData(chassisIds, subBusinessUnitIds);
        Map<Integer, ChassisRestrictionDTO> chassisRestrictionMap = new HashMap<>();

        for (Object[] row : rawData) {
            Integer chassisId = (Integer) row[0];
            String restrictionRemark = (String) row[1];
            String reason = (String) row[2];

            ChassisRestrictionDTO dto = chassisRestrictionMap.get(chassisId);
            if (dto == null) {
                dto = ChassisRestrictionDTO.builder()
                        .chassisId(chassisId)
                        .restrictionRemark(restrictionRemark)
                        .reasons(reason)
                        .build();
                chassisRestrictionMap.put(chassisId, dto);
            } else {
                String updatedReasons = dto.getReasons() + (dto.getReasons().isEmpty() ? "" : ", ") + reason;
                dto.setReasons(updatedReasons);
            }
        }

        return new ArrayList<>(chassisRestrictionMap.values());
    }

    private void processShipperFilter(List<StockDataDTO> stockDataDTOS, Integer shipperNameId) {
        if (shipperNameId == null || shipperNameId == 0) {
            return;
        }
        String shipperSocialReason = companyRepository.findLegalNameById(shipperNameId);
        stockDataDTOS.forEach(dto -> dto.setFilter(Boolean.FALSE));

        if (shipperSocialReason == null || shipperSocialReason.trim().isEmpty()) {
            return;
        }
        stockDataDTOS.forEach(dto -> dto.setFilter(dto.getShipperName() != null &&
                dto.getShipperName().trim().toUpperCase()
                        .startsWith(shipperSocialReason.trim().toUpperCase())));

        stockDataDTOS.removeIf(dto -> !Boolean.TRUE.equals(dto.getFilter()));
    }

    private List<StockInventoryReportOutputDTO.StockInventory> processReport(
            List<StockDataDTO> stockDataDTOS, Integer isContainer, Integer languageId, Integer isChassis,
            Integer subBusinessUnitId, String dateTimeFormat, Integer isEmpty, Integer isFull,
            List<RestrictionDTO> restrictionDTOS, List<ChassisRestrictionDTO> chassisRestrictionDTOS,
            List<IMODetailsDTO> imoDetailsList) {
        List<StockInventoryReportOutputDTO.StockInventory> dataList = new ArrayList<>();
        for (StockDataDTO stockData : stockDataDTOS) {
            StockInventoryReportOutputDTO.StockInventory data = StockInventoryReportOutputDTO.StockInventory.builder()
                    .depot(stockData.getLocal())
                    .emptyFull(getEmptyFull(stockData, languageId, isContainer))
                    .equipmentNumber(stockData.getEquipmentNumber())
                    .equipmentCategory(
                            Optional.ofNullable(catalogLanguageRepository
                                    .fnCatalogTranslationDescLong(stockData.getEquipmentCategory(),
                                            languageId)).orElse(""))
                    .equipmentSizeType(getEquipmentSizeType(stockData, isContainer, isChassis, languageId))
                    .equipmentGrade(getEquipmentGrade(stockData, isContainer))
                    .manufactureYear((stockData.getManufactureDateContainer() != null
                            ? String.valueOf(stockData.getManufactureDateContainer().getYear()) : null))
                    .isoCode(getIsoCode(stockData))
                    .shippingLineOrChassisOwner(getShippingLineChassisOwner(stockData, isContainer, isChassis))
                    .shipperName(Optional.ofNullable(stockData.getShipperName()).orElse(""))
                    .commodity(getProductName(stockData))
                    .gateInDate(getGateInDate(stockData, subBusinessUnitId, dateTimeFormat))
                    .dwellTime(calculateDwellTime(stockData))
                    .structureCondition(Optional.ofNullable(catalogLanguageRepository
                                    .fnCatalogTranslationDescLong(stockData.getStructureConditionId(), languageId))
                            .orElse(""))
                    .machineryCondition(Optional.ofNullable(catalogLanguageRepository
                                    .fnCatalogTranslationDescLong(stockData.getMachineryConditionId(), languageId))
                            .orElse(""))
                    .equipmentRestriction(getEquipmentRestriction(stockData, isContainer, isEmpty, isFull, restrictionDTOS, chassisRestrictionDTOS))
                    .usdaApproved(getUSDAApprovedStatus(stockData.getUsdaApproved()))
                    .imoInformation(getIMODetailsValue(stockData, isContainer, imoDetailsList))
                    .gateInEirContainer(stockData.getEirId() != null ? String.format("%01d", stockData.getEirId()) : "")
                    .gateInEirChassis(stockData.getEirChassisId() != null ? String.format("%01d", stockData.getEirChassisId()) : "")
                    .chassisStayed(getGateInChassisStayed(stockData, isChassis))
                    .inspectionStatus(Optional.ofNullable(catalogLanguageRepository
                                    .fnCatalogTranslationDesc(stockData.getInspectionStatus(), languageId))
                            .orElse(""))
                    .structureConditionInspection(Optional.ofNullable(catalogLanguageRepository
                                    .fnCatalogTranslationDescLong(stockData.getStructureConditionInspectionId(), languageId))
                            .orElse(""))
                    .machineryConditionInspection(Optional.ofNullable(catalogLanguageRepository
                                    .fnCatalogTranslationDescLong(stockData.getMachineryConditionInspectionId(), languageId))
                            .orElse(""))
                    .gateInComment(Optional.ofNullable(stockData.getGinComment()).orElse(""))
                    .structureInspectionEmptyContainerComment(Optional.ofNullable(stockData.getInspectorComment()).orElse(""))
                    .bookingPreAllocation(Optional.ofNullable(stockData.getBookingPreAllocation()).orElse(""))
                    .potentialFGIS((stockData.getPotentialFoodAid() != null && stockData.getPotentialFoodAid().equals(Boolean.TRUE)) ? "Yes" : "")
                    .yardLocation(containerLocationRepository.getContainerLocation(stockData.getContainerId()))
                    .estimateStructureStatus(Optional.ofNullable(catalogLanguageRepository
                                    .findDescriptionByCatalogIdAndLanguageId(stockData.getEstimateStructureStatus(), languageId))
                            .orElse(""))
                    .estimateMachineryStatus(Optional.ofNullable(catalogLanguageRepository
                                    .findDescriptionByCatalogIdAndLanguageId(stockData.getEstimateMachineStatus(), languageId))
                            .orElse(""))
                    .nonMaerskFlag(getNoMaerskFlagString(stockData.getNoMaerskFlag()))
                    .build();
            dataList.add(data);
        }
        return sortByEquipmentCategoryAndEquipmentNumber(dataList);
    }

    private String getEmptyFull(StockDataDTO stockData, Integer languageId, Integer isContainer) {
        if (stockData.getEquipmentCategory().equals(isContainer)) {
            return Optional.ofNullable(catalogLanguageRepository.fnCatalogTranslationDescLong(stockData.getCatEmptyFullId(), languageId)).orElse("");
        }
        return "";
    }

    private String getEquipmentSizeType(StockDataDTO data, Integer isContainer, Integer isChassis, Integer languageId) {
        if (data.getEquipmentCategory().equals(isContainer)) {
            return catalogRepository.findDescriptionByCatalogId(data.getEquipmentSizeId()) + " " +
                    catalogRepository.findDescriptionByCatalogId(data.getEquipmentTypeId());
        } else if (data.getEquipmentCategory().equals(isChassis)) {
            return catalogLanguageRepository.fnCatalogTranslationDescLong(data.getEquipmentTypeId(), languageId);
        } else {
            return "";
        }
    }

    private String getEquipmentGrade(StockDataDTO data, Integer isContainer) {
        String description = "";
        if (data.getEquipmentCategory().equals(isContainer)) {
            description = catalogRepository.findDescriptionByCatalogId(data.getEquipmentClassId());
        }
        return Objects.nonNull(description) ? description : "";
    }

    private String getIsoCode(StockDataDTO data) {
        if (StringUtils.isBlank(data.getIsoCode())) {
            return "";
        }

        Integer isoCodeId = Integer.valueOf(data.getIsoCode());
        String isoCode = isoCodeRepository.findCodeByIsoCodeId(isoCodeId);
        return isoCode != null ? isoCode : "";
    }

    private String getShippingLineChassisOwner(StockDataDTO data, Integer isContainer, Integer isChassis) {
        if (data.getEquipmentCategory().equals(isContainer)) {
            return Optional.ofNullable(shippingLineRepository.getShippingNameById(data.getShippingLineId()))
                    .orElse("");
        } else if (data.getEquipmentCategory().equals(isChassis)) {
            return Optional.ofNullable(companyRepository.findLegalNameById(data.getChassisOwnerCompanyId()))
                    .orElse("");
        } else {
            return "";
        }
    }

    private String getProductName(StockDataDTO data) {
        return Optional.ofNullable(productRepository.findProductNameByProductId(data.getProductId()))
                .orElse("");
    }

    private String getGateInDate(StockDataDTO data, Integer subBusinessUnitId, String dateTimeFormat) {
        LocalDateTime gateInDate = businessUnitConfigService.getDateTime(subBusinessUnitId, data.getTruckEntryDate());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateTimeFormat);
        return gateInDate.format(formatter);
    }

    private int calculateDwellTime(StockDataDTO data) {
        LocalDate currentDate = LocalDate.now();
        LocalDate entryDate = data.getTruckEntryDate().toLocalDate();
        return Math.toIntExact(ChronoUnit.DAYS.between(entryDate, currentDate)) + 1;
    }

    private String getEquipmentRestriction(StockDataDTO data, Integer isContainer, Integer isEmpty, Integer isFull,
                                           List<RestrictionDTO> restrictionDTOS, List<ChassisRestrictionDTO> chassisRestrictionDTOS) {
        if (data.getEquipmentCategory().equals(isContainer)) {
            String emptyRestriction = restrictionDTOS.stream()
                    .filter(restriction ->
                            restriction.getContainerId().equals(data.getContainerId()) &&
                                    restriction.getEmptyFullCategoryId().equals(isEmpty))
                    .map(restriction -> "[Empty]" + restriction.getRestrictionRemark() + ": " + restriction.getReasons() + " ")
                    .findFirst()
                    .orElse("");

            String fullRestriction = restrictionDTOS.stream()
                    .filter(restriction ->
                            restriction.getContainerId().equals(data.getContainerId()) &&
                                    restriction.getEmptyFullCategoryId().equals(isFull))
                    .map(restriction -> "[Full]" + restriction.getRestrictionRemark() + ": " + restriction.getReasons() + " ")
                    .findFirst()
                    .orElse("");

            return emptyRestriction + fullRestriction;

        } else {
            return chassisRestrictionDTOS.stream()
                    .filter(restriction -> restriction.getChassisId().equals(data.getChassisId()))
                    .map(restriction -> "[Chassis] " + restriction.getRestrictionRemark() + ": " + restriction.getReasons() + " ")
                    .findFirst()
                    .orElse("");
        }
    }

    private String getUSDAApprovedStatus(Integer usdaApproved) {
        if (usdaApproved.equals(1)) {
            return "Yes";
        } else if (usdaApproved.equals(2)) {
            return "No";
        } else {
            return "";
        }
    }

    private String getIMODetailsValue(StockDataDTO stockData,
                                      Integer isContainer,
                                      List<IMODetailsDTO> imoDetailsList) {

        if (stockData.getEquipmentCategory().equals(isContainer) && !CollectionUtils.isEmpty(imoDetailsList)) {
            return imoDetailsList.stream()
                    .filter(imo ->
                            Objects.equals(imo.getEirId(), stockData.getEirId()) &&
                                    Objects.equals(imo.getContainerId(), stockData.getContainerId()) &&
                                    Objects.equals(imo.getSchedulingDetailId(), stockData.getProgrammingShipDetailId()))
                    .map(IMODetailsDTO::getImoValue)
                    .findFirst()
                    .orElse("");
        }
        return "";
    }

    private String getGateInChassisStayed(StockDataDTO stockData, Integer isChassis) {
        Boolean chassisStayedFlag = stockData.getChassisStayedFlag();
        return (chassisStayedFlag != null && chassisStayedFlag && stockData.getEquipmentCategory().equals(isChassis)) ? "Y" : "";
    }

    private String getNoMaerskFlagString(Boolean flag) {
        if (flag == null) {
            return "";
        } else if (flag) {
            return "Yes";
        } else {
            return "No";
        }
    }

    private List<StockInventoryReportOutputDTO.StockInventory> sortByEquipmentCategoryAndEquipmentNumber(List<StockInventoryReportOutputDTO.StockInventory> dataList) {
        dataList.sort(Comparator
                .comparing(StockInventoryReportOutputDTO.StockInventory::getEquipmentCategory, Comparator.nullsLast(String::compareTo))
                .thenComparing(StockInventoryReportOutputDTO.StockInventory::getEquipmentNumber, Comparator.nullsLast(String::compareTo)));
        return dataList;
    }

    private List<StockInventoryReportOutputDTO.StockInventory> getPaginatedResult(List<StockInventoryReportOutputDTO.StockInventory> dataList, Integer pageNo,
                                                                                  Integer noOfRecordsPerPage, Integer totalRecord) {
        if (pageNo == null) {
            pageNo = 1;
        }
        if (noOfRecordsPerPage == null) {
            noOfRecordsPerPage = (totalRecord == 0 ? 1 : totalRecord);
        }
        long offset = (long) (pageNo - 1) * (long) noOfRecordsPerPage;

        return dataList.stream()
                .skip(offset)
                .limit(noOfRecordsPerPage)
                .toList();
    }

    private StockDataDTO buildStockDataDTO(Object[] result) {
        String seals;
        if (result[20] == null) {
            seals = "";
        } else if (result[20] instanceof String) {
            seals = (String) result[20];
        } else if (result[20] instanceof Character) {
            seals = String.valueOf(result[20]);
        } else {
            seals = "";
        }

        Integer shippingLineId;
        if (result[17] != null) {
            if (result[17] instanceof BigDecimal) {
                shippingLineId = ((BigDecimal) result[17]).intValue();
            } else if (result[17] instanceof Integer) {
                shippingLineId = (Integer) result[17];
            } else {
                shippingLineId = null;
            }
        } else {
            shippingLineId = null;
        }

        String isoCode;
        if (result[16] != null) {
            if (result[16] instanceof String) {
                isoCode = (String) result[16];
            } else if (result[16] instanceof Integer) {
                isoCode = String.valueOf(result[16]);
            } else {
                isoCode = "";
            }
        } else {
            isoCode = "";
        }

        return StockDataDTO.builder()
                .id(result[0] != null ? ((Integer) result[0]) : null)
                .businessUnitId(result[1] != null ? ((BigDecimal) result[1]).intValue() : null)
                .subBusinessUnitId(result[2] != null ? ((BigDecimal) result[2]).intValue() : null)
                .programmingShipDetailId(result[3] != null ? ((Integer) result[3]) : null)
                .containerId(result[4] != null ? ((Integer) result[4]) : null)
                .chassisId(result[5] != null ? ((Integer) result[5]) : null)
                .catEmptyFullId(result[6] != null ? ((BigDecimal) result[6]).intValue() : null)
                .local(result[7] != null ? (String) result[7] : "")
                .eirId(result[8] != null ? ((Integer) result[8]) : null)
                .truckEntryDate(result[9] != null ? toLocalDateTime(result[9]) : null)
                .truckExitDate(result[10] != null ? toLocalDateTime(result[10]) : null)
                .equipmentNumber(result[11] != null ? (String) result[11] : "")
                .equipmentSizeId(result[12] != null ? ((BigDecimal) result[12]).intValue() : null)
                .equipmentTypeId(result[13] != null ? ((BigDecimal) result[13]).intValue() : null)
                .equipmentCategory(result[14] != null ? ((Integer) result[14]) : null)
                .equipmentClassId(result[15] != null ? ((BigDecimal) result[15]).intValue() : null)
                .isoCode(isoCode)
                .shippingLineId(shippingLineId)
                .chassisOwnerCompanyId(result[18] != null ? ((BigDecimal) result[18]).intValue() : null)
                .registrationDate(result[19] != null ? toLocalDateTime(result[19]) : null)
                .seals(seals)
                .observation(result[21] != null ? (String) result[21] : "")
                .cargoDocumentTypeId(result[22] != null ? ((BigDecimal) result[22]).intValue() : null)
                .cargoDocument(result[23] != null ? (String) result[23] : "")
                .shipperNumber(result[24] != null ? (String) result[24] : "")
                .shipperName(result[25] != null ? (String) result[25] : "")
                .consigneeNumber(result[26] != null ? (String) result[26] : "")
                .consigneeName(result[27] != null ? (String) result[27] : "")
                .operationType(result[28] != null ? ((BigDecimal) result[28]).intValue() : null)
                .productId(result[29] != null ? ((Integer) result[29]) : null)
                .eirChassisId(result[30] != null ? ((Integer) result[30]) : null)
                .structureConditionId(result[31] != null ? ((BigDecimal) result[31]).intValue() : null)
                .machineryConditionId(result[32] != null ? ((BigDecimal) result[32]).intValue() : null)
                .chassisStayedFlag(result[33] != null ? (Boolean) result[33] : null)
                .filter(result[34] != null && ((Integer) result[34]) == 1)
                .inspectorComment(result[35] != null ? (String) result[35] : "")
                .potentialFoodAid(result[36] != null && (result[36] instanceof Boolean ? (Boolean) result[36] : result[36] instanceof Integer && (Integer) result[36] == 1))
                .ginComment(result[37] != null ? (String) result[37] : "")
                .usdaApproved(result[38] != null ? ((Integer) result[38]) : null)
                .structureConditionInspectionId(result[39] != null ? ((BigDecimal) result[39]).intValue() : null)
                .machineryConditionInspectionId(result[40] != null ? ((BigDecimal) result[40]).intValue() : null)
                .bookingPreAllocation(result[41] != null ? (String) result[41] : "")
                .inspectionStatus(result[42] != null ? ((BigDecimal) result[42]).intValue() : null)
                .estimateStructureNumber(result[43] != null ? ((Integer) result[43]) : null)
                .estimateStructureStatus(result[44] != null ? ((BigDecimal) result[44]).intValue() : null)
                .estimateMachineNumber(result[45] != null ? ((Integer) result[45]) : null)
                .estimateMachineStatus(result[46] != null ? ((BigDecimal) result[46]).intValue() : null)
                .noMaerskFlag(result[47] != null ? (Boolean) result[47] : null)
                .manufactureDateContainer(result[48] != null ? toLocalDateTime(result[48]) : null)
                .build();
    }

    private LocalDateTime toLocalDateTime(Object timestamp) {
        if (timestamp instanceof java.sql.Timestamp ts) {
            return ts.toLocalDateTime();
        }
        return null;
    }

}
