package com.maersk.sd1.sdg.service;

import com.maersk.sd1.common.Constants;
import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.CargoDocumentDetailRepository;
import com.maersk.sd1.common.repository.CargoDocumentRepository;
import com.maersk.sd1.common.repository.EirDocumentCargoDetailRepository;
import com.maersk.sd1.common.repository.EirRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.sdg.controller.dto.ValidateDocumentAvailabilityOutput;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import java.util.Objects;

@Service
@RequiredArgsConstructor
public class ValidateDocumentAvailabilityService {

    private final CatalogRepository catalogRepository;


    private final CargoDocumentDetailRepository cargoDocumentDetailRepository;


    private final CargoDocumentRepository cargoDocumentRepository;


    private final EirRepository eirRepository;


    private final EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;


    private final MessageLanguageRepository messageLanguageRepository;

    //[sdg].[validate_document_availability] procedure
    public ValidateDocumentAvailabilityOutput validateDocumentAvailability(Integer catEmptyFullId,
                                                                           Integer documentoCargaId, Integer languajeId) {

        Integer isGateOut = catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS);
        Integer isFull = catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS);

        ValidateDocumentAvailabilityOutput output = new ValidateDocumentAvailabilityOutput();
        String message = "";

        if (documentoCargaId != null && Objects.equals(catEmptyFullId, isFull)) {
            String documentNumber = cargoDocumentRepository.findCargoDocumentByCargoDocumentId(documentoCargaId);

            Integer qCntsReceived = cargoDocumentDetailRepository.containersReceivedQuantitiesOrWeights(documentoCargaId);
            Integer qPendingAssignments = eirRepository.countPendingAssignments(isFull, isGateOut, documentoCargaId);
            Integer qAssignedCnts = eirDocumentCargoDetailRepository.countAssignedContainers(documentoCargaId, isFull, isGateOut);


            if ((qPendingAssignments + qAssignedCnts) < qCntsReceived) {
                message = ""; // It's ok, continue
            } else {
                String respMessage = messageLanguageRepository.fnTranslatedMessage(Constants.PRC_FULL_GI_GO, 27, languajeId);
                message = translateMessage(respMessage, documentNumber);
            }
        }
        output.setMessage(message);
        return output;
    }

    private String translateMessage(String message, String documentNumber) {
        String docs = "{DOCX}";
        return message.replace(docs, documentNumber.toUpperCase());
    }
}