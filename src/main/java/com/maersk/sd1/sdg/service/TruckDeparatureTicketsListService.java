package com.maersk.sd1.sdg.service;

import com.maersk.sd1.common.repository.AzureStorageConfigRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.EirNotificationRepository;
import com.maersk.sd1.sdg.controller.dto.TruckDeparturePrintTicketOutput;
import com.maersk.sd1.sdg.dto.NotificationTicket;
import com.maersk.sd1.sdg.controller.dto.TruckDeparturePrintTicketInput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class TruckDeparatureTicketsListService {
    private static final Logger logger = LogManager.getLogger(TruckDeparatureTicketsListService.class);

    private static final String PENDING_ALIAS = "sd1_eir_notification_pending";
    private static final String PROCESSING_ALIAS = "sd1_eir_notification_processing";
    private static final String AZURE_SDG_TRUCK_DEPARTURE_TICKET = "azure_sdg_truck_departure_ticket";
    private static final Integer USER_REGISTER_ID = 1;

    private final EirNotificationRepository eirNotificationRepository;
    private final AzureStorageConfigRepository azureStorageConfigRepository;
    private final TruckDeparturePrintTicketService truckDepartureTicketPrintService;
    private final CatalogRepository catalogRepository;

    @Autowired
    public TruckDeparatureTicketsListService(EirNotificationRepository eirNotificationRepository,
                                             AzureStorageConfigRepository azureStorageConfigRepository,
                                             TruckDeparturePrintTicketService truckDepartureTicketPrintService,
                                             CatalogRepository catalogRepository) {
        this.eirNotificationRepository = eirNotificationRepository;
        this.azureStorageConfigRepository = azureStorageConfigRepository;
        this.truckDepartureTicketPrintService = truckDepartureTicketPrintService;
        this.catalogRepository = catalogRepository;
    }

    public List<NotificationTicket> processTruckDepartureTickets() {
        logger.info("Starting processTruckDepartureTickets");
        logger.info("USER_REGISTER_ID: {}", USER_REGISTER_ID);

        Integer pendingCatalogId = catalogRepository.findIdByAlias(PENDING_ALIAS);
        Integer processingCatalogId = catalogRepository.findIdByAlias(PROCESSING_ALIAS);
        Integer azureSdgTruckDepartureTicketCatalogId = azureStorageConfigRepository.findAzureStorageIdById(AZURE_SDG_TRUCK_DEPARTURE_TICKET);

        if (azureSdgTruckDepartureTicketCatalogId == null || pendingCatalogId == null || processingCatalogId == null) {
            logger.warn("One or more required catalog IDs are null");
            return new ArrayList<>();
        }

        Pageable pageable = PageRequest.of(0, 10);
        Page<Object[]> pagedata = eirNotificationRepository.findTop10ByStatusId(pendingCatalogId, pageable);
        List<Object[]> notifications = pagedata.getContent();

        if (notifications.isEmpty()) {
            logger.info("No notifications found with pending status");
            return new ArrayList<>();
        }

        List<NotificationTicket> tempData = createTempData(notifications);

        for (NotificationTicket ticket : tempData) {
            Integer eirId = ticket.getEirId();
            logger.info("Processing ticket for EIR ID: {}", eirId);

            TruckDeparturePrintTicketInput.Input input = new TruckDeparturePrintTicketInput.Input();
            input.setEirId(eirId);

            TruckDeparturePrintTicketInput.Prefix prefix = new TruckDeparturePrintTicketInput.Prefix();
            prefix.setInput(input);

            TruckDeparturePrintTicketInput.Root root = new TruckDeparturePrintTicketInput.Root();
            root.setSdg(prefix);

            try {
                TruckDeparturePrintTicketOutput ticketContent = truckDepartureTicketPrintService.getTruckDeparturePrintTicket(root);
                ticket.setTicketContent(ticketContent.getMessage());
                ticket.setAzureStorageConfigId(processingCatalogId);
                logger.info("Successfully processed ticket for EIR ID: {}", eirId);
            } catch (Exception ex) {
                logger.error("Error processing ticket for EIR ID: {}", eirId, ex);
            }
        }

        logger.info("Finished processTruckDepartureTickets");
        return tempData;
    }

    private List<NotificationTicket> createTempData(List<Object[]> notifications) {
        List<NotificationTicket> tempData = new ArrayList<>();
        for (Object[] notification : notifications) {
            Integer eirId = (Integer) notification[0];
            Integer notificationId = (Integer) notification[1];
            NotificationTicket ticket = new NotificationTicket(eirId, notificationId);
            tempData.add(ticket);
        }
        return tempData;
    }
}