package com.maersk.sd1.sdg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

public class GateInGeneralRegisterV2Input {

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Root {
        @JsonProperty("SDG")
        private GateInGeneralRegisterV2Input.Prefix prefix;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Prefix {
        @JsonProperty("F")
        private GateInGeneralRegisterV2Input.Input input;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Input {
        @JsonProperty("sub_business_unit_local_id")
        private Integer subBusinessUnitLocalId;

        @JsonProperty("equipment_cat_empty_full_id")
        @With
        private Integer equipmentCatEmptyFullId;

        @JsonProperty("equipment")
        @With private String equipment;

        @JsonProperty("move_type_id")
        private Integer moveTypeId;

        @JsonProperty("chassis")
        private String chassis;

        @JsonProperty("driver_id")
        private Integer driverId;

        @JsonProperty("vehicle_id")
        private Integer vehicleId;

        @JsonProperty("truck_company_id")
        private Integer truckCompanyId;

        @JsonProperty("comments")
        private String comments;

        @JsonProperty("user_registration_id")
        private Integer userRegistrationId;

        @JsonProperty("languaje_id")
        private Integer languageId;

        @JsonProperty("system_rule_id")
        private String systemRuleId;

        @JsonProperty("type_process")
        private String typeProcess;

        @JsonProperty("operation_code")
        private String operationCode;

        @JsonProperty("input_chassis_number")
        private String inputChassisNumber;

        @JsonProperty("flag_chassis_stayed")
        private Boolean flagChassisStayed;

        @JsonProperty("inspection_situ")
        private Boolean inspectionSitu;

        @JsonProperty("twr_number")
        private String twrNumber;

        public void setFlagChassisStayed(String flagChassisStayed) {
            this.flagChassisStayed = "1".equals(flagChassisStayed);
        }

        public void setInspectionSitu(String inspectionSitu) {
            this.inspectionSitu = "1".equals(inspectionSitu);
        }

        public static Input generateDummyGateInInput(GateOutGeneralRegisterInput.Input gateOutInput) {
            return GateInGeneralRegisterV2Input.Input.builder()
                    .chassis(gateOutInput.getChassis())
                    .comments(gateOutInput.getComments())
                    .driverId(gateOutInput.getDriverId())
                    .equipment("[]")
                    .equipmentCatEmptyFullId(gateOutInput.getEquipmentCatEmptyFullId())
                    .flagChassisStayed(true)
                    .inputChassisNumber(gateOutInput.getInputChassisNumber())
                    .inspectionSitu(null)
                    .languageId(gateOutInput.getLanguageId())
                    .moveTypeId(gateOutInput.getMoveTypeId())
                    .operationCode(null)
                    .systemRuleId("sd1_rule_integration_sdy")
                    .vehicleId(gateOutInput.getVehicleId())
                    .truckCompanyId(gateOutInput.getTruckCompanyId())
                    .typeProcess("gateout")
                    .twrNumber(gateOutInput.getTwrNumber())
                    .subBusinessUnitLocalId(gateOutInput.getSubBusinessUnitLocalId())
                    .userRegistrationId(gateOutInput.getUserRegistrationId())
                    .build();
        }
    }
}
