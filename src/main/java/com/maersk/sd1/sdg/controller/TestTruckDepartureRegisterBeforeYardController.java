package com.maersk.sd1.sdg.controller;
import com.maersk.sd1.sdg.controller.dto.ResponseTruckDepartureRegisterBeforeYard;
import com.maersk.sd1.sdg.controller.dto.SdgTruckDepartureRegisterBeforeYardInput;
import com.maersk.sd1.sdg.service.TruckDepartureRegisterBeforeYardService;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/ModuleSDG/module/sdg/testTruckDepartureRegisterBeforeYardController")
public class TestTruckDepartureRegisterBeforeYardController {

    private final TruckDepartureRegisterBeforeYardService service;

    @PostMapping
    public ResponseEntity<ResponseTruckDepartureRegisterBeforeYard> truckDepartureBeforeYard(@RequestBody SdgTruckDepartureRegisterBeforeYardInput.Root truckDepartureBeforeYard) throws Exception {
        return ResponseEntity.ok(service.registerBeforeYard(truckDepartureBeforeYard));
    }


}
