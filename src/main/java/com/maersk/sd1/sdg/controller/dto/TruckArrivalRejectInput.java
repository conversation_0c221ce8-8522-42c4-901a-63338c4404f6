package com.maersk.sd1.sdg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
public class TruckArrivalRejectInput {

    @Data
    public static class Input {

        @JsonProperty("sub_unidad_negocio_id")
        private Integer subUnidadNegocioId;

        @JsonProperty("idioma_id")
        private Integer pfIdiomaId;

        @JsonProperty("container_number")
        private String containerNumber;

        @JsonProperty("chassis_number")
        private String chassisNumber;

        @JsonProperty("type_movement")
        private String typeMovement;

        @JsonProperty("empty_full_process")
        private String emptyFullProcess;

        @JsonProperty("trucking_company")
        private String truckingCompany;

        @JsonProperty("driver_name")
        private String driverName;

        @JsonProperty("date_registration_desde")
        private Date dateRegistrationDesde;

        @JsonProperty("date_registration_hasta")
        private Date dateRegistrationHasta;

        @JsonProperty("pagina")
        private Integer pagina;

        @JsonProperty("cantidad")
        private Integer cantidad;

    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDG")
        private Prefix prefix;
    }
}
