package com.maersk.sd1.sdg.controller.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.maersk.sd1.common.serializer.GateInSearchChassisSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public class GateInSearchChassisOutput {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private Integer resultState;
        private String resultMessage;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Response{
        private Integer id;
        private Integer customerCompayId;
        private String customerCompany;
        private Integer chassisId;
        private String chassisNumber;
        private Integer catChassisTypeId;
        private String catChassisType;
        private String referenceDocument;
        private Integer documentChassisDetailId;
        private String dateEta;
        private Integer transportCompanyId;
        private String transportCompany;
        private Integer documentChassisId;
        private String chassisOperationType;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Chassis{
        private Integer chassisType;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonSerialize(using = GateInSearchChassisSerializer.class)
    public static class Output {
        private Result result;
        private List<Response> response;
        private Chassis chassis;
    }
}
