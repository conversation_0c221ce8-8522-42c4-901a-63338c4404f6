package com.maersk.sd1.sdg.controller.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;


public class TruckDepartureTicketListOutput {
    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Ticket {
        @JsonProperty("RowID")
        private Integer rowId;

        @JsonProperty("eir_id")
        private Integer eirId;

        @JsonProperty("eir_notification_id")
        private Integer eirNotificationId;

        @JsonProperty("ticket_content")
        private String ticketContent;

        @JsonProperty("azure_storage_config_id")
        private Integer azureStorageConfigId;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Root {
        @JsonProperty("tickets")
        private List<TruckDepartureTicketListOutput.Ticket> tickets;
    }
}
